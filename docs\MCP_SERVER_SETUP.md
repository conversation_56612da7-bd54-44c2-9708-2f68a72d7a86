# Leantime MCP Server - Secure Multi-Computer Project Management

## Overview

This MCP (Model Context Protocol) server transforms your Leantime instance into a centralized AI-powered project management hub that all your computers can connect to. It implements Microsoft's security best practices and uses OpenRouter for AI capabilities with self-organizing features.

## Key Features

### 🔐 **Security First**
- JWT-based authentication for all clients
- Encrypted data transmission
- Client whitelisting
- Audit logging
- Non-root Docker containers
- Read-only filesystems

### 🤖 **Self-Organizing AI**
- **Auto-organize tasks** - AI prioritizes and organizes your backlog
- **Smart assignment** - Automatically assigns tasks to best team member
- **Health monitoring** - Real-time project health analysis
- **Report generation** - Automated status reports for different audiences
- **Sprint optimization** - AI-powered sprint planning

### 💻 **Multi-Computer Access**
- Connect from any computer with Claude Desktop
- Secure authentication tokens
- Centralized task management
- Real-time synchronization
- Offline capability with sync

## Architecture

```
┌──────────────────────────────────────────────┐
│           Your Computers                      │
├────────────┬────────────┬────────────────────┤
│  Desktop   │   Laptop   │   Work Machine     │
│  Claude    │   Claude   │    Claude          │
└─────┬──────┴─────┬──────┴─────┬──────────────┘
      │            │            │
      └────────────┼────────────┘
                   │ HTTPS/TLS
                   ▼
        ┌─────────────────────┐
        │   MCP TLS Proxy     │
        │   (nginx:443)       │
        └──────────┬──────────┘
                   │
        ┌──────────▼──────────┐
        │  MCP Server Gateway │
        │  - Authentication   │
        │  - Authorization    │
        │  - AI Processing    │
        │  - Self-Organizing  │
        └──────────┬──────────┘
                   │
        ┌──────────▼──────────┐
        │   Leantime MySQL    │
        │   Your Project Data │
        └─────────────────────┘
```

## Installation

### Prerequisites
- Docker and Docker Compose installed
- Leantime already running in Docker
- OpenRouter API key (get from https://openrouter.ai)
- SSL certificates (or use self-signed for dev)

### Step 1: Set Environment Variables

Add to your `.env` file:

```bash
# OpenRouter AI (Required)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Security Keys (Generate unique ones!)
MCP_SECRET_KEY=$(openssl rand -base64 32)
MCP_JWT_SECRET=$(openssl rand -hex 32)
MCP_REGISTRY_PASSWORD=$(openssl rand -base64 24)

# Allowed Clients (optional - leave empty to allow all)
MCP_ALLOWED_CLIENTS=desktop_12345,laptop_67890

# Public Port
MCP_PUBLIC_PORT=8443

# Self-Organizing Features
MCP_AUTO_ORGANIZE=true
MCP_AUTO_ASSIGN=true
MCP_AUTO_PRIORITIZE=true
```

### Step 2: Generate SSL Certificates

For production, use real certificates. For development:

```bash
mkdir -p mcp-server/certs
cd mcp-server/certs

# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem \
  -days 365 -nodes -subj "/CN=leantime-mcp"

cd ../..
```

### Step 3: Create Nginx Configuration

```bash
cat > mcp-server/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream mcp_backend {
        server mcp-gateway:8000;
    }

    server {
        listen 443 ssl http2;
        server_name _;

        ssl_certificate /etc/nginx/certs/cert.pem;
        ssl_certificate_key /etc/nginx/certs/key.pem;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        location /health {
            return 200 "OK";
        }
        
        location /mcp {
            proxy_pass http://mcp_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
EOF
```

### Step 4: Deploy MCP Server

```bash
# Start the MCP server alongside Leantime
docker-compose -f docker-compose.yml -f docker-compose.mcp.yml up -d

# Check logs
docker logs leantime-mcp-gateway

# Verify health
curl -k https://localhost:8443/health
```

## Client Setup (On Each Computer)

### Option 1: Claude Desktop Integration

1. **Install setup script** on each computer:

```bash
# Download client setup script
curl -O https://your-server/mcp-server/client_setup.py

# Or copy it manually to each machine
```

2. **Run interactive setup**:

```bash
python client_setup.py
```

Choose option 1 for Claude Desktop and follow prompts:
- Enter your server URL (e.g., https://pm.dxbmeta.com:8443)
- Generate or provide authentication token
- Client will be automatically configured

3. **Restart Claude Desktop**

Your Leantime PM will now appear in Claude's context menu!

### Option 2: Direct API Access

```python
import requests

# Authenticate
response = requests.post(
    "https://your-server:8443/api/auth/token",
    json={"username": "your_username", "password": "your_password"}
)
token = response.json()["token"]

# Use MCP tools
headers = {"Authorization": f"Bearer {token}"}

# Create task
task = requests.post(
    "https://your-server:8443/mcp/tools/create_task",
    headers=headers,
    json={
        "project_id": 1,
        "title": "New task from laptop",
        "priority": "high"
    }
)

# Get AI analysis
health = requests.post(
    "https://your-server:8443/mcp/tools/analyze_project_health",
    headers=headers,
    json={"project_id": 1}
)
print(health.json())
```

## Available MCP Resources

### Resources (Read-only data)
- `leantime://projects` - List all projects
- `leantime://tasks` - All tasks with AI organization
- `leantime://team` - Team member information  
- `leantime://reports` - AI-generated reports

### Tools (Actions)
- `create_task` - Create new task with auto-assignment
- `update_task_status` - Update task status
- `analyze_project_health` - AI health analysis
- `auto_assign_task` - Smart task assignment
- `generate_report` - Create status reports

### Prompts (Templates)
- `daily_standup` - Generate standup report
- `sprint_planning` - Plan next sprint with AI
- `task_breakdown` - Break complex tasks into subtasks

## Self-Organizing Features

### Automatic Task Organization
When enabled, the MCP server will:
1. Analyze all incoming tasks
2. Suggest optimal priority order
3. Identify dependencies
4. Recommend assignments based on team skills
5. Estimate completion times

### Smart Assignment
The AI considers:
- Team member skills and expertise
- Current workload distribution
- Past performance on similar tasks
- Availability and capacity
- Task complexity and requirements

### Health Monitoring
Continuous analysis of:
- Project velocity trends
- Risk factors
- Blocker patterns  
- Team burnout indicators
- Budget and timeline adherence

## Security Configuration

### Client Whitelisting
To restrict access to specific computers:

```bash
# Get client IDs from each computer
python client_setup.py
# Note the "Client ID: hostname_123456789"

# Add to server .env
MCP_ALLOWED_CLIENTS=desktop_123456,laptop_789012,work_345678
```

### Token Rotation
Tokens expire after 24 hours by default. To change:

```bash
# In .env
MCP_JWT_EXPIRY_HOURS=48
```

### Audit Logging
All actions are logged with:
- Client ID
- Action performed
- Timestamp
- Details

View logs:
```bash
docker logs leantime-mcp-gateway 2>&1 | grep AUDIT
```

## Monitoring

### Health Check
```bash
# Server health
curl -k https://localhost:8443/health

# Database connection
docker exec leantime-mcp-gateway python -c "
from leantime_mcp_server import DatabaseManager
db = DatabaseManager()
print('DB Pool:', db.pool.pool_name)
"
```

### Metrics
```bash
# Connection stats
docker exec leantime-mcp-registry redis-cli -a $MCP_REGISTRY_PASSWORD INFO clients

# Resource usage
docker stats leantime-mcp-gateway
```

## Troubleshooting

### Connection Issues

1. **"Connection refused"**
   - Check firewall rules
   - Verify port 8443 is open
   - Ensure Docker containers are running

2. **"Authentication failed"**
   - Regenerate token
   - Check token hasn't expired
   - Verify client ID is whitelisted

3. **"SSL certificate error"**
   - For self-signed certs, add exception in browser
   - Or use `verify=False` in Python requests

### Database Issues

1. **"Database connection unavailable"**
   - Check MySQL container is running
   - Verify credentials in .env
   - Test connection:
   ```bash
   docker exec -it mysql_leantime mysql -u lean -p
   ```

2. **"Access denied"**
   - Ensure MCP server has database permissions
   - Check network connectivity between containers

### AI Features Not Working

1. **"AI request failed"**
   - Verify OPENROUTER_API_KEY is set
   - Check API key has credits
   - Try free models first (mistral-7b-free)

2. **"Auto-organize not working"**
   - Ensure MCP_AUTO_ORGANIZE=true
   - Check logs for AI errors
   - Verify OpenRouter connectivity

## Advanced Configuration

### Using Different AI Models

Edit `mcp-server/leantime_mcp_server.py`:

```python
# Change default model
payload = {
    "model": "meta-llama/llama-3.1-70b-instruct",  # More powerful
    # or
    "model": "microsoft/phi-3-mini-128k-instruct",  # Faster/cheaper
}
```

### Custom Self-Organizing Rules

Add your own rules in the AI prompts:

```python
async def auto_organize_tasks(self, tasks):
    prompt = f"""
    {your_custom_rules}
    
    Consider our team's specific workflow:
    - Frontend tasks before backend
    - Testing required for all features
    - Client approval needed for UI changes
    """
```

### Scaling for Large Teams

For teams > 10 people:

1. Increase database pool:
   ```bash
   DB_POOL_SIZE=25
   ```

2. Add Redis caching:
   ```yaml
   # In docker-compose.mcp.yml
   deploy:
     replicas: 3
   ```

3. Use load balancer for multiple MCP servers

## Cost Optimization

### OpenRouter Model Costs

| Model | Cost/Million Tokens | Best For |
|-------|-------------------|----------|
| mistral-7b-free | $0 | Simple tasks, testing |
| llama-3.1-8b | $0.06 | General use |
| mixtral-8x7b | $0.24 | Complex analysis |
| llama-3.1-70b | $0.59 | Critical decisions |

### Tips to Reduce Costs

1. Use free models for simple operations
2. Cache AI responses for repeated queries
3. Batch similar requests
4. Set max token limits
5. Use scheduled analysis instead of real-time

## Backup and Recovery

### Backup MCP Configuration
```bash
# Backup settings and tokens
docker cp leantime-mcp-gateway:/app/config backup/

# Backup Redis registry
docker exec leantime-mcp-registry redis-cli -a $MCP_REGISTRY_PASSWORD --rdb /data/dump.rdb
```

### Restore
```bash
# Restore configuration
docker cp backup/config leantime-mcp-gateway:/app/

# Restore Redis
docker cp dump.rdb leantime-mcp-registry:/data/
```

## Support

- **Leantime Issues**: https://github.com/Leantime/leantime/issues
- **MCP Protocol**: https://modelcontextprotocol.io
- **OpenRouter**: https://openrouter.ai/docs

---

**Security Note**: Always use unique security keys in production and enable TLS/HTTPS for all connections. Never commit secrets to version control.