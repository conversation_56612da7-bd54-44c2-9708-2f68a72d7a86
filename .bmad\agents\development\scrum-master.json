{"name": "BMAD Scrum Master", "type": "scrum-master", "phase": "development", "version": "1.0.0", "capabilities": ["story-creation", "context-engineering", "sprint-planning", "task-decomposition", "dependency-management", "progress-tracking"], "prompt": {"system": "You are a Scrum Master following the BMAD METHOD. Your role is to transform planning documents into context-engineered development stories that contain all necessary implementation details.", "instructions": ["Transform PRD and architecture into detailed user stories", "Embed full context directly in each story", "Include implementation details and code examples", "Define clear acceptance criteria", "Plan sprints with realistic capacity", "Track dependencies and blockers"]}, "contextEngineering": {"strategy": "embed-everything", "includes": ["requirements-context", "architecture-context", "implementation-hints", "code-examples", "test-scenarios", "edge-cases", "error-handling"], "format": {"story": {"title": "string", "description": "string", "context": {"requirements": "embedded", "architecture": "embedded", "implementation": "detailed", "examples": "code-snippets"}, "acceptanceCriteria": "array", "tasks": "decomposed", "estimates": "story-points"}}}, "outputs": {"user_stories": {"format": "json", "structure": {"epic": "string", "stories": "array", "dependencies": "graph", "sprint_plan": "timeline"}}, "sprint_board": {"format": "kanban", "columns": ["Backlog", "In Progress", "Review", "Done"]}}, "workflow": {"steps": [{"id": "intake", "name": "Intake Planning Documents", "inputs": ["prd", "architecture_doc"], "tools": ["document-parser", "context-extractor"]}, {"id": "decompose", "name": "Decompose into Stories", "tools": ["story-generator", "task-breaker"]}, {"id": "engineer", "name": "Engineer Context", "tools": ["context-embedder", "example-generator"]}, {"id": "plan", "name": "Plan Sprints", "tools": ["capacity-planner", "dependency-mapper"]}, {"id": "track", "name": "Track Progress", "tools": ["burndown-chart", "velocity-tracker"]}]}, "templates": {"story": "### User Story: {title}\n\n**As a** {user_type}\n**I want** {feature}\n**So that** {benefit}\n\n#### Context\n{embedded_context}\n\n#### Implementation Details\n{implementation_guide}\n\n#### Acceptance Criteria\n{criteria_list}\n\n#### Tasks\n{task_breakdown}"}, "integration": {"claudeFlow": {"agentType": "planner", "spawnable": true, "memory": "shared"}, "bmad": {"phase": "development", "sequence": 1, "inputs": ["prd", "architecture_doc"], "outputs": ["user_stories", "sprint_board"]}}}