version: '3.8'

services:
  # Node.js Email Service
  email-service:
    build: ./email-service
    container_name: leantime-email-service
    restart: unless-stopped
    environment:
      - HTTP_PORT=3000
      - SMTP_PORT=2525
      - LOG_LEVEL=info
      - REDIS_URL=redis://redis_email:6379
      - REDIS_HOST=redis_email
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${EMAIL_REDIS_PASSWORD:-email_redis_secure_password}
      - DEFAULT_FROM_EMAIL=${LEAN_EMAIL_RETURN:-<EMAIL>}
      - SMTP_USERNAME=leantime
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAIL_API_KEY=${EMAIL_API_KEY}
      
      # Email Provider Configuration
      - RESEND_API_KEY=${RESEND_API_KEY:-}
      - BREVO_USERNAME=${BREVO_USERNAME:-}
      - BREVO_SMTP_KEY=${BREVO_SMTP_KEY:-}
      
      # Optional: Gmail OAuth2
      - GMAIL_CLIENT_ID=${GMAIL_CLIENT_ID:-}
      - GMAIL_CLIENT_SECRET=${GMAIL_CLIENT_SECRET:-}
      - GMAIL_REFRESH_TOKEN=${GMAIL_REFRESH_TOKEN:-}
      - GMAIL_USER=${GMAIL_USER:-}
      
      # Optional: Fallback SMTP
      - FALLBACK_SMTP_HOST=${FALLBACK_SMTP_HOST:-}
      - FALLBACK_SMTP_PORT=${FALLBACK_SMTP_PORT:-587}
      - FALLBACK_SMTP_SECURE=${FALLBACK_SMTP_SECURE:-false}
      - FALLBACK_SMTP_USER=${FALLBACK_SMTP_USER:-}
      - FALLBACK_SMTP_PASS=${FALLBACK_SMTP_PASS:-}
      
    ports:
      - "${EMAIL_DASHBOARD_PORT:-3000}:3000"  # Dashboard
      # SMTP port 2525 is internal only
      
    volumes:
      - email-logs:/app/logs
      
    networks:
      - leantime-net
      
    depends_on:
      redis_email:
        condition: service_healthy
      
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Redis for Email Queue
  redis_email:
    image: redis:7-alpine
    container_name: redis_email
    restart: unless-stopped
    command: redis-server --requirepass ${EMAIL_REDIS_PASSWORD:-email_redis_secure_password}
    environment:
      - TZ=Asia/Dubai
      
    volumes:
      - redis-email-data:/data
      
    networks:
      - leantime-net
      
    # Security: No external access
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 128M
        reservations:
          cpus: '0.05'
          memory: 32M

volumes:
  email-logs:
    driver: local
  redis-email-data:
    driver: local

networks:
  leantime-net:
    external: true