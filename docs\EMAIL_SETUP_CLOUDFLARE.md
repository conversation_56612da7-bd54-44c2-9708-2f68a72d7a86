# Email Setup with <PERSON><PERSON><PERSON><PERSON> + Resend

## Quick Setup Guide

### Step 1: Sign up for Resend
1. Go to https://resend.com/signup
2. Create free account (no credit card needed)
3. Get your API key from dashboard

### Step 2: Add Domain to Resend
1. In Resend dashboard, go to "Domains"
2. Add your domain (e.g., kanousai.com)
3. You'll get DNS records to add

### Step 3: Configure Cloudflare DNS
Add these records in Cloudflare DNS:

```
Type: TXT
Name: @
Value: v=spf1 include:amazonses.com ~all

Type: TXT  
Name: resend._domainkey
Value: [Resend will provide this - looks like p=MIGfMA0G...]

Type: CNAME
Name: cname._domainkey
Value: [Resend will provide]

Type: MX (if using Email Routing)
Name: @
Priority: 1
Value: feedback-smtp.us-east-1.amazonses.com
```

### Step 4: Update Leantime Configuration

```env
# Email Configuration with Resend
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'smtp.resend.com'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = 'resend'
LEAN_EMAIL_SMTP_PASSWORD = 're_YOUR_API_KEY_HERE'
LEAN_EMAIL_SMTP_AUTO_TLS = true
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'
```

## Alternative Email Services

### Option 1: Mailgun with Cloudflare

```env
# Mailgun Configuration
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'smtp.mailgun.org'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = '<EMAIL>'
LEAN_EMAIL_SMTP_PASSWORD = 'YOUR_MAILGUN_PASSWORD'
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'
```

**Mailgun DNS Records for Cloudflare:**
```
Type: TXT
Name: mg
Value: v=spf1 include:mailgun.org ~all

Type: TXT
Name: k1._domainkey.mg
Value: [Mailgun provides this]

Type: CNAME
Name: email.mg
Value: mailgun.org

Type: MX
Name: mg
Priority: 10
Value: mxa.mailgun.org

Type: MX
Name: mg
Priority: 10
Value: mxb.mailgun.org
```

### Option 2: Brevo (SendinBlue) - Free Forever Plan

```env
# Brevo Configuration
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'smtp-relay.brevo.com'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = 'YOUR_BREVO_EMAIL'
LEAN_EMAIL_SMTP_PASSWORD = 'YOUR_SMTP_KEY'
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'
```

### Option 3: Amazon SES (Most Cost-Effective)

```env
# Amazon SES Configuration
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'email-smtp.eu-west-1.amazonaws.com'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = 'YOUR_AWS_SMTP_USERNAME'
LEAN_EMAIL_SMTP_PASSWORD = 'YOUR_AWS_SMTP_PASSWORD'
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'
```

### Option 4: Postmark (Best Deliverability)

```env
# Postmark Configuration
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'smtp.postmarkapp.com'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = 'YOUR_POSTMARK_SERVER_TOKEN'
LEAN_EMAIL_SMTP_PASSWORD = 'YOUR_POSTMARK_SERVER_TOKEN'
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'
```

## Cloudflare Email Routing + SMTP Service

The best setup combines:
1. **Cloudflare Email Routing** for receiving emails
2. **Resend/Mailgun/Brevo** for sending emails

### How it works:
```
Incoming: <EMAIL> → Cloudflare → Your Gmail/Outlook
Outgoing: Leantime → Resend SMTP → Recipients
```

## Testing Email Configuration

### 1. Test SMTP Connection
```bash
# Using telnet
telnet smtp.resend.com 587

# Using curl
curl smtp://smtp.resend.com:587 --mail-from <EMAIL> --mail-rcpt <EMAIL>
```

### 2. Test from Leantime
```bash
# Restart Leantime with new email config
docker-compose restart leantime

# Try password reset or invite user to test
```

### 3. Using Python Script
```python
import smtplib
from email.mime.text import MIMEText

smtp_host = 'smtp.resend.com'
smtp_port = 587
username = 'resend'
password = 're_YOUR_API_KEY'

msg = MIMEText('Test email from Leantime')
msg['Subject'] = 'Test Email'
msg['From'] = '<EMAIL>'
msg['To'] = '<EMAIL>'

server = smtplib.SMTP(smtp_host, smtp_port)
server.starttls()
server.login(username, password)
server.send_message(msg)
server.quit()
print("Email sent successfully!")
```

## Troubleshooting

### Common Issues:

1. **"Authentication failed"**
   - Check API key/password
   - Verify username format
   - Ensure account is verified

2. **"Connection refused"**
   - Check firewall/port 587
   - Try port 465 with SSL
   - Verify Docker network

3. **"SPF/DKIM failure"**
   - Wait 48h for DNS propagation
   - Verify DNS records in Cloudflare
   - Check sender domain verification

4. **"Rate limit exceeded"**
   - Check service tier limits
   - Implement email queuing
   - Upgrade plan if needed

## Recommended: Resend Setup Steps

1. Sign up at https://resend.com
2. Verify your email
3. Add domain in dashboard
4. Copy DNS records to Cloudflare
5. Wait for verification (usually < 5 minutes)
6. Get API key
7. Update .env file
8. Restart Leantime

## Free Tier Comparison

| Service | Free Emails | Credit Card | Best For |
|---------|------------|-------------|----------|
| Resend | 100/day, 3000/month | No | Developers |
| Mailgun | 5000/month (3 months) | Yes | High volume |
| Brevo | 300/day forever | No | Small business |
| Amazon SES | Pay-as-you-go | Yes | Scale |
| Postmark | 100/month | Yes | Transactional |

## Security Best Practices

1. **Use API keys, not passwords**
2. **Enable 2FA on email service**
3. **Restrict sending domains**
4. **Monitor bounce rates**
5. **Set up DMARC policy**
6. **Use dedicated IPs (paid plans)**

Choose Resend for easiest setup, Brevo for most generous free tier, or Mailgun for best Cloudflare integration!