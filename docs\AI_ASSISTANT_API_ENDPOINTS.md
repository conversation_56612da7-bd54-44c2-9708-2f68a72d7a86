# AI Assistant API Endpoints Documentation

## 🌐 Server Configuration

### Base URLs (When Deployed)
- **Local Development**: `http://localhost:8444`
- **Production Server**: `http://*************:8444`
- **Docker Internal**: `http://ai-assistant:8444`

### Current Status: ⚠️ **NOT DEPLOYED YET**
The AI assistant server is built but not yet running on the production server.

## 📍 Available Endpoints

### 1. Health Check
- **URL**: `GET /health`
- **Auth Required**: No
- **Description**: Check if server is alive
- **Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-09-12T14:30:00.000Z"
}
```

### 2. Server Status
- **URL**: `GET /status`
- **Auth Required**: No
- **Description**: Get detailed server status and configuration
- **Response**:
```json
{
  "status": "running",
  "version": "1.0.0",
  "uptime_seconds": 3600,
  "request_count": 150,
  "config": {
    "features": {
      "expense_automation": true,
      "ticket_automation": true,
      "knowledge_base": true,
      "project_automation": true,
      "analytics": true
    },
    "automation": {
      "auto_organize": true,
      "auto_assign": true,
      "auto_prioritize": true,
      "auto_learn": true
    }
  }
}
```

### 3. Process Request (Main API)
- **URL**: `POST /api/v1/process`
- **Auth Required**: Yes (JWT Bearer token)
- **Description**: Main endpoint for all AI operations
- **Headers**:
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

#### Supported Actions:

##### Expense Processing
```json
{
  "action": "expense.process",
  "data": {
    "receipt_image": "base64_encoded_image",
    "user_id": 1,
    "project_id": 5
  }
}
```

##### Ticket Processing
```json
{
  "action": "ticket.process",
  "data": {
    "ticket_id": 123,
    "title": "Cannot login",
    "description": "User reporting login issues",
    "priority": "high"
  }
}
```

##### Knowledge Base Search
```json
{
  "action": "knowledge.search",
  "data": {
    "query": "How to reset password",
    "limit": 5
  }
}
```

##### Workflow Execution
```json
{
  "action": "workflow.execute",
  "data": {
    "workflow_id": "expense_approval",
    "params": {
      "amount": 500,
      "category": "travel"
    }
  }
}
```

## 🔐 Authentication

### Get JWT Token
First, you need to authenticate and get a token (endpoint to be implemented):
```bash
curl -X POST http://*************:8444/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "your_password"
  }'
```

### Use Token in Requests
```bash
curl -X POST http://*************:8444/api/v1/process \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "knowledge.search",
    "data": {"query": "test"}
  }'
```

## 🚀 Deployment Instructions

To deploy and make URLs active:

### 1. Copy files to server
```bash
scp -r ai-assistant root@*************:/root/
scp docker-compose.ai.yml root@*************:/root/
```

### 2. Create .env file on server
```bash
ssh root@*************
cd /root/ai-assistant
cp .env.template .env
# Edit .env with actual values
nano .env
```

### 3. Build and start services
```bash
# On server
cd /root
docker-compose -f docker-compose.ai.yml build
docker-compose -f docker-compose.ai.yml up -d
```

### 4. Apply database migrations
```bash
# On server
mysql -u lean -p'password' leantime < ai-assistant/migrations/001_ai_tables.sql
```

### 5. Verify deployment
```bash
# Test health check
curl http://localhost:8444/health

# Check logs
docker logs ai-assistant
```

## 📊 API Rate Limits

- **Per Minute**: 60 requests (configurable)
- **Per Hour**: 1000 requests (configurable)
- **Per Client**: Tracked via JWT token

## 🔄 WebSocket Support (Planned)

Future support for real-time updates:
- **URL**: `ws://*************:8444/ws`
- **Events**: 
  - `expense.processed`
  - `ticket.updated`
  - `workflow.completed`

## 🧪 Testing Endpoints

### Local Testing (Before Deployment)
```bash
# Start locally
cd ai-assistant
python mcp_server.py

# Test health
curl http://localhost:8444/health

# Test status
curl http://localhost:8444/status
```

### Production Testing (After Deployment)
```bash
# From your machine
curl http://*************:8444/health

# Test with authentication
curl -X POST http://*************:8444/api/v1/process \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"action": "knowledge.search", "data": {"query": "test"}}'
```

## ⚠️ Important Notes

1. **CORS is enabled** for all origins (configure for production)
2. **JWT tokens expire** after 24 hours by default
3. **File uploads** limited to 10MB by default
4. **ChromaDB** runs on port 8000 for vector search
5. **Redis** used for task queuing (internal only)

## 🎯 Next Steps to Activate URLs

1. Deploy the Docker containers
2. Configure environment variables
3. Apply database migrations
4. Test endpoints
5. Set up SSL/TLS for production (recommended)

The URLs will become active once the deployment steps are completed!