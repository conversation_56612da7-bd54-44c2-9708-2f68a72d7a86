# Quick Email Test Commands

## 🚀 Run These Commands on Your DigitalOcean Server

### 1. Quick Health Check
```bash
curl -f http://localhost:2525/health
```
**Expected**: `{"status":"healthy","service":"enhanced-email-service",...}`

### 2. Send Test Email
```bash
curl -X POST http://localhost:2525/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Password Reset Test - '$(date)'",
    "html": "<h2>Email Test</h2><p>If you receive this, email is working!</p>"
  }'
```
**Expected**: `{"success":true,"id":"...","message":"Email sent successfully"}`

### 3. Check Email Service Logs
```bash
podman logs leantime-email-service --tail 20
```
**Look for**: `✅ SMTP: Email sent successfully` or `📤 HTTP: Sending email`

### 4. Test Password Reset in Browser
1. Go to: **https://admin.dxbmeta.com**
2. Click "Forgot Password" 
3. Enter: **<EMAIL>**
4. Submit and check logs:
```bash
podman logs leantime-email-service --tail 10
```

### 5. Check Container Status
```bash
podman ps | grep -E "(leantime|email)"
```
**Expected**: Both containers running

## ✅ Success Indicators

- Health check returns `"status":"healthy"`
- Test email API returns `"success":true`
- Email <NAME_EMAIL> within 2 minutes
- Logs show `Email sent successfully`
- Password reset form submits without errors

## ❌ Failure Indicators

- Health check fails or times out
- API returns error or `"success":false`
- No email received after 5 minutes
- Logs show errors or no activity
- Password reset form shows error message

## 🔧 Quick Fixes

### If Health Check Fails:
```bash
podman restart leantime-email-service
sleep 10
curl -f http://localhost:2525/health
```

### If Email Not Sent:
```bash
# Check Resend API key
grep RESEND_API_KEY .env

# Restart email service
podman-compose restart email-service
```

### If Container Not Running:
```bash
podman-compose up -d email-service
```

---

## 📧 Expected Email Content

When password reset works, you should receive:

**From**: <EMAIL>  
**Subject**: Password Reset - Kanousai Project Management  
**Content**: Professional HTML email with reset link

---

**🎯 Bottom Line**: If the test email command works and you receive the email, then password reset emails will work too!
