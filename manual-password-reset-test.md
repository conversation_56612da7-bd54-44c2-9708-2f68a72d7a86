# Password Reset Manual Testing Guide

## 🎯 Objective
Test the complete password reset flow to ensure emails are being sent correctly.

## ✅ Pre-Test Checklist

### 1. Email Configuration Status
- [x] **Domain Consistency**: All using `dxbmeta.com` ✅
- [x] **SMTP Port**: Set to `2525` ✅  
- [x] **Resend API**: Working (tested successfully) ✅
- [x] **Configuration**: All mismatches resolved ✅

### 2. Services Running
Verify these services are running on your DigitalOcean server:
```bash
# Check containers
podman ps | grep -E "(leantime|email)"

# Check email service specifically
podman logs leantime-email-service --tail 20

# Check Leantime logs
podman logs mysql_leantime_podman --tail 20
```

## 🧪 Manual Test Steps

### Step 1: Access Leantime
1. Open browser and go to: **https://admin.dxbmeta.com**
2. Verify the login page loads correctly
3. Look for "Forgot Password" or "Reset Password" link

### Step 2: Initiate Password Reset
1. Click on "Forgot Password" link
2. Enter email: **<EMAIL>**
3. Click "Send Reset Link" or similar button
4. Note any success/error messages

### Step 3: Check Email Service Logs
On your DigitalOcean server, run:
```bash
# Check email service logs for activity
podman logs leantime-email-service --tail 50

# Look for entries like:
# "📤 SMTP: Processing email from..."
# "✅ SMTP: Email sent successfully..."
# "❌ SMTP: Email processing failed..."
```

### Step 4: Verify Email Delivery
1. Check **<EMAIL>** inbox
2. Look for email from **<EMAIL>**
3. Subject should be related to password reset
4. Email should contain a reset link

### Step 5: Test Reset Link
1. Click the reset link in the email
2. Should redirect to Leantime password reset form
3. Enter new password
4. Submit and verify success

## 🔍 Troubleshooting Guide

### If No Email is Received

#### Check 1: Email Service Status
```bash
# Health check
curl -f http://localhost:2525/health

# Expected response:
# {"status":"healthy","service":"enhanced-email-service",...}
```

#### Check 2: Leantime Email Settings
Verify in Leantime admin panel:
- Go to Settings → Email
- Confirm SMTP settings match our configuration:
  - Host: `email-service`
  - Port: `2525`
  - From: `<EMAIL>`

#### Check 3: Resend API Status
```bash
# Test Resend API directly
curl -X POST https://api.resend.com/emails \
  -H "Authorization: Bearer re_4V59PXub_GEiFvXAEp11e1Fudtcrk5Lw9" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Direct API Test",
    "html": "<p>This is a direct API test</p>"
  }'
```

#### Check 4: Container Network
```bash
# Verify containers can communicate
podman exec mysql_leantime_podman ping email-service
podman exec leantime-email-service ping mysql_leantime_podman
```

### Common Issues & Solutions

#### Issue: "Connection Refused" 
**Solution**: Email service container not running
```bash
podman-compose -f docker-compose.yml up -d email-service
```

#### Issue: "Invalid API Key"
**Solution**: Check Resend API key in .env
```bash
grep RESEND_API_KEY .env
# Should show: RESEND_API_KEY=re_4V59PXub_GEiFvXAEp11e1Fudtcrk5Lw9
```

#### Issue: "Email Not Found"
**Solution**: Verify user exists in Leantime database
```bash
podman exec mysql_leantime_podman mysql -u lean -p leantime -e "SELECT id, username, email FROM zp_user WHERE email='<EMAIL>';"
```

## 📧 Expected Email Content

The password reset email should contain:
- **From**: <EMAIL>
- **Subject**: Password Reset - [Site Name]
- **Content**: 
  - Professional HTML template
  - Reset link with token
  - Security warnings
  - Expiry information (24 hours)

## 🎯 Success Criteria

✅ **Test Passes If**:
1. Password reset form submits without errors
2. Email service logs show successful email processing
3. Reset email arrives in inbox within 2 minutes
4. Reset link works and allows password change
5. New password allows successful login

❌ **Test Fails If**:
1. Form submission shows error
2. No email service activity in logs
3. No email received after 5 minutes
4. Reset link is broken or expired
5. Password change fails

## 🚀 Quick Test Command

Run this on your DigitalOcean server to test email service directly:
```bash
# Test email service health
curl -f http://localhost:2525/health

# Send test email via HTTP API
curl -X POST http://localhost:2525/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Password Reset Test",
    "html": "<h2>Test Email</h2><p>If you receive this, the email service is working!</p>"
  }'
```

## 📞 Next Steps After Testing

1. **If Test Passes**: Password reset is working correctly! ✅
2. **If Test Fails**: Use troubleshooting guide above
3. **Need Help**: Check logs and provide error details

---

**Note**: This test should be performed on the DigitalOcean server where Leantime is running, as the email service is internal to the container network.
