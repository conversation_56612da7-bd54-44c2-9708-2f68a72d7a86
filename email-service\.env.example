# Email Service Configuration

# Server Settings
HTTP_PORT=3000
SMTP_PORT=2525
LOG_LEVEL=info

# Redis Configuration (for email queue)
REDIS_URL=redis://redis_email:6379
REDIS_HOST=redis_email
REDIS_PORT=6379
REDIS_PASSWORD=

# Default Email Settings
DEFAULT_FROM_EMAIL=<EMAIL>
SMTP_USERNAME=leantime
SMTP_PASSWORD=leantime-email-service

# Resend Configuration (Primary - Cloudflare compatible)
RESEND_API_KEY=re_your_api_key_here

# Brevo Configuration (Fallback 1 - 300/day free)
BREVO_USERNAME=<EMAIL>
BREVO_SMTP_KEY=your_smtp_key_here

# Gmail OAuth2 Configuration (Fallback 2)
GMAIL_CLIENT_ID=your_client_id
GMAIL_CLIENT_SECRET=your_client_secret
GMAIL_REFRESH_TOKEN=your_refresh_token
GMAIL_USER=<EMAIL>

# Generic SMTP Fallback (Emergency)
FALLBACK_SMTP_HOST=smtp.example.com
FALLBACK_SMTP_PORT=587
FALLBACK_SMTP_SECURE=false
FALLBACK_SMTP_USER=username
FALLBACK_SMTP_PASS=password