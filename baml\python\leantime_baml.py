#!/usr/bin/env python3
"""
BAML Integration for Leantime Project Management
Provides AI-powered enhancements for project management workflows
"""

import os
import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

# FastAPI for REST API
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field
import mysql.connector
from mysql.connector import pooling

# Environment configuration
class Config:
    """Configuration for Leantime BAML integration"""
    # AI Provider Keys
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Leantime Database
    DB_HOST = os.getenv("LEAN_DB_HOST", "mysql_leantime")
    DB_USER = os.getenv("LEAN_DB_USER", "lean")
    DB_PASSWORD = os.getenv("LEAN_DB_PASSWORD", "changeme123")
    DB_DATABASE = os.getenv("LEAN_DB_DATABASE", "leantime")
    
    # API Configuration
    API_PORT = int(os.getenv("BAML_API_PORT", "8000"))
    API_HOST = os.getenv("BAML_API_HOST", "0.0.0.0")

# Database connection pool
db_pool = pooling.MySQLConnectionPool(
    pool_name="leantime_pool",
    pool_size=10,
    host=Config.DB_HOST,
    user=Config.DB_USER,
    password=Config.DB_PASSWORD,
    database=Config.DB_DATABASE
)

# FastAPI app
app = FastAPI(title="Leantime BAML Integration", version="1.0.0")

# Pydantic models for API
class ProjectHealthRequest(BaseModel):
    project_id: int
    include_forecast: bool = True

class TaskBreakdownRequest(BaseModel):
    task_id: int
    max_subtasks: int = 10

class SprintOptimizationRequest(BaseModel):
    project_id: int
    sprint_duration: int = 14
    exclude_tasks: List[int] = []

class EffortEstimationRequest(BaseModel):
    task_title: str
    task_description: str
    tags: List[str] = []

class StatusReportRequest(BaseModel):
    project_id: int
    period_days: int = 7
    audience: str = "team"  # team, management, client

# Database helper functions
def get_db_connection():
    """Get database connection from pool"""
    return db_pool.get_connection()

def fetch_project(project_id: int) -> Dict:
    """Fetch project details from database"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    query = """
    SELECT 
        p.id,
        p.name,
        p.clientId,
        c.name as clientName,
        p.state as status,
        p.start,
        p.end,
        p.hourBudget,
        p.dollarBudget as budget
    FROM zp_projects p
    LEFT JOIN zp_clients c ON p.clientId = c.id
    WHERE p.id = %s
    """
    
    cursor.execute(query, (project_id,))
    project = cursor.fetchone()
    
    cursor.close()
    conn.close()
    
    return project

def fetch_project_tasks(project_id: int) -> List[Dict]:
    """Fetch all tasks for a project"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    query = """
    SELECT 
        t.id,
        t.headline as title,
        t.description,
        t.userId as assigneeId,
        u.firstname as assigneeName,
        t.status,
        t.priority,
        t.hourRemaining as estimatedHours,
        t.tags,
        t.dateToFinish as dueDate,
        t.dependingTicketId as dependencies
    FROM zp_tickets t
    LEFT JOIN zp_users u ON t.userId = u.id
    WHERE t.projectId = %s AND t.type = 'task'
    ORDER BY t.priority DESC, t.dateToFinish ASC
    """
    
    cursor.execute(query, (project_id,))
    tasks = cursor.fetchall()
    
    cursor.close()
    conn.close()
    
    return tasks

def fetch_team_members(project_id: int) -> List[Dict]:
    """Fetch team members for a project"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    query = """
    SELECT DISTINCT
        u.id,
        u.firstname,
        u.lastname,
        u.username as email,
        r.role
    FROM zp_relationuserproject rup
    JOIN zp_users u ON rup.userId = u.id
    JOIN zp_roles r ON u.role = r.id
    WHERE rup.projectId = %s
    """
    
    cursor.execute(query, (project_id,))
    team = cursor.fetchall()
    
    cursor.close()
    conn.close()
    
    return team

def fetch_time_entries(project_id: int, days: int = 30) -> List[Dict]:
    """Fetch recent time entries for a project"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    query = """
    SELECT 
        t.id,
        t.userId,
        t.ticketId as taskId,
        t.hours,
        t.workDate as date,
        t.description
    FROM zp_timesheets t
    JOIN zp_tickets tk ON t.ticketId = tk.id
    WHERE tk.projectId = %s 
    AND t.workDate >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
    ORDER BY t.workDate DESC
    """
    
    cursor.execute(query, (project_id, days))
    entries = cursor.fetchall()
    
    cursor.close()
    conn.close()
    
    return entries

# API Endpoints

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Leantime BAML Integration"}

@app.post("/api/project-health")
async def analyze_project_health(request: ProjectHealthRequest):
    """Analyze project health using AI"""
    try:
        # Fetch data from database
        project = fetch_project(request.project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        tasks = fetch_project_tasks(request.project_id)
        time_entries = fetch_time_entries(request.project_id)
        
        # Calculate basic metrics
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t['status'] == 'done'])
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        # Determine health status
        health = "healthy"
        issues = []
        
        if completion_rate < 40:
            health = "at-risk"
            issues.append("Low task completion rate")
        
        overdue_tasks = [t for t in tasks if t['dueDate'] and t['dueDate'] < datetime.now().isoformat()]
        if len(overdue_tasks) > total_tasks * 0.2:
            health = "critical"
            issues.append(f"{len(overdue_tasks)} overdue tasks")
        
        # Generate recommendations
        recommendations = []
        if health != "healthy":
            recommendations.append("Review and update task priorities")
            recommendations.append("Consider redistributing workload")
            if overdue_tasks:
                recommendations.append("Address overdue tasks immediately")
        
        # Predict completion (simplified)
        if request.include_forecast and tasks:
            avg_completion_time = 7  # days per task (simplified)
            remaining_tasks = total_tasks - completed_tasks
            predicted_days = remaining_tasks * avg_completion_time
            predicted_date = (datetime.now() + timedelta(days=predicted_days)).isoformat()
        else:
            predicted_date = project.get('end')
        
        return {
            "project_id": request.project_id,
            "project_name": project['name'],
            "overall_health": health,
            "score": max(0, 100 - len(issues) * 20),
            "issues": issues,
            "recommendations": recommendations,
            "metrics": {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_rate": round(completion_rate, 2),
                "overdue_tasks": len(overdue_tasks)
            },
            "predicted_completion_date": predicted_date
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/breakdown-task")
async def breakdown_task(request: TaskBreakdownRequest):
    """Break down a complex task into subtasks"""
    try:
        # Fetch task from database
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = "SELECT * FROM zp_tickets WHERE id = %s"
        cursor.execute(query, (request.task_id,))
        task = cursor.fetchone()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Generate subtasks (simplified AI simulation)
        subtasks = []
        task_types = ["Design", "Implementation", "Testing", "Documentation", "Review"]
        
        for i, task_type in enumerate(task_types[:request.max_subtasks]):
            subtasks.append({
                "title": f"{task_type}: {task['headline']}",
                "description": f"{task_type} phase for {task['headline']}",
                "estimated_hours": 4 + (i * 2),
                "priority": task['priority'],
                "dependencies": [subtasks[-1]['title']] if subtasks else []
            })
        
        # Calculate total estimate
        total_hours = sum(st['estimated_hours'] for st in subtasks)
        
        cursor.close()
        conn.close()
        
        return {
            "original_task": {
                "id": task['id'],
                "title": task['headline']
            },
            "subtasks": subtasks,
            "total_estimated_hours": total_hours,
            "suggested_approach": "Sequential execution recommended for dependencies"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/optimize-sprint")
async def optimize_sprint(request: SprintOptimizationRequest):
    """Optimize sprint planning"""
    try:
        # Fetch available tasks
        tasks = fetch_project_tasks(request.project_id)
        team = fetch_team_members(request.project_id)
        
        # Filter out excluded and completed tasks
        available_tasks = [
            t for t in tasks 
            if t['id'] not in request.exclude_tasks 
            and t['status'] not in ['done', 'archived']
        ]
        
        # Sort by priority and select for sprint
        available_tasks.sort(key=lambda x: (x['priority'], x['dueDate'] or '9999'))
        
        # Calculate team capacity (simplified)
        team_capacity = len(team) * request.sprint_duration * 6  # 6 hours per day per person
        
        # Select tasks for sprint
        selected_tasks = []
        total_hours = 0
        
        for task in available_tasks:
            task_hours = task.get('estimatedHours') or 8  # Default 8 hours if not estimated
            if total_hours + task_hours <= team_capacity:
                selected_tasks.append(task)
                total_hours += task_hours
            else:
                break
        
        # Generate sprint goal
        if selected_tasks:
            priorities = [t for t in selected_tasks if t['priority'] == 'urgent']
            sprint_goal = f"Complete {len(priorities)} high-priority items and advance {len(selected_tasks)} total tasks"
        else:
            sprint_goal = "No tasks available for sprint"
        
        return {
            "sprint_duration": request.sprint_duration,
            "team_capacity_hours": team_capacity,
            "selected_tasks": [
                {
                    "id": t['id'],
                    "title": t['title'],
                    "priority": t['priority'],
                    "estimated_hours": t.get('estimatedHours') or 8
                }
                for t in selected_tasks
            ],
            "total_hours": total_hours,
            "capacity_utilization": round((total_hours / team_capacity * 100), 2),
            "sprint_goal": sprint_goal,
            "recommendations": [
                "Review task estimates for accuracy",
                "Consider pair programming for complex items",
                "Schedule daily standups for coordination"
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/estimate-effort")
async def estimate_effort(request: EffortEstimationRequest):
    """Estimate effort for a new task"""
    try:
        # Simplified estimation based on keywords and patterns
        complexity_keywords = {
            "simple": ["update", "fix", "change", "modify", "adjust"],
            "medium": ["create", "implement", "develop", "integrate"],
            "complex": ["redesign", "architect", "migrate", "refactor", "optimize"]
        }
        
        # Analyze task description
        description_lower = (request.task_title + " " + request.task_description).lower()
        
        complexity = "medium"
        for level, keywords in complexity_keywords.items():
            if any(keyword in description_lower for keyword in keywords):
                complexity = level
                break
        
        # Base estimates
        estimates = {
            "simple": (2, 4, 8),
            "medium": (4, 8, 16),
            "complex": (8, 16, 32)
        }
        
        min_hours, likely_hours, max_hours = estimates[complexity]
        
        # Adjust based on tags
        if "frontend" in request.tags:
            likely_hours *= 1.2
        if "backend" in request.tags:
            likely_hours *= 1.3
        if "testing" in request.tags:
            likely_hours *= 0.8
        
        confidence = "high" if complexity == "simple" else "medium" if complexity == "medium" else "low"
        
        return {
            "task_title": request.task_title,
            "estimated_hours": round(likely_hours, 1),
            "confidence": confidence,
            "range": {
                "min": round(min_hours, 1),
                "max": round(max_hours, 1)
            },
            "complexity": complexity,
            "factors": [
                f"Complexity assessed as {complexity}",
                f"Tags considered: {', '.join(request.tags) if request.tags else 'none'}",
                "Historical data pattern matching applied"
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate-report")
async def generate_status_report(request: StatusReportRequest):
    """Generate project status report"""
    try:
        # Fetch project data
        project = fetch_project(request.project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        tasks = fetch_project_tasks(request.project_id)
        time_entries = fetch_time_entries(request.project_id, request.period_days)
        
        # Calculate metrics
        completed_recently = [
            t for t in tasks 
            if t['status'] == 'done' 
            # Simplified: assume recently completed
        ][:5]
        
        in_progress = [
            t for t in tasks 
            if t['status'] in ['in-progress', 'review']
        ][:5]
        
        blockers = [
            t for t in tasks 
            if 'blocked' in (t.get('tags') or '').lower()
        ]
        
        # Hours worked in period
        total_hours = sum(entry['hours'] for entry in time_entries)
        
        # Generate report based on audience
        if request.audience == "client":
            summary = f"Project '{project['name']}' is progressing well with {len(completed_recently)} tasks completed this period."
            focus = "deliverables and milestones"
        elif request.audience == "management":
            summary = f"Team logged {total_hours:.1f} hours with {len(in_progress)} tasks in progress."
            focus = "resource utilization and risks"
        else:  # team
            summary = f"Sprint progress: {len(completed_recently)} done, {len(in_progress)} in progress, {len(blockers)} blocked."
            focus = "task details and blockers"
        
        return {
            "project_name": project['name'],
            "period": f"Last {request.period_days} days",
            "audience": request.audience,
            "summary": summary,
            "achievements": [t['title'] for t in completed_recently],
            "in_progress": [t['title'] for t in in_progress],
            "blockers": [t['title'] for t in blockers],
            "next_steps": [
                "Continue current sprint tasks",
                "Address blocked items",
                "Prepare for upcoming milestone"
            ],
            "metrics": {
                "tasks_completed": len(completed_recently),
                "tasks_in_progress": len(in_progress),
                "hours_logged": round(total_hours, 1),
                "team_size": len(set(e['userId'] for e in time_entries))
            },
            "focus_area": focus
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# CLI interface
if __name__ == "__main__":
    import uvicorn
    
    print(f"Starting Leantime BAML Integration API on {Config.API_HOST}:{Config.API_PORT}")
    print(f"Documentation available at http://{Config.API_HOST}:{Config.API_PORT}/docs")
    
    uvicorn.run(
        app,
        host=Config.API_HOST,
        port=Config.API_PORT,
        reload=False
    )