#!/usr/bin/env python3
"""
Security Manager Agent - Odoo-Leantime MCP Integration
Comprehensive security audit and vulnerability assessment
"""

import json
import os
import re
import hashlib
import asyncio
from pathlib import Path
from datetime import datetime
import structlog

logger = structlog.get_logger()

class SecurityManagerAgent:
    """Security Manager specialized agent for comprehensive security audit"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.security_findings = {
            "vulnerabilities": [],
            "recommendations": [],
            "compliance": {},
            "risk_assessment": {}
        }

    async def audit_authentication_security(self):
        """Audit authentication and authorization mechanisms"""
        logger.info("Starting authentication security audit")

        findings = {
            "authentication_methods": [],
            "vulnerabilities": [],
            "recommendations": []
        }

        # Analyze bridge server authentication
        bridge_server = self.project_root / "odoo-bridge" / "odoo_bridge_server.py"
        if bridge_server.exists():
            with open(bridge_server) as f:
                content = f.read()

            # Check for authentication mechanisms
            if "HTTPBearer" in content:
                findings["authentication_methods"].append("Bearer Token Authentication")

            if "JWT" in content or "jwt" in content:
                findings["authentication_methods"].append("JWT Tokens")

            if "verify_api_key" in content:
                findings["authentication_methods"].append("API Key Validation")

            # Check for security vulnerabilities
            if "credentials.credentials" in content:
                if "!=" in content and "settings.BRIDGE_API_KEY" in content:
                    findings["recommendations"].append("API key validation implemented correctly")
                else:
                    findings["vulnerabilities"].append({
                        "type": "Weak API Key Validation",
                        "severity": "HIGH",
                        "description": "API key validation may be insufficient"
                    })

            # Check for password handling
            if "password" in content.lower():
                if "hash" not in content.lower() and "encrypt" not in content.lower():
                    findings["vulnerabilities"].append({
                        "type": "Plaintext Password Storage",
                        "severity": "CRITICAL",
                        "description": "Passwords may be stored in plaintext"
                    })

            # Check for SQL injection protection
            if "execute(" in content:
                if "%s" in content:
                    findings["recommendations"].append("Parameterized queries used for SQL injection protection")
                else:
                    findings["vulnerabilities"].append({
                        "type": "SQL Injection Risk",
                        "severity": "HIGH",
                        "description": "Potential SQL injection vulnerability"
                    })

        self.security_findings["authentication"] = findings
        logger.info("Authentication security audit completed", findings=findings)
        return findings

    async def audit_input_validation(self):
        """Audit input validation and sanitization"""
        logger.info("Starting input validation audit")

        findings = {
            "validation_mechanisms": [],
            "vulnerabilities": [],
            "recommendations": []
        }

        # Check bridge server for input validation
        bridge_server = self.project_root / "odoo-bridge" / "odoo_bridge_server.py"
        if bridge_server.exists():
            with open(bridge_server) as f:
                content = f.read()

            # Check for input sanitization
            if "html.escape" in content:
                findings["validation_mechanisms"].append("HTML Escaping")
                findings["recommendations"].append("HTML escaping implemented for XSS protection")

            # Check for Pydantic validation
            if "BaseModel" in content and "Field" in content:
                findings["validation_mechanisms"].append("Pydantic Data Validation")
                findings["recommendations"].append("Pydantic models used for input validation")

            # Check for dangerous patterns
            dangerous_patterns = [
                (r"eval\(", "Code Injection via eval()"),
                (r"exec\(", "Code Injection via exec()"),
                (r"__import__", "Dynamic Import Risk"),
                (r"pickle\.loads", "Pickle Deserialization Risk"),
                (r"yaml\.load\(", "YAML Deserialization Risk")
            ]

            for pattern, description in dangerous_patterns:
                if re.search(pattern, content):
                    findings["vulnerabilities"].append({
                        "type": "Code Injection Risk",
                        "severity": "CRITICAL",
                        "description": description
                    })

            # Check for file system access
            if "open(" in content or "Path(" in content:
                if "safe" not in content.lower() and "validate" not in content.lower():
                    findings["vulnerabilities"].append({
                        "type": "Path Traversal Risk",
                        "severity": "MEDIUM",
                        "description": "File system access without proper validation"
                    })

        self.security_findings["input_validation"] = findings
        logger.info("Input validation audit completed", findings=findings)
        return findings

    async def audit_network_security(self):
        """Audit network security configurations"""
        logger.info("Starting network security audit")

        findings = {
            "network_configurations": [],
            "vulnerabilities": [],
            "recommendations": []
        }

        # Check CORS configuration
        bridge_server = self.project_root / "odoo-bridge" / "odoo_bridge_server.py"
        if bridge_server.exists():
            with open(bridge_server) as f:
                content = f.read()

            if "CORSMiddleware" in content:
                if 'allow_origins=["*"]' in content:
                    findings["vulnerabilities"].append({
                        "type": "Overly Permissive CORS",
                        "severity": "MEDIUM",
                        "description": "CORS allows all origins (*)"
                    })
                    findings["recommendations"].append("Restrict CORS origins to specific domains")
                else:
                    findings["network_configurations"].append("CORS Properly Configured")

            # Check for HTTPS enforcement
            if "uvicorn.run" in content:
                if "ssl_keyfile" not in content and "ssl_certfile" not in content:
                    findings["vulnerabilities"].append({
                        "type": "No HTTPS Enforcement",
                        "severity": "HIGH",
                        "description": "Server runs without HTTPS/TLS encryption"
                    })
                    findings["recommendations"].append("Enable HTTPS with SSL certificates")

        # Check Docker network configurations
        compose_files = list(self.project_root.glob("docker-compose*.yml"))
        for compose_file in compose_files:
            with open(compose_file) as f:
                content = f.read()

            if "networks:" in content:
                findings["network_configurations"].append(f"Docker network defined in {compose_file.name}")

            if "ports:" in content and "expose:" not in content:
                findings["vulnerabilities"].append({
                    "type": "Unnecessary Port Exposure",
                    "severity": "MEDIUM",
                    "description": f"Ports exposed without explicit exposure in {compose_file.name}"
                })

        self.security_findings["network_security"] = findings
        logger.info("Network security audit completed", findings=findings)
        return findings

    async def audit_secrets_management(self):
        """Audit secrets and sensitive data management"""
        logger.info("Starting secrets management audit")

        findings = {
            "secrets_found": [],
            "vulnerabilities": [],
            "recommendations": []
        }

        # Check .env file for secrets
        env_file = self.project_root / ".env"
        if env_file.exists():
            with open(env_file) as f:
                content = f.read()

            # Pattern matching for potential secrets
            secret_patterns = [
                (r"password\s*=\s*['\"]([^'\"]+)['\"]", "Database Password"),
                (r"api_key\s*=\s*['\"]([^'\"]+)['\"]", "API Key"),
                (r"secret\s*=\s*['\"]([^'\"]+)['\"]", "Secret Key"),
                (r"token\s*=\s*['\"]([^'\"]+)['\"]", "Access Token")
            ]

            for pattern, secret_type in secret_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    secret_value = match.group(1)
                    findings["secrets_found"].append({
                        "type": secret_type,
                        "value_hash": hashlib.sha256(secret_value.encode()).hexdigest()[:16],
                        "strength": self._assess_secret_strength(secret_value)
                    })

        # Check source code for hardcoded secrets
        python_files = list(self.project_root.glob("**/*.py"))
        for py_file in python_files:
            try:
                with open(py_file, encoding='utf-8') as f:
                    content = f.read()

                # Look for hardcoded secrets
                if re.search(r'password\s*=\s*["\'][^"\']{8,}["\']', content, re.IGNORECASE):
                    findings["vulnerabilities"].append({
                        "type": "Hardcoded Password",
                        "severity": "HIGH",
                        "file": str(py_file.relative_to(self.project_root)),
                        "description": "Password hardcoded in source code"
                    })

                if re.search(r'api[_-]?key\s*=\s*["\'][^"\']{20,}["\']', content, re.IGNORECASE):
                    findings["vulnerabilities"].append({
                        "type": "Hardcoded API Key",
                        "severity": "HIGH",
                        "file": str(py_file.relative_to(self.project_root)),
                        "description": "API key hardcoded in source code"
                    })

            except Exception as e:
                logger.warning("Could not read file", file=str(py_file), error=str(e))

        # Recommendations
        findings["recommendations"].extend([
            "Use environment variables for all secrets",
            "Implement secret rotation mechanisms",
            "Use strong, randomly generated secrets",
            "Consider using secret management services",
            "Never commit secrets to version control"
        ])

        self.security_findings["secrets_management"] = findings
        logger.info("Secrets management audit completed", findings=findings)
        return findings

    def _assess_secret_strength(self, secret: str) -> str:
        """Assess the strength of a secret"""
        if len(secret) < 8:
            return "WEAK"
        elif len(secret) < 16:
            return "MEDIUM"
        elif len(secret) >= 32 and any(c.isdigit() for c in secret) and any(c.isalpha() for c in secret):
            return "STRONG"
        else:
            return "MEDIUM"

    async def audit_database_security(self):
        """Audit database security configurations"""
        logger.info("Starting database security audit")

        findings = {
            "configurations": [],
            "vulnerabilities": [],
            "recommendations": []
        }

        # Check database connection security
        bridge_server = self.project_root / "odoo-bridge" / "odoo_bridge_server.py"
        if bridge_server.exists():
            with open(bridge_server) as f:
                content = f.read()

            # Check for connection pooling
            if "connection" in content.lower() and "pool" in content.lower():
                findings["configurations"].append("Connection pooling implemented")
            else:
                findings["recommendations"].append("Implement database connection pooling")

            # Check for SQL injection protection
            if "execute(" in content and "%s" in content:
                findings["configurations"].append("Parameterized queries used")
            else:
                findings["vulnerabilities"].append({
                    "type": "SQL Injection Risk",
                    "severity": "HIGH",
                    "description": "Non-parameterized queries may be vulnerable"
                })

            # Check for database encryption
            if "ssl" not in content.lower() and "tls" not in content.lower():
                findings["vulnerabilities"].append({
                    "type": "Unencrypted Database Connection",
                    "severity": "MEDIUM",
                    "description": "Database connections may not be encrypted"
                })

        # Check Docker database configurations
        compose_files = list(self.project_root.glob("docker-compose*.yml"))
        for compose_file in compose_files:
            if "mysql" in compose_file.name.lower() or "postgres" in compose_file.name.lower():
                with open(compose_file) as f:
                    content = f.read()

                if "MYSQL_ROOT_PASSWORD" in content:
                    findings["configurations"].append("MySQL root password configured")

                if "volumes:" in content:
                    findings["configurations"].append("Database persistence configured")

        findings["recommendations"].extend([
            "Enable database SSL/TLS encryption",
            "Use strong database passwords",
            "Implement database backup encryption",
            "Regular security updates for database",
            "Principle of least privilege for database users"
        ])

        self.security_findings["database_security"] = findings
        logger.info("Database security audit completed", findings=findings)
        return findings

    async def generate_risk_assessment(self):
        """Generate overall risk assessment"""
        logger.info("Generating risk assessment")

        # Calculate risk scores
        critical_count = sum(1 for finding in self.security_findings.values()
                           if isinstance(finding, dict) and
                           any(v.get("severity") == "CRITICAL" for v in finding.get("vulnerabilities", [])))

        high_count = sum(1 for finding in self.security_findings.values()
                        if isinstance(finding, dict) and
                        any(v.get("severity") == "HIGH" for v in finding.get("vulnerabilities", [])))

        medium_count = sum(1 for finding in self.security_findings.values()
                          if isinstance(finding, dict) and
                          any(v.get("severity") == "MEDIUM" for v in finding.get("vulnerabilities", [])))

        # Calculate overall risk score
        risk_score = (critical_count * 10) + (high_count * 5) + (medium_count * 2)

        risk_level = "LOW"
        if risk_score >= 20:
            risk_level = "CRITICAL"
        elif risk_score >= 10:
            risk_level = "HIGH"
        elif risk_score >= 5:
            risk_level = "MEDIUM"

        risk_assessment = {
            "overall_risk_level": risk_level,
            "risk_score": risk_score,
            "vulnerability_counts": {
                "critical": critical_count,
                "high": high_count,
                "medium": medium_count
            },
            "priority_actions": self._generate_priority_actions(),
            "compliance_status": self._assess_compliance(),
            "recommendations": self._generate_security_recommendations()
        }

        self.security_findings["risk_assessment"] = risk_assessment
        logger.info("Risk assessment completed", assessment=risk_assessment)
        return risk_assessment

    def _generate_priority_actions(self) -> list:
        """Generate priority actions based on findings"""
        return [
            "Enable HTTPS/TLS encryption for all services",
            "Implement proper CORS origin restrictions",
            "Audit and strengthen all secrets and passwords",
            "Enable database connection encryption",
            "Implement comprehensive input validation",
            "Set up security monitoring and alerting"
        ]

    def _assess_compliance(self) -> dict:
        """Assess compliance with security standards"""
        return {
            "owasp_top_10": {
                "injection": "PARTIAL",  # Some parameterized queries
                "broken_authentication": "NEEDS_IMPROVEMENT",
                "sensitive_data_exposure": "NEEDS_IMPROVEMENT",
                "xml_external_entities": "NOT_APPLICABLE",
                "broken_access_control": "PARTIAL",
                "security_misconfiguration": "NEEDS_IMPROVEMENT",
                "cross_site_scripting": "PARTIAL",  # HTML escaping implemented
                "insecure_deserialization": "UNKNOWN",
                "known_vulnerabilities": "NEEDS_ASSESSMENT",
                "insufficient_logging": "NEEDS_IMPROVEMENT"
            },
            "gdpr": {
                "data_encryption": "NEEDS_IMPROVEMENT",
                "access_control": "PARTIAL",
                "audit_logging": "NEEDS_IMPROVEMENT",
                "data_minimization": "UNKNOWN"
            }
        }

    def _generate_security_recommendations(self) -> list:
        """Generate comprehensive security recommendations"""
        return [
            "Implement end-to-end encryption for all data in transit",
            "Use secrets management service (Azure Key Vault, AWS Secrets Manager)",
            "Enable comprehensive audit logging",
            "Implement rate limiting and DDoS protection",
            "Regular security vulnerability scanning",
            "Security headers implementation (HSTS, CSP, etc.)",
            "Multi-factor authentication for administrative access",
            "Regular backup and disaster recovery testing",
            "Security training for development team",
            "Penetration testing before production deployment"
        ]

    async def save_audit_results(self):
        """Save security audit results to file"""
        output_dir = self.project_root / ".swarm" / "security"
        output_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().isoformat()

        # Save security audit
        audit_file = output_dir / f"security_audit_{timestamp.replace(':', '-')}.json"
        with open(audit_file, 'w') as f:
            json.dump({
                "timestamp": timestamp,
                "agent": "SecurityManager",
                "audit_results": self.security_findings
            }, f, indent=2)

        logger.info("Security audit results saved", file=str(audit_file))
        return str(audit_file)

    async def execute_security_audit(self):
        """Execute complete security audit"""
        logger.info("Starting Security Manager Agent execution")

        try:
            # Run security audits
            await self.audit_authentication_security()
            await self.audit_input_validation()
            await self.audit_network_security()
            await self.audit_secrets_management()
            await self.audit_database_security()
            await self.generate_risk_assessment()

            # Save results
            output_file = await self.save_audit_results()

            risk_level = self.security_findings["risk_assessment"]["overall_risk_level"]
            priority_actions = self.security_findings["risk_assessment"]["priority_actions"]

            logger.info("Security Manager Agent completed successfully",
                       output=output_file,
                       risk_level=risk_level)

            return {
                "status": "completed",
                "agent": "SecurityManager",
                "output_file": output_file,
                "risk_level": risk_level,
                "priority_actions": priority_actions[:3],  # Top 3 priorities
                "next_actions": [
                    "Implement priority security fixes",
                    "Enable HTTPS/TLS encryption",
                    "Strengthen secrets management",
                    "Deploy security monitoring"
                ]
            }

        except Exception as e:
            logger.error("Security Manager Agent failed", error=str(e))
            return {
                "status": "failed",
                "agent": "SecurityManager",
                "error": str(e)
            }

async def main():
    """Main execution for Security Manager Agent"""
    project_root = r"C:\Users\<USER>\projects\docker-leantime"
    agent = SecurityManagerAgent(project_root)
    result = await agent.execute_security_audit()
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())