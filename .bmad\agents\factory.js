/**
 * BMAD-Claude Agent Factory
 * Dynamic agent creation system integrating BMAD METHOD with Claude Code patterns
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

class AgentFactory {
  constructor() {
    this.agentRegistry = new Map();
    this.activeAgents = new Map();
    this.templateCache = new Map();
    this.loadCoreAgents();
  }

  /**
   * Load core BMAD and Claude Flow agent definitions
   */
  async loadCoreAgents() {
    // BMAD Planning Phase Agents
    this.registerAgent('analyst', {
      type: 'planning',
      role: 'Requirements Analyst',
      capabilities: ['requirements-gathering', 'stakeholder-analysis', 'use-case-definition'],
      template: 'bmad-analyst',
      prompt: 'You are a Requirements Analyst following BMAD methodology. Analyze requirements and create detailed specifications.'
    });

    this.registerAgent('product-manager', {
      type: 'planning',
      role: 'Product Manager',
      capabilities: ['prd-creation', 'feature-prioritization', 'roadmap-planning'],
      template: 'bmad-pm',
      prompt: 'You are a Product Manager creating comprehensive PRDs with clear success metrics and user stories.'
    });

    this.registerAgent('architect', {
      type: 'planning',
      role: 'System Architect',
      capabilities: ['architecture-design', 'tech-stack-selection', 'system-integration'],
      template: 'bmad-architect',
      prompt: 'You are a System Architect designing scalable, maintainable architectures following best practices.'
    });

    // BMAD Development Phase Agents
    this.registerAgent('scrum-master', {
      type: 'development',
      role: 'Scrum Master',
      capabilities: ['story-creation', 'context-engineering', 'sprint-planning'],
      template: 'bmad-scrum',
      prompt: 'You are a Scrum Master creating context-engineered development stories with embedded implementation details.'
    });

    // Claude Flow Pattern Agents
    this.registerAgent('swarm-coordinator', {
      type: 'orchestration',
      role: 'Swarm Coordinator',
      capabilities: ['multi-agent-coordination', 'task-distribution', 'consensus-building'],
      template: 'claude-swarm',
      prompt: 'You coordinate multiple AI agents in a swarm pattern for complex tasks.'
    });

    this.registerAgent('code-reviewer', {
      type: 'development',
      role: 'Code Reviewer',
      capabilities: ['code-analysis', 'best-practices-enforcement', 'security-review'],
      template: 'claude-reviewer',
      prompt: 'You review code for quality, security, and adherence to project standards.'
    });

    // Awesome-Claude-Code Pattern Agents
    this.registerAgent('flow-orchestrator', {
      type: 'orchestration',
      role: 'Flow Orchestrator',
      capabilities: ['workflow-automation', 'pipeline-management', 'task-sequencing'],
      template: 'claude-flow-orchestrator',
      prompt: 'You orchestrate complex workflows using Claude Code Flow patterns.'
    });

    this.registerAgent('context-loader', {
      type: 'utility',
      role: 'Context Loader',
      capabilities: ['context-preparation', 'document-flattening', 'knowledge-extraction'],
      template: 'context-loader',
      prompt: 'You prepare and load context for AI consumption using BMAD context-engineering principles.'
    });
  }

  /**
   * Register a new agent type
   */
  registerAgent(name, config) {
    this.agentRegistry.set(name, {
      ...config,
      created: new Date(),
      instances: 0
    });
  }

  /**
   * Create an agent instance dynamically
   */
  async createAgent(type, options = {}) {
    const agentDef = this.agentRegistry.get(type);
    if (!agentDef) {
      throw new Error(`Unknown agent type: ${type}`);
    }

    const agentId = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const agent = {
      id: agentId,
      type,
      ...agentDef,
      options,
      status: 'initializing',
      createdAt: new Date(),
      context: await this.loadAgentContext(type, options)
    };

    // Spawn using Claude Flow if available
    if (options.useClaudeFlow) {
      await this.spawnWithClaudeFlow(agent);
    }

    this.activeAgents.set(agentId, agent);
    agentDef.instances++;
    
    return agent;
  }

  /**
   * Load agent-specific context
   */
  async loadAgentContext(type, options) {
    const context = {
      bmadConfig: await this.loadBMADConfig(),
      projectContext: options.projectContext || {},
      templates: []
    };

    // Load relevant templates for the agent type
    const agentDef = this.agentRegistry.get(type);
    if (agentDef && agentDef.template) {
      const templatePath = path.join(__dirname, 'templates', `${agentDef.template}.md`);
      try {
        const template = await fs.readFile(templatePath, 'utf-8');
        context.templates.push(template);
      } catch (error) {
        console.warn(`Template not found: ${templatePath}`);
      }
    }

    return context;
  }

  /**
   * Spawn agent using Claude Flow
   */
  async spawnWithClaudeFlow(agent) {
    return new Promise((resolve, reject) => {
      const command = `npx claude-flow agent spawn --type "${agent.type}" --task "${agent.options.task || 'default'}"`;
      
      const process = spawn('npx', [
        'claude-flow',
        'agent',
        'spawn',
        '--type', agent.type,
        '--task', agent.options.task || 'default',
        '--id', agent.id
      ], {
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          agent.status = 'active';
          agent.flowProcess = process;
          resolve(output);
        } else {
          agent.status = 'failed';
          reject(new Error(`Failed to spawn agent: ${output}`));
        }
      });
    });
  }

  /**
   * Create multiple agents for a swarm pattern
   */
  async createSwarm(agents, topology = 'mesh') {
    const swarmId = `swarm-${Date.now()}`;
    const swarm = {
      id: swarmId,
      topology,
      agents: [],
      createdAt: new Date()
    };

    for (const agentConfig of agents) {
      const agent = await this.createAgent(agentConfig.type, {
        ...agentConfig.options,
        swarmId,
        topology
      });
      swarm.agents.push(agent);
    }

    return swarm;
  }

  /**
   * Load BMAD configuration
   */
  async loadBMADConfig() {
    try {
      const configPath = path.join(process.cwd(), '.bmad', 'config.json');
      const config = await fs.readFile(configPath, 'utf-8');
      return JSON.parse(config);
    } catch (error) {
      return {};
    }
  }

  /**
   * Generate context-engineered story
   */
  async generateStory(requirements, architecture, options = {}) {
    const scrumMaster = await this.createAgent('scrum-master', {
      task: 'Generate context-engineered development story',
      projectContext: {
        requirements,
        architecture,
        ...options
      }
    });

    const story = {
      id: `story-${Date.now()}`,
      title: options.title || 'Development Story',
      context: {
        requirements: requirements,
        architecture: architecture,
        implementationDetails: options.implementation || {},
        acceptanceCriteria: options.criteria || []
      },
      generatedBy: scrumMaster.id,
      createdAt: new Date()
    };

    return story;
  }

  /**
   * List all available agent types
   */
  getAvailableAgents() {
    return Array.from(this.agentRegistry.entries()).map(([name, config]) => ({
      name,
      type: config.type,
      role: config.role,
      capabilities: config.capabilities,
      instances: config.instances
    }));
  }

  /**
   * Get active agents
   */
  getActiveAgents() {
    return Array.from(this.activeAgents.values());
  }

  /**
   * Terminate an agent
   */
  async terminateAgent(agentId) {
    const agent = this.activeAgents.get(agentId);
    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    if (agent.flowProcess) {
      agent.flowProcess.kill();
    }

    agent.status = 'terminated';
    this.activeAgents.delete(agentId);
    
    const agentDef = this.agentRegistry.get(agent.type);
    if (agentDef) {
      agentDef.instances--;
    }

    return agent;
  }
}

// Export singleton instance
module.exports = new AgentFactory();