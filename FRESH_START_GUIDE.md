# 🚀 Fresh Start Leantime Deployment Guide

## 🎯 Overview

This guide will give you a completely fresh Leantime installation with **working email functionality** from day one. All the email configuration issues have been resolved and baked into this deployment.

## ✅ What This Fixes

- ✅ **Email Port Mismatch**: Fixed SMTP port (2525)
- ✅ **Domain Consistency**: All using dxbmeta.com
- ✅ **Resend API Integration**: Working email service
- ✅ **Database Issues**: Fresh, clean database
- ✅ **Configuration Problems**: Proper environment variables
- ✅ **Network Connectivity**: Containers properly networked

## 📋 Prerequisites

- SSH access to your DigitalOcean server (*************)
- Podman installed and running
- Domain admin.dxbmeta.com pointing to your server

## 🚀 Deployment Steps

### Step 1: Upload Scripts to Server

Upload these files to your DigitalOcean server:
- `fresh-start-deploy.sh`
- `verify-fresh-deployment.sh`

```bash
# SSH to your server
ssh root@*************

# Make scripts executable
chmod +x fresh-start-deploy.sh
chmod +x verify-fresh-deployment.sh
```

### Step 2: Run Fresh Deployment

```bash
# Run the fresh start deployment
./fresh-start-deploy.sh
```

**What this does:**
1. 🧹 **Clean Slate**: Removes all old containers and volumes
2. 🌐 **Network Setup**: Creates proper container network
3. 🗄️ **Database**: Deploys fresh MySQL with correct credentials
4. 📧 **Email Service**: Deploys working email service with Resend API
5. 🏢 **Leantime**: Deploys application with fixed email configuration
6. 🧪 **Testing**: Sends test email to verify functionality

**Expected Duration**: 5-10 minutes

### Step 3: Verify Deployment

```bash
# Verify everything is working
./verify-fresh-deployment.sh
```

**Expected Results:**
- ✅ 7-8 tests should pass
- ✅ Test email <NAME_EMAIL>
- ✅ All containers running
- ✅ Web interface accessible

### Step 4: Complete Leantime Setup

1. **Access Leantime**: Go to https://admin.dxbmeta.com
2. **Complete Installation**: Follow the setup wizard
3. **Create Admin User**: 
   - Email: <EMAIL>
   - Password: admin123 (or your choice)
4. **Verify Login**: Ensure you can log in

### Step 5: Test Email Functionality

1. **Test Password Reset**:
   - Log out of Leantime
   - Click "Forgot Password"
   - Enter: <EMAIL>
   - Submit and check email

2. **Expected Result**:
   - Email arrives within 2 minutes
   - From: <EMAIL>
   - Contains working reset link

## 📧 Email Configuration Details

### Service Architecture
```
Leantime → Email Service → Resend API → Email Delivery
   ↓           ↓              ↓            ↓
Port 8090   Port 2525    HTTPS API    <EMAIL>
```

### Key Settings
- **SMTP Host**: leantime-email-service
- **SMTP Port**: 2525
- **From Address**: <EMAIL>
- **API Provider**: Resend
- **Authentication**: Working API key

## 🔧 Management Commands

### Container Management
```bash
# View all containers
podman ps

# Check logs
podman logs leantime_podman
podman logs leantime-email-service
podman logs mysql_leantime_podman

# Restart services
podman restart leantime_podman
podman restart leantime-email-service
```

### Email Testing
```bash
# Test email service health
curl http://localhost:3001/health

# Send test email
curl -X POST http://localhost:3001/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "html": "<p>Test message</p>"
  }'
```

### Database Access
```bash
# Connect to database
podman exec -it mysql_leantime_podman mysql -u lean -pSecureDBPass2024! leantime

# Check tables
podman exec mysql_leantime_podman mysql -u lean -pSecureDBPass2024! leantime -e "SHOW TABLES;"
```

## 🎯 Success Criteria

### ✅ Deployment Successful If:
1. All containers running without errors
2. Web interface accessible at https://admin.dxbmeta.com
3. Database connection working
4. Email service health check passes
5. Test email delivered successfully
6. Leantime installation completes without issues
7. Password reset emails work

### ❌ Troubleshooting If Issues:
1. Check container logs for errors
2. Verify network connectivity between containers
3. Test email service independently
4. Ensure domain DNS is properly configured
5. Check firewall/security group settings

## 📊 Expected Timeline

- **Deployment**: 5-10 minutes
- **Verification**: 2-3 minutes  
- **Leantime Setup**: 5 minutes
- **Email Testing**: 2 minutes
- **Total**: ~15-20 minutes

## 🎉 Benefits of Fresh Start

1. **Clean Configuration**: No legacy issues
2. **Working Email**: Password reset functional from day 1
3. **Known State**: Everything properly configured
4. **Easy Maintenance**: Clear container structure
5. **Scalable**: Proper service separation

## 📞 Support

If you encounter issues:

1. **Check Logs**: Use the management commands above
2. **Re-run Verification**: `./verify-fresh-deployment.sh`
3. **Container Status**: `podman ps` and `podman logs <container>`
4. **Network Issues**: Check if containers can communicate
5. **Email Problems**: Test email service independently

## 🔄 Rollback Plan

If needed, you can always:
1. Stop containers: `podman stop $(podman ps -q)`
2. Remove containers: `podman rm $(podman ps -aq)`
3. Remove volumes: `podman volume prune`
4. Re-run deployment script

---

**🚀 Ready to deploy? Run `./fresh-start-deploy.sh` on your DigitalOcean server!**
