{"name": "resend-api-mailer", "version": "1.0.0", "description": "HTTP API-based email service using Resend", "main": "resend-api-mailer.js", "scripts": {"start": "node resend-api-mailer.js", "test": "curl -f http://localhost:2525/health"}, "dependencies": {"axios": "^1.12.2", "cheerio": "^1.1.2", "dotenv": "^17.2.2", "express": "^4.18.2"}, "keywords": ["resend", "email", "api", "leantime"], "author": "<PERSON>", "license": "MIT"}