# Critical Security Fixes Required

## IMMEDIATE ACTION REQUIRED ⚠️

These security vulnerabilities must be fixed before production deployment:

### 1. Remove Hardcoded Credentials
**File**: `email-service/server.js:105`
**Current**: `const password = process.env.SMTP_PASSWORD || 'leantime-email-service';`
**Fix**:
```javascript
const password = process.env.SMTP_PASSWORD;
if (!password) {
    logger.error('SMTP_PASSWORD environment variable is required');
    process.exit(1);
}
```

### 2. Enable TLS Certificate Validation  
**File**: `email-service/providers/resend.js:23-24`
**Current**: `rejectUnauthorized: false`
**Fix**:
```javascript
tls: {
    rejectUnauthorized: true,
    minVersion: 'TLSv1.2',
    ciphers: 'ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS'
}
```

### 3. Add API Authentication
**File**: `email-service/server.js` (before route definitions)
**Add**:
```javascript
const authenticateAPI = (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    const validKey = process.env.EMAIL_API_KEY;
    
    if (!validKey) {
        return res.status(500).json({ error: 'API key not configured' });
    }
    
    if (!apiKey || apiKey !== validKey) {
        return res.status(401).json({ error: 'Invalid API key' });
    }
    
    next();
};

// Protect all API routes
app.use('/api/', authenticateAPI);
```

### 4. Fix XSS Vulnerability
**File**: `email-service/server.js:285`
**Add dependency**: `npm install escape-html`
**Fix**:
```javascript
const escapeHtml = require('escape-html');
// In parseRawEmail function:
html: `<p>${escapeHtml(body.trim()).replace(/\n/g, '<br>')}</p>`
```

### 5. Secure SMTP Server
**File**: `email-service/server.js:98-101`
**Fix**:
```javascript
const smtpServer = new SMTPServer({
    secure: true,
    authOptional: false,
    allowInsecureAuth: false,
    requireTLS: true,
    // Add these security options
    disabledCommands: ['STARTTLS'], // Force TLS from start
    hideSTARTTLS: true,
});
```

### 6. Add Environment Variable Validation
**File**: `email-service/server.js` (at startup)
**Add**:
```javascript
const requiredEnvVars = [
    'REDIS_URL',
    'DEFAULT_FROM_EMAIL',
    'SMTP_PASSWORD',
    'EMAIL_API_KEY'
];

for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
        logger.error(`Required environment variable ${envVar} is not set`);
        process.exit(1);
    }
}
```

### 7. Update .env Configuration
**Add to `.env` file**:
```env
# Email Service Security
EMAIL_API_KEY=your_secure_api_key_here_minimum_32_chars
SMTP_PASSWORD=your_secure_smtp_password_here

# Remove any default passwords from code
```

## Implementation Steps

1. **Apply all code fixes above**
2. **Generate secure API key**: `openssl rand -hex 32`
3. **Update .env with secure passwords**
4. **Test authentication**: `curl -H "x-api-key: your_key" http://localhost:3000/api/send`
5. **Rebuild and restart containers**

## Verification

```bash
# 1. Verify no hardcoded credentials
grep -r "leantime-email-service" email-service/
# Should return no results

# 2. Test API authentication
curl http://localhost:3000/api/send
# Should return 401 Unauthorized

# 3. Test with valid key
curl -H "x-api-key: your_key" -H "Content-Type: application/json" \
  -d '{"to":"<EMAIL>","subject":"test","text":"test"}' \
  http://localhost:3000/api/send
# Should return success
```

**⚠️ DO NOT DEPLOY TO PRODUCTION UNTIL ALL FIXES ARE APPLIED AND VERIFIED**