# BMAD-Claude Framework Integration

## Overview

This project now integrates the **BMAD METHOD™** with <PERSON> and Claude Flow, creating a powerful AI-driven development framework that combines structured planning with context-engineered execution.

## What is BMAD METHOD?

BMAD METHOD is an innovative development framework with two key innovations:

1. **Agentic Planning Phase**: Specialized AI agents (<PERSON>ly<PERSON>, PM, Architect) create consistent, detailed planning documents
2. **Context-Engineered Development**: All implementation context is embedded directly into development stories, eliminating information loss

## System Architecture

```
┌─────────────────────────────────────────┐
│           BMAD-Claude Framework         │
├─────────────────────────────────────────┤
│                                         │
│  ┌──────────┐  ┌──────────┐  ┌──────┐ │
│  │   BMAD   │←→│  Bridge  │←→│Claude│ │
│  │  METHOD  │  │          │  │ Flow │ │
│  └──────────┘  └──────────┘  └──────┘ │
│                                         │
│  ┌─────────────────────────────────┐   │
│  │        Agent Factory            │   │
│  │  ┌─────┐ ┌─────┐ ┌─────────┐  │   │
│  │  │Plan │ │Dev  │ │Orchestr.│  │   │
│  │  └─────┘ └─────┘ └─────────┘  │   │
│  └─────────────────────────────────┘   │
│                                         │
└─────────────────────────────────────────┘
```

## Quick Start

### 1. Initialize BMAD Project
```bash
node .bmad/workflow/commands.js init
```

### 2. Run Planning Phase
```bash
node .bmad/workflow/commands.js plan "Your project description"
```

### 3. Execute Development
```bash
node .bmad/workflow/commands.js develop --swarm
```

## Agent Types

### Planning Phase Agents
- **Analyst**: Requirements gathering and analysis
- **Product Manager**: PRD creation and feature prioritization
- **Architect**: System design and technical architecture

### Development Phase Agents
- **Scrum Master**: Context-engineered story generation
- **Developer**: Code implementation (maps to Claude Flow 'coder')
- **Code Reviewer**: Quality assurance and best practices

### Orchestration Agents
- **Swarm Coordinator**: Multi-agent coordination
- **Flow Orchestrator**: Workflow automation

## Dynamic Agent Creation

The system can create agents on-the-fly based on patterns from:
- BMAD METHOD templates
- Claude Flow agent library
- awesome-claude-code patterns

### Creating an Agent
```javascript
const agentFactory = require('.bmad/agents/factory');

// Create a single agent
const analyst = await agentFactory.createAgent('analyst', {
  task: 'Analyze user requirements',
  projectContext: { /* context */ }
});

// Create a swarm
const swarm = await agentFactory.createSwarm([
  { type: 'analyst', options: {} },
  { type: 'architect', options: {} }
], 'mesh');
```

## Context Engineering

BMAD's key innovation is embedding full context in development stories:

```javascript
{
  "story": {
    "title": "Implement user authentication",
    "context": {
      "requirements": "/* Full requirements embedded */",
      "architecture": "/* Architecture details embedded */",
      "implementation": "/* Step-by-step guide embedded */",
      "examples": "/* Code examples embedded */"
    }
  }
}
```

## Configuration

Edit `.bmad/config.json` to customize:
- Phase settings (planning, development, validation)
- Agent factory configuration
- Context engineering parameters
- Claude Flow integration settings

## Workflow Commands

### Basic Commands
```bash
bmad init                    # Initialize project
bmad plan [task]            # Run planning phase
bmad develop [--swarm]      # Execute development
bmad validate [phase]       # Validate outputs
```

### Agent Management
```bash
bmad agent create <type>    # Create agent
bmad agent list active      # List active agents
bmad agent list available   # List available types
bmad agent terminate <id>   # Terminate agent
```

## Integration with Claude Flow

The bridge automatically:
- Maps BMAD agents to Claude Flow types
- Syncs agent definitions
- Shares memory and context
- Coordinates swarm operations

### Using Claude Flow Commands
```bash
# BMAD agents work with Claude Flow
npx claude-flow agent list
npx claude-flow swarm status
```

## Benefits

1. **Eliminates Planning Inconsistency**: Structured planning with AI agents
2. **Prevents Context Loss**: All context embedded in stories
3. **Dynamic Scaling**: Create specialized agents as needed
4. **Unified Workflow**: Combines BMAD's methodology with Claude Flow's execution

## Directory Structure

```
.bmad/
├── config.json              # Main configuration
├── agents/
│   ├── factory.js          # Agent factory
│   ├── planning/           # Planning phase agents
│   ├── development/        # Development agents
│   └── orchestration/      # Orchestration agents
├── templates/              # Agent templates
├── stories/                # Context-engineered stories
├── docs/                   # Generated documents
├── workflow/
│   └── commands.js         # CLI commands
└── integration/
    └── claude-flow-bridge.js  # Integration layer
```

## Example Workflow

```bash
# 1. Initialize BMAD
node .bmad/workflow/commands.js init

# 2. Run planning with agents
node .bmad/workflow/commands.js plan "E-commerce platform"

# 3. Generate development stories
node .bmad/workflow/commands.js develop

# 4. Create implementation swarm
node .bmad/workflow/commands.js agent create swarm-coordinator

# 5. Validate results
node .bmad/workflow/commands.js validate planning
```

## Advanced Features

### Custom Agent Templates
Create custom agents by adding JSON definitions to `.bmad/agents/`:
```json
{
  "name": "Custom Agent",
  "type": "custom",
  "capabilities": ["..."],
  "prompt": { "...": "..." }
}
```

### Workflow Automation
Chain phases together:
```javascript
const bridge = require('.bmad/integration/claude-flow-bridge');
await bridge.executeBMADWorkflow('full-cycle', {
  topology: 'adaptive',
  synchronous: true
});
```

## Troubleshooting

### Claude Flow Not Found
```bash
npm install -g claude-flow@alpha
```

### Agent Creation Failed
Check `.bmad/config.json` for proper settings

### Context Too Large
Adjust `maxContextSize` in config

## Resources

- [BMAD METHOD Repository](https://github.com/bmad-code-org/BMAD-METHOD)
- [awesome-claude-code](https://github.com/hesreallyhim/awesome-claude-code)
- [Claude Flow Documentation](https://github.com/ruvnet/claude-flow)

## Support

For issues or questions:
1. Check the configuration in `.bmad/config.json`
2. Verify Claude Flow is installed: `npx claude-flow --version`
3. Review agent logs in `.bmad/logs/`

---

*BMAD-Claude Framework v1.0.0 - Context-Engineered AI Development*