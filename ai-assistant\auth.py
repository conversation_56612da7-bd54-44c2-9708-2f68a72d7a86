#!/usr/bin/env python3
"""
JWT Authentication and validation for AI Assistant System
"""

import jwt
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from aiohttp import web

from config.config import config

logger = logging.getLogger(__name__)

class JWTAuth:
    """JWT Authentication handler"""
    
    def __init__(self):
        self.secret = config.security.jwt_secret
        self.algorithm = config.security.jwt_algorithm
        self.expiry_hours = config.security.jwt_expiry_hours
    
    def generate_token(self, user_id: int, client_id: str = None) -> str:
        """Generate JWT token"""
        payload = {
            'user_id': user_id,
            'client_id': client_id,
            'exp': datetime.utcnow() + timedelta(hours=self.expiry_hours),
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, self.secret, algorithm=self.algorithm)
    
    def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate JWT token and return payload"""
        try:
            payload = jwt.decode(
                token,
                self.secret,
                algorithms=[self.algorithm]
            )
            
            # Check if client is allowed
            if config.security.allowed_clients:
                client_id = payload.get('client_id')
                if client_id not in config.security.allowed_clients:
                    logger.warning(f"Unauthorized client: {client_id}")
                    return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
    
    @staticmethod
    def extract_token(request: web.Request) -> Optional[str]:
        """Extract token from request header"""
        auth_header = request.headers.get('Authorization', '')
        
        if auth_header.startswith('Bearer '):
            return auth_header[7:]
        
        return None

def require_auth(func):
    """Decorator for routes requiring authentication"""
    async def wrapper(self, request: web.Request):
        auth = JWTAuth()
        token = auth.extract_token(request)
        
        if not token:
            return web.json_response(
                {'error': 'Missing authentication token'},
                status=401
            )
        
        payload = auth.validate_token(token)
        if not payload:
            return web.json_response(
                {'error': 'Invalid or expired token'},
                status=401
            )
        
        # Add user info to request
        request['user_id'] = payload.get('user_id')
        request['client_id'] = payload.get('client_id')
        
        return await func(self, request)
    
    return wrapper

# Global auth instance
auth = JWTAuth()