FROM python:3.11-slim

# Create non-root user
RUN groupadd -r aiuser && useradd -r -g aiuser -d /app -s /sbin/nologin aiuser

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install as root
COPY requirements-minimal.txt .
RUN pip install --no-cache-dir -r requirements-minimal.txt

# Copy application code
COPY --chown=aiuser:aiuser . .

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/uploads /app/knowledge /tmp/app && \
    chown -R aiuser:aiuser /app /tmp/app

# Switch to non-root user
USER aiuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://127.0.0.1:8444/health || exit 1

# Use secure server by default
CMD ["python", "secure_server.py"]