#!/bin/bash

# Deep Database Connectivity Diagnosis and Fix
# Comprehensive analysis of SQLSTATE[HY000] [2002] Host is unreachable

set -e

echo "🔍 Deep Database Connectivity Diagnosis"
echo "======================================="
echo "📅 $(date)"
echo "🎯 PERSISTENT: SQLSTATE[HY000] [2002] Host is unreachable"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SERVER_IP="*************"
SSH_KEY="~/.ssh/id_ed25519"
SSH_USER="root"

echo -e "${BLUE}🔐 Testing SSH Connection${NC}"
if ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "$SSH_USER@$SERVER_IP" "echo 'SSH connection successful'" 2>/dev/null; then
    echo -e "${GREEN}✅ SSH connection successful${NC}"
else
    echo -e "${RED}❌ SSH connection failed${NC}"
    exit 1
fi
echo ""

echo -e "${BLUE}🔍 Deep Diagnosis Starting${NC}"
echo "=========================="

# Execute comprehensive diagnosis on server
ssh -i "$SSH_KEY" "$SSH_USER@$SERVER_IP" << 'ENDSSH'

echo "🔍 STEP 1: Complete Container Inventory"
echo "======================================"

echo "All containers (running and stopped):"
podman ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Networks}}"
echo ""

echo "All networks:"
podman network ls
echo ""

# Find containers more comprehensively
echo "Searching for Leantime containers:"
LEANTIME_CONTAINERS=$(podman ps -a --format "{{.Names}}" | grep -i leantime || echo "NONE")
echo "$LEANTIME_CONTAINERS"
echo ""

echo "Searching for MySQL containers:"
MYSQL_CONTAINERS=$(podman ps -a --format "{{.Names}}" | grep -i mysql || echo "NONE")
echo "$MYSQL_CONTAINERS"
echo ""

# Get the first running container of each type
LEANTIME_CONTAINER=$(podman ps --format "{{.Names}}" | grep -i leantime | head -1 || echo "")
MYSQL_CONTAINER=$(podman ps --format "{{.Names}}" | grep -i mysql | head -1 || echo "")

echo "Selected containers:"
echo "  Leantime: ${LEANTIME_CONTAINER:-NOT_RUNNING}"
echo "  MySQL: ${MYSQL_CONTAINER:-NOT_RUNNING}"

if [[ -z "$LEANTIME_CONTAINER" ]]; then
    echo "❌ No running Leantime container found!"
    echo "Available Leantime containers (including stopped):"
    podman ps -a --filter "name=leantime" --format "table {{.Names}}\t{{.Status}}"
    
    # Try to start a stopped Leantime container
    STOPPED_LEANTIME=$(podman ps -a --filter "status=exited" --format "{{.Names}}" | grep -i leantime | head -1 || echo "")
    if [[ -n "$STOPPED_LEANTIME" ]]; then
        echo "🔧 Found stopped Leantime container: $STOPPED_LEANTIME"
        echo "Attempting to start it..."
        podman start "$STOPPED_LEANTIME"
        sleep 10
        LEANTIME_CONTAINER="$STOPPED_LEANTIME"
        echo "✅ Started Leantime container: $LEANTIME_CONTAINER"
    else
        echo "❌ No Leantime containers found at all"
        exit 1
    fi
fi

if [[ -z "$MYSQL_CONTAINER" ]]; then
    echo "❌ No running MySQL container found!"
    echo "Available MySQL containers (including stopped):"
    podman ps -a --filter "name=mysql" --format "table {{.Names}}\t{{.Status}}"
    
    # Try to start a stopped MySQL container
    STOPPED_MYSQL=$(podman ps -a --filter "status=exited" --format "{{.Names}}" | grep -i mysql | head -1 || echo "")
    if [[ -n "$STOPPED_MYSQL" ]]; then
        echo "🔧 Found stopped MySQL container: $STOPPED_MYSQL"
        echo "Attempting to start it..."
        podman start "$STOPPED_MYSQL"
        sleep 15
        MYSQL_CONTAINER="$STOPPED_MYSQL"
        echo "✅ Started MySQL container: $MYSQL_CONTAINER"
    else
        echo "❌ No MySQL containers found at all"
        exit 1
    fi
fi

echo ""
echo "🔍 STEP 2: Container Health Check"
echo "==============================="

echo "Leantime container health:"
podman inspect "$LEANTIME_CONTAINER" --format '{{.State.Status}} {{.State.Health.Status}}' || echo "Cannot inspect"

echo "MySQL container health:"
podman inspect "$MYSQL_CONTAINER" --format '{{.State.Status}} {{.State.Health.Status}}' || echo "Cannot inspect"

echo ""
echo "🔍 STEP 3: Container Logs Analysis"
echo "================================="

echo "Recent Leantime logs (last 20 lines):"
podman logs "$LEANTIME_CONTAINER" --tail 20 | grep -E "(error|Error|ERROR|database|Database|mysql|MySQL|connection|Connection)" || echo "No database-related errors in recent logs"
echo ""

echo "Recent MySQL logs (last 20 lines):"
podman logs "$MYSQL_CONTAINER" --tail 20 | grep -E "(error|Error|ERROR|ready|Ready|started|Started)" || echo "No relevant entries in recent logs"
echo ""

echo "🔍 STEP 4: Network Configuration Deep Dive"
echo "=========================================="

echo "Leantime container network details:"
podman inspect "$LEANTIME_CONTAINER" --format '{{range $k, $v := .NetworkSettings.Networks}}Network: {{$k}}, IP: {{$v.IPAddress}}, Gateway: {{$v.Gateway}}{{"\n"}}{{end}}'

echo "MySQL container network details:"
podman inspect "$MYSQL_CONTAINER" --format '{{range $k, $v := .NetworkSettings.Networks}}Network: {{$k}}, IP: {{$v.IPAddress}}, Gateway: {{$v.Gateway}}{{"\n"}}{{end}}'

echo ""
echo "🔍 STEP 5: Database Configuration Analysis"
echo "========================================"

echo "Leantime database environment variables:"
podman exec "$LEANTIME_CONTAINER" env | grep -E "LEAN_DB|DB_" | sort

echo ""
echo "MySQL environment variables:"
podman exec "$MYSQL_CONTAINER" env | grep -E "MYSQL_" | sort

# Extract key variables
DB_HOST=$(podman exec "$LEANTIME_CONTAINER" env | grep "LEAN_DB_HOST" | cut -d'=' -f2 || echo "localhost")
DB_USER=$(podman exec "$LEANTIME_CONTAINER" env | grep "LEAN_DB_USER" | cut -d'=' -f2 || echo "lean")
DB_PASSWORD=$(podman exec "$LEANTIME_CONTAINER" env | grep "LEAN_DB_PASSWORD" | cut -d'=' -f2 || echo "")
DB_NAME=$(podman exec "$LEANTIME_CONTAINER" env | grep "LEAN_DB_DATABASE" | cut -d'=' -f2 || echo "leantime")

echo ""
echo "Extracted configuration:"
echo "  DB_HOST: $DB_HOST"
echo "  DB_USER: $DB_USER"
echo "  DB_NAME: $DB_NAME"
echo "  DB_PASSWORD: ${DB_PASSWORD:0:8}..."

echo ""
echo "🔍 STEP 6: MySQL Service Status"
echo "============================="

echo "Checking if MySQL process is running inside container:"
MYSQL_PROCESS=$(podman exec "$MYSQL_CONTAINER" ps aux | grep mysqld | grep -v grep || echo "NOT_RUNNING")
echo "$MYSQL_PROCESS"

echo ""
echo "Testing MySQL service responsiveness:"
MYSQL_PING=$(podman exec "$MYSQL_CONTAINER" mysqladmin ping 2>/dev/null && echo "RESPONSIVE" || echo "NOT_RESPONSIVE")
echo "MySQL ping: $MYSQL_PING"

if [[ "$MYSQL_PING" == "NOT_RESPONSIVE" ]]; then
    echo "❌ MySQL service is not responding"
    echo "🔧 Attempting to restart MySQL service..."
    
    # Try to restart MySQL service inside container
    podman exec "$MYSQL_CONTAINER" service mysql restart 2>/dev/null || echo "Service restart failed"
    sleep 10
    
    # Test again
    MYSQL_PING_RETRY=$(podman exec "$MYSQL_CONTAINER" mysqladmin ping 2>/dev/null && echo "RESPONSIVE" || echo "STILL_NOT_RESPONSIVE")
    echo "MySQL ping after restart: $MYSQL_PING_RETRY"
fi

echo ""
echo "🔍 STEP 7: Network Connectivity Testing"
echo "======================================"

echo "Testing DNS resolution from Leantime to MySQL:"
DNS_TEST=$(podman exec "$LEANTIME_CONTAINER" nslookup "$DB_HOST" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "DNS resolution: $DNS_TEST"

echo ""
echo "Testing ping from Leantime to MySQL:"
PING_TEST=$(podman exec "$LEANTIME_CONTAINER" ping -c 2 "$DB_HOST" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "Ping test: $PING_TEST"

echo ""
echo "Testing port 3306 connectivity:"
PORT_TEST=$(podman exec "$LEANTIME_CONTAINER" nc -z "$DB_HOST" 3306 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "Port 3306 test: $PORT_TEST"

echo ""
echo "Testing telnet to MySQL port:"
TELNET_TEST=$(podman exec "$LEANTIME_CONTAINER" timeout 5 telnet "$DB_HOST" 3306 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "Telnet test: $TELNET_TEST"

echo ""
echo "🔍 STEP 8: Database Connection Testing"
echo "===================================="

if [[ -n "$DB_PASSWORD" ]]; then
    echo "Testing database connection from Leantime container:"
    DB_CONNECT_TEST=$(podman exec "$LEANTIME_CONTAINER" mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT 1;" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
    echo "Database connection: $DB_CONNECT_TEST"
    
    if [[ "$DB_CONNECT_TEST" == "FAILED" ]]; then
        echo "❌ Database connection failed"
        echo "🔧 Testing connection with error details:"
        podman exec "$LEANTIME_CONTAINER" mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT 1;" 2>&1 | head -5
    fi
else
    echo "❌ No database password found in environment"
fi

echo ""
echo "🔧 STEP 9: Aggressive Network Fix"
echo "==============================="

echo "Disconnecting containers from all networks..."
# Get all networks for each container and disconnect
LEANTIME_NETS=$(podman inspect "$LEANTIME_CONTAINER" --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}')
MYSQL_NETS=$(podman inspect "$MYSQL_CONTAINER" --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}')

for net in $LEANTIME_NETS; do
    if [[ "$net" != "podman" ]]; then
        echo "Disconnecting Leantime from $net"
        podman network disconnect "$net" "$LEANTIME_CONTAINER" 2>/dev/null || echo "Already disconnected"
    fi
done

for net in $MYSQL_NETS; do
    if [[ "$net" != "podman" ]]; then
        echo "Disconnecting MySQL from $net"
        podman network disconnect "$net" "$MYSQL_CONTAINER" 2>/dev/null || echo "Already disconnected"
    fi
done

echo ""
echo "Creating fresh network..."
podman network rm leantime-net 2>/dev/null || echo "Network didn't exist"
podman network create leantime-net

echo ""
echo "Connecting containers to fresh network..."
podman network connect leantime-net "$MYSQL_CONTAINER"
podman network connect leantime-net "$LEANTIME_CONTAINER"

echo ""
echo "Waiting for network to stabilize..."
sleep 10

echo ""
echo "🔍 STEP 10: Post-Fix Testing"
echo "=========================="

echo "Testing connectivity after network fix:"
POST_FIX_PORT_TEST=$(podman exec "$LEANTIME_CONTAINER" nc -z "$DB_HOST" 3306 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "Port 3306 test: $POST_FIX_PORT_TEST"

if [[ "$POST_FIX_PORT_TEST" == "FAILED" ]]; then
    echo "❌ Still cannot connect after network fix"
    echo "🔧 Trying container restart approach..."
    
    echo "Restarting MySQL container..."
    podman restart "$MYSQL_CONTAINER"
    sleep 20
    
    echo "Restarting Leantime container..."
    podman restart "$LEANTIME_CONTAINER"
    sleep 15
    
    echo "Final connectivity test:"
    FINAL_PORT_TEST=$(podman exec "$LEANTIME_CONTAINER" nc -z "$DB_HOST" 3306 2>/dev/null && echo "SUCCESS" || echo "FAILED")
    echo "Final port test: $FINAL_PORT_TEST"
    
    if [[ "$FINAL_PORT_TEST" == "SUCCESS" ]] && [[ -n "$DB_PASSWORD" ]]; then
        FINAL_DB_TEST=$(podman exec "$LEANTIME_CONTAINER" mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT 1;" 2>/dev/null && echo "SUCCESS" || echo "FAILED")
        echo "Final database test: $FINAL_DB_TEST"
    fi
fi

echo ""
echo "📊 DIAGNOSIS SUMMARY"
echo "==================="

echo "Container Status:"
echo "  Leantime: $(podman ps --format '{{.Status}}' --filter name="$LEANTIME_CONTAINER")"
echo "  MySQL: $(podman ps --format '{{.Status}}' --filter name="$MYSQL_CONTAINER")"

echo ""
echo "Network Status:"
echo "  DNS Resolution: $DNS_TEST"
echo "  Port Connectivity: ${FINAL_PORT_TEST:-$POST_FIX_PORT_TEST}"
echo "  Database Connection: ${FINAL_DB_TEST:-$DB_CONNECT_TEST}"

echo ""
echo "🎯 RECOMMENDED ACTIONS:"

if [[ "${FINAL_DB_TEST:-$DB_CONNECT_TEST}" == "SUCCESS" ]]; then
    echo "✅ Database connectivity restored!"
    echo "1. Test web interface: https://admin.dxbmeta.com"
    echo "2. Check if /install redirect is gone"
elif [[ "${FINAL_PORT_TEST:-$POST_FIX_PORT_TEST}" == "SUCCESS" ]]; then
    echo "⚠️  Network connectivity works but database auth fails"
    echo "1. Check database credentials"
    echo "2. Verify database and user exist"
    echo "3. May need to recreate database user"
else
    echo "❌ Network connectivity still failed"
    echo "1. Consider recreating containers"
    echo "2. Check for firewall/security policies"
    echo "3. Verify container runtime configuration"
fi

ENDSSH

echo ""
echo -e "${GREEN}🔍 Deep Diagnosis Completed${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps Based on Results${NC}"
echo "=============================="
echo "1. Review the detailed diagnosis above"
echo "2. Check if database connectivity was restored"
echo "3. Test https://admin.dxbmeta.com"
echo "4. If still failing, we may need to recreate containers"
echo ""
echo "✅ Diagnosis completed at $(date)"
