name: Docker

on:
  workflow_dispatch: 
    inputs: 
      tag:
        description: "Version"
        required: true
      set_as_latest:
        description: "Set this version as 'latest'"
        type: boolean
        default: false
        required: true

jobs:

  build:
  
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
              
    - name: Docker login
      run: |
        docker login -u ${{secrets.DOCKER_USER}} -p ${{secrets.DOCKER_PASSWORD}}
        
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        platforms: linux/amd64,linux/arm64,linux/arm/v8

    - name: Build and push
      run: |
        docker buildx build --build-arg LEAN_VERSION=${{ github.event.inputs.tag }} \
          --push --platform linux/amd64,linux/arm64,linux/arm/v8 \
          --tag leantime/leantime:${{ github.event.inputs.tag }} .

    - name: Create and push manifest for latest
      if: ${{ inputs.set_as_latest }}
      run: |
        docker buildx imagetools create -t leantime/leantime:latest leantime/leantime:${{ github.event.inputs.tag }}