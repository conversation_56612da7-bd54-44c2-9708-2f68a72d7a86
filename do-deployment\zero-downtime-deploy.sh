#!/bin/bash

# Zero-Downtime Deployment Script for DigitalOcean
# Uses blue-green deployment strategy

set -e

# Configuration
DEPLOY_DIR="/opt/leantime"
BACKUP_DIR="/opt/leantime/backups"
CURRENT_COLOR=$(cat $DEPLOY_DIR/.current_color 2>/dev/null || echo "blue")
NEW_COLOR=$([ "$CURRENT_COLOR" == "blue" ] && echo "green" || echo "blue")
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

echo "======================================"
echo "   Zero-Downtime Deployment"
echo "======================================"
echo "Current: $CURRENT_COLOR"
echo "Deploying to: $NEW_COLOR"
echo ""

# Step 1: Backup current data
echo "Step 1: Creating backup..."
mkdir -p $BACKUP_DIR
docker exec mysql_leantime mysqldump -u root -p${MYSQL_ROOT_PASSWORD} leantime > $BACKUP_DIR/db-backup-$TIMESTAMP.sql
tar -czf $BACKUP_DIR/volumes-backup-$TIMESTAMP.tar.gz /var/lib/docker/volumes/leantime_*

# Step 2: Pull new images
echo "Step 2: Pulling new images..."
docker-compose -f docker-compose.production.yml pull

# Step 3: Start new containers with different names
echo "Step 3: Starting new containers ($NEW_COLOR)..."
export COMPOSE_PROJECT_NAME="leantime_$NEW_COLOR"
docker-compose -f docker-compose.production.yml up -d --no-deps --scale leantime=1

# Step 4: Wait for new containers to be healthy
echo "Step 4: Waiting for health checks..."
RETRIES=30
while [ $RETRIES -gt 0 ]; do
    if docker exec leantime_${NEW_COLOR}_leantime_1 curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✓ New container is healthy"
        break
    fi
    echo "Waiting for container to be healthy... ($RETRIES retries left)"
    sleep 5
    RETRIES=$((RETRIES - 1))
done

if [ $RETRIES -eq 0 ]; then
    echo "✗ New container failed health check. Rolling back..."
    docker-compose -f docker-compose.production.yml down
    exit 1
fi

# Step 5: Update nginx to point to new containers
echo "Step 5: Switching traffic to new containers..."
cat > /tmp/nginx-upstream.conf << EOF
upstream leantime_backend {
    least_conn;
    server leantime_${NEW_COLOR}_leantime_1:8080 max_fails=3 fail_timeout=30s;
}
EOF

docker cp /tmp/nginx-upstream.conf nginx-proxy:/etc/nginx/conf.d/upstream.conf
docker exec nginx-proxy nginx -s reload

# Step 6: Verify new deployment
echo "Step 6: Verifying deployment..."
sleep 5
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN)
if [ "$HTTP_CODE" != "200" ] && [ "$HTTP_CODE" != "302" ]; then
    echo "✗ Deployment verification failed (HTTP $HTTP_CODE). Rolling back..."
    
    # Rollback
    cat > /tmp/nginx-upstream.conf << EOF
upstream leantime_backend {
    least_conn;
    server leantime_${CURRENT_COLOR}_leantime_1:8080 max_fails=3 fail_timeout=30s;
}
EOF
    docker cp /tmp/nginx-upstream.conf nginx-proxy:/etc/nginx/conf.d/upstream.conf
    docker exec nginx-proxy nginx -s reload
    
    # Stop new containers
    export COMPOSE_PROJECT_NAME="leantime_$NEW_COLOR"
    docker-compose -f docker-compose.production.yml down
    
    exit 1
fi

echo "✓ Deployment verified successfully"

# Step 7: Stop old containers
echo "Step 7: Stopping old containers..."
export COMPOSE_PROJECT_NAME="leantime_$CURRENT_COLOR"
docker-compose -f docker-compose.production.yml down

# Step 8: Update current color
echo $NEW_COLOR > $DEPLOY_DIR/.current_color

# Step 9: Cleanup old images
echo "Step 9: Cleaning up..."
docker image prune -f

echo ""
echo "======================================"
echo "✓ Zero-downtime deployment complete!"
echo "======================================"
echo "Deployed version: $NEW_COLOR"
echo "Backup created: $BACKUP_DIR/*-$TIMESTAMP.*"

# Optional: Send notification
if [ ! -z "$SLACK_WEBHOOK" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"✅ Leantime deployed successfully to $DOMAIN (version: $NEW_COLOR)\"}" \
        $SLACK_WEBHOOK
fi