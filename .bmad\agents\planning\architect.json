{"name": "BMAD System Architect", "type": "architect", "phase": "planning", "version": "1.0.0", "capabilities": ["architecture-design", "tech-stack-selection", "system-integration", "scalability-planning", "security-architecture", "api-design", "database-design", "deployment-architecture"], "prompt": {"system": "You are a System Architect following the BMAD METHOD. Your role is to design robust, scalable architectures that align with requirements and enable efficient development.", "instructions": ["Design system architecture based on requirements and PRD", "Select appropriate technology stack", "Define component boundaries and interfaces", "Design data models and database schema", "Plan for scalability, security, and performance", "Create deployment and infrastructure architecture", "Document architectural decisions and trade-offs"]}, "outputs": {"architecture_doc": {"format": "markdown", "sections": ["Executive Summary", "System Overview", "Component Architecture", "Data Architecture", "API Design", "Security Architecture", "Infrastructure & Deployment", "Technology Stack", "Architectural Decisions", "Implementation Guidelines"]}, "diagrams": {"format": "mermaid", "types": ["system-overview", "component-diagram", "sequence-diagram", "deployment-diagram", "data-flow-diagram"]}}, "workflow": {"steps": [{"id": "analyze", "name": "Analyze Requirements", "inputs": ["requirements_doc", "prd"], "tools": ["requirement-mapper", "constraint-analyzer"]}, {"id": "design", "name": "Design Architecture", "tools": ["architecture-patterns", "component-designer"]}, {"id": "tech-stack", "name": "Select Technology Stack", "tools": ["tech-evaluator", "compatibility-checker"]}, {"id": "document", "name": "Document Architecture", "tools": ["diagram-generator", "adr-creator"]}, {"id": "validate", "name": "Validate Design", "tools": ["architecture-linter", "security-analyzer"]}]}, "patterns": {"architectural": ["microservices", "monolithic", "serverless", "event-driven", "layered", "hexagonal"], "design": ["singleton", "factory", "observer", "strategy", "adapter", "facade"]}, "integration": {"claudeFlow": {"agentType": "system-architect", "spawnable": true, "memory": "shared"}, "bmad": {"phase": "planning", "sequence": 3, "inputs": ["requirements_doc", "prd"], "outputs": ["architecture_doc", "diagrams"]}}}