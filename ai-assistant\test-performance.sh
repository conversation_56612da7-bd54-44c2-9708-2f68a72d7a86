#!/bin/bash
# Performance testing script for AI Assistant

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

BASE_URL="http://127.0.0.1:8444"

echo "⚡ Performance Testing Round 2 - AI Assistant"
echo "============================================="

# Get authentication token first
echo -e "\n${YELLOW}Getting authentication token...${NC}"
AUTH_RESPONSE=$(curl -s -X POST ${BASE_URL}/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "admin123"}')

if [[ $AUTH_RESPONSE == *"token"* ]]; then
    TOKEN=$(echo $AUTH_RESPONSE | jq -r .token)
    echo -e "${GREEN}✓ Authentication token obtained${NC}"
else
    echo -e "${RED}✗ Failed to get authentication token${NC}"
    exit 1
fi

# Test 1: Response time benchmark
echo -e "\n${YELLOW}Test 1: Response time benchmark${NC}"
TOTAL_TIME=0
REQUESTS=50
SUCCESS_COUNT=0

for i in $(seq 1 $REQUESTS); do
    START_TIME=$(date +%s%N)
    RESPONSE=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/api/v1/process \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"action": "knowledge.search", "data": {"query": "test"}}' -o /dev/null)
    END_TIME=$(date +%s%N)
    
    if [[ $RESPONSE == "200" ]]; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        DURATION=$((($END_TIME - $START_TIME) / 1000000))  # Convert to milliseconds
        TOTAL_TIME=$(($TOTAL_TIME + $DURATION))
    fi
    
    # Progress indicator
    if (( i % 10 == 0 )); then
        echo -n "."
    fi
done

AVERAGE_TIME=$(($TOTAL_TIME / $SUCCESS_COUNT))
echo -e "\n${GREEN}Average response time: ${AVERAGE_TIME}ms (${SUCCESS_COUNT}/${REQUESTS} successful)${NC}"

if [[ $AVERAGE_TIME -lt 500 ]]; then
    echo -e "${GREEN}✓ Response time excellent (<500ms)${NC}"
elif [[ $AVERAGE_TIME -lt 1000 ]]; then
    echo -e "${YELLOW}⚠ Response time good (<1000ms)${NC}"
else
    echo -e "${RED}✗ Response time poor (>1000ms)${NC}"
fi

# Test 2: Concurrent requests
echo -e "\n${YELLOW}Test 2: Concurrent request handling${NC}"
CONCURRENT_REQUESTS=10
CONCURRENT_FILE="/tmp/concurrent_results.txt"
rm -f $CONCURRENT_FILE

echo "Starting $CONCURRENT_REQUESTS concurrent requests..."

for i in $(seq 1 $CONCURRENT_REQUESTS); do
    (
        START_TIME=$(date +%s%N)
        RESPONSE=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/api/v1/process \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"action": "knowledge.search", "data": {"query": "concurrent test '$i'"}}' -o /dev/null)
        END_TIME=$(date +%s%N)
        DURATION=$((($END_TIME - $START_TIME) / 1000000))
        echo "$RESPONSE $DURATION" >> $CONCURRENT_FILE
    ) &
done

wait

CONCURRENT_SUCCESS=$(grep "^200" $CONCURRENT_FILE | wc -l)
CONCURRENT_AVG=$(grep "^200" $CONCURRENT_FILE | awk '{sum+=$2} END {print int(sum/NR)}')

echo -e "${GREEN}Concurrent requests: ${CONCURRENT_SUCCESS}/${CONCURRENT_REQUESTS} successful${NC}"
echo -e "${GREEN}Average concurrent response time: ${CONCURRENT_AVG}ms${NC}"

if [[ $CONCURRENT_SUCCESS -eq $CONCURRENT_REQUESTS ]]; then
    echo -e "${GREEN}✓ All concurrent requests successful${NC}"
else
    echo -e "${YELLOW}⚠ Some concurrent requests failed${NC}"
fi

# Test 3: Memory usage monitoring
echo -e "\n${YELLOW}Test 3: Memory usage monitoring${NC}"
CONTAINER_NAME="ai-assistant-secure"

# Get memory stats
MEMORY_STATS=$(podman stats --no-stream --format "json" $CONTAINER_NAME | jq '.[0]')
MEMORY_USAGE=$(echo $MEMORY_STATS | jq -r '.MemUsage' | cut -d'/' -f1 | sed 's/MiB//')
MEMORY_LIMIT=$(echo $MEMORY_STATS | jq -r '.MemUsage' | cut -d'/' -f2 | sed 's/GiB//')
MEMORY_PERCENT=$(echo $MEMORY_STATS | jq -r '.MemPerc' | sed 's/%//')

echo -e "${GREEN}Memory usage: ${MEMORY_USAGE}MB / ${MEMORY_LIMIT}GB (${MEMORY_PERCENT}%)${NC}"

if (( $(echo "$MEMORY_PERCENT < 50" | bc -l) )); then
    echo -e "${GREEN}✓ Memory usage excellent (<50%)${NC}"
elif (( $(echo "$MEMORY_PERCENT < 80" | bc -l) )); then
    echo -e "${YELLOW}⚠ Memory usage moderate (<80%)${NC}"
else
    echo -e "${RED}✗ Memory usage high (>80%)${NC}"
fi

# Test 4: CPU usage monitoring
echo -e "\n${YELLOW}Test 4: CPU usage monitoring${NC}"
CPU_PERCENT=$(echo $MEMORY_STATS | jq -r '.CPU' | sed 's/%//')

echo -e "${GREEN}CPU usage: ${CPU_PERCENT}%${NC}"

if (( $(echo "$CPU_PERCENT < 20" | bc -l) )); then
    echo -e "${GREEN}✓ CPU usage excellent (<20%)${NC}"
elif (( $(echo "$CPU_PERCENT < 50" | bc -l) )); then
    echo -e "${YELLOW}⚠ CPU usage moderate (<50%)${NC}"
else
    echo -e "${RED}✗ CPU usage high (>50%)${NC}"
fi

# Test 5: Rate limiting performance
echo -e "\n${YELLOW}Test 5: Rate limiting performance${NC}"
RATE_LIMIT_START=$(date +%s)
RATE_LIMIT_COUNT=0

for i in {1..70}; do
    RESPONSE=$(curl -s -w "%{http_code}" ${BASE_URL}/health -o /dev/null)
    if [[ $RESPONSE == "429" ]]; then
        RATE_LIMIT_COUNT=$i
        break
    fi
done

RATE_LIMIT_END=$(date +%s)
RATE_LIMIT_DURATION=$(($RATE_LIMIT_END - $RATE_LIMIT_START))

if [[ $RATE_LIMIT_COUNT -gt 0 ]]; then
    echo -e "${GREEN}✓ Rate limiting triggered after ${RATE_LIMIT_COUNT} requests in ${RATE_LIMIT_DURATION}s${NC}"
else
    echo -e "${YELLOW}⚠ Rate limiting not triggered in 70 requests${NC}"
fi

# Test 6: Token refresh performance
echo -e "\n${YELLOW}Test 6: Token refresh performance${NC}"
REFRESH_START=$(date +%s%N)
REFRESH_RESPONSE=$(curl -s -X POST ${BASE_URL}/auth/refresh \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
REFRESH_END=$(date +%s%N)
REFRESH_DURATION=$((($REFRESH_END - $REFRESH_START) / 1000000))

if [[ $REFRESH_RESPONSE == *"token"* ]]; then
    echo -e "${GREEN}✓ Token refresh successful in ${REFRESH_DURATION}ms${NC}"
else
    echo -e "${RED}✗ Token refresh failed${NC}"
fi

# Test 7: Container resource limits
echo -e "\n${YELLOW}Test 7: Container resource limits validation${NC}"

# Check if container respects memory limits
MEMORY_LIMIT_BYTES=$(podman inspect $CONTAINER_NAME | jq -r '.[0].HostConfig.Memory')
MEMORY_LIMIT_MB=$(($MEMORY_LIMIT_BYTES / 1024 / 1024))

echo -e "${GREEN}Memory limit: ${MEMORY_LIMIT_MB}MB${NC}"

if [[ $MEMORY_LIMIT_MB -eq 2048 ]]; then
    echo -e "${GREEN}✓ Memory limit correctly set to 2GB${NC}"
else
    echo -e "${YELLOW}⚠ Memory limit: ${MEMORY_LIMIT_MB}MB${NC}"
fi

# Check CPU limits
CPU_QUOTA=$(podman inspect $CONTAINER_NAME | jq -r '.[0].HostConfig.CpuQuota')
CPU_PERIOD=$(podman inspect $CONTAINER_NAME | jq -r '.[0].HostConfig.CpuPeriod')

if [[ $CPU_QUOTA != "null" && $CPU_PERIOD != "null" ]]; then
    CPU_LIMIT=$(echo "scale=1; $CPU_QUOTA / $CPU_PERIOD" | bc)
    echo -e "${GREEN}✓ CPU limit: ${CPU_LIMIT} cores${NC}"
else
    echo -e "${YELLOW}⚠ CPU limits not set${NC}"
fi

# Cleanup
rm -f $CONCURRENT_FILE

echo -e "\n${GREEN}Performance Testing Round 2 Complete!${NC}"
echo -e "\n${YELLOW}Performance Summary:${NC}"
echo -e "  Average Response Time: ${AVERAGE_TIME}ms"
echo -e "  Concurrent Success Rate: ${CONCURRENT_SUCCESS}/${CONCURRENT_REQUESTS}"
echo -e "  Memory Usage: ${MEMORY_PERCENT}%"
echo -e "  CPU Usage: ${CPU_PERCENT}%"
echo -e "  Memory Limit: ${MEMORY_LIMIT_MB}MB"