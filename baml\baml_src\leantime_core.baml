// BAML Core Functions for Leantime Project Management
// AI-powered enhancements for project management workflows

// Client Configuration
client<llm> SmartClient {
  provider anthropic
  options {
    model "claude-3-opus-20240229"
    api_key env.ANTHROPIC_API_KEY
    max_tokens 4096
    temperature 0.7
  }
}

client<llm> FastClient {
  provider anthropic
  options {
    model "claude-3-haiku-20240307"
    api_key env.ANTHROPIC_API_KEY
    max_tokens 2048
    temperature 0.5
  }
}

// Project Schema
class Project {
  id string
  name string
  clientName string
  status "planning" | "active" | "on-hold" | "completed" | "archived"
  startDate string
  endDate string
  budget float?
  actualCost float?
  completionPercentage float
  health "healthy" | "at-risk" | "critical"
  team TeamMember[]
  milestones Milestone[]
  risks Risk[]
}

class TeamMember {
  id string
  name string
  email string
  role string
  skills string[]
  availability float @description("0-100% availability")
  currentWorkload float @description("Hours allocated")
}

class Milestone {
  id string
  title string
  dueDate string
  status "pending" | "in-progress" | "completed" | "delayed"
  dependencies string[]
  deliverables string[]
}

class Risk {
  description string
  probability "low" | "medium" | "high"
  impact "low" | "medium" | "high"
  mitigation string
  owner string?
}

// Task Schema
class Task {
  id string
  title string
  description string
  projectId string
  assignee string?
  priority "low" | "medium" | "high" | "critical"
  status "backlog" | "todo" | "in-progress" | "review" | "done"
  estimatedHours float?
  actualHours float?
  dueDate string?
  tags string[]
  blockers string[]
  subtasks Task[]?
}

// Sprint Schema
class Sprint {
  id string
  projectId string
  name string
  startDate string
  endDate string
  goal string
  velocity float?
  tasks Task[]
  retrospective {
    whatWentWell string[]
    whatCouldImprove string[]
    actionItems string[]
  }?
}

// Project Health Analysis
function AnalyzeProjectHealth(
  project: Project,
  tasks: Task[],
  timeEntries: {userId: string, hours: float, date: string}[]
) -> {
  overallHealth: "healthy" | "at-risk" | "critical",
  score: float,
  issues: string[],
  recommendations: string[],
  predictedCompletionDate: string,
  budgetForecast: float?
} {
  client SmartClient
  
  prompt #"
    Analyze the health of this project:
    
    Project: {{ project }}
    Tasks: {{ tasks }}
    Time Entries: {{ timeEntries }}
    
    Evaluate:
    1. Schedule adherence (current vs planned)
    2. Budget tracking (if applicable)
    3. Team workload and burnout risk
    4. Task completion velocity
    5. Blocker patterns
    6. Risk factors
    
    Calculate:
    - Overall health score (0-100)
    - Predicted completion date based on current velocity
    - Budget forecast based on burn rate
    
    Return comprehensive health assessment with actionable recommendations.
  "#
}

// Intelligent Task Breakdown
function BreakdownTask(
  task: Task,
  teamSkills: {[memberId: string]: string[]},
  projectContext: string
) -> {
  subtasks: Task[],
  dependencies: {from: string, to: string}[],
  estimatedTotalHours: float,
  suggestedAssignments: {[taskId: string]: string},
  risks: string[]
} {
  client SmartClient
  
  prompt #"
    Break down this complex task into manageable subtasks:
    
    Task: {{ task }}
    Team Skills: {{ teamSkills }}
    Project Context: {{ projectContext }}
    
    Requirements:
    1. Create atomic, clearly defined subtasks
    2. Identify task dependencies
    3. Estimate hours for each subtask
    4. Suggest optimal assignees based on skills
    5. Identify potential risks or blockers
    
    Consider:
    - Task complexity and technical requirements
    - Team member expertise and availability
    - Parallel vs sequential execution
    - Testing and review requirements
    
    Return detailed breakdown with assignments.
  "#
}

// Sprint Planning Optimization
function OptimizeSprint(
  availableTasks: Task[],
  teamCapacity: {[memberId: string]: float},
  sprintDuration: int,
  previousVelocity: float?
) -> {
  selectedTasks: Task[],
  assignments: {[taskId: string]: string},
  estimatedVelocity: float,
  sprintGoal: string,
  risks: string[],
  alternativePlan: Task[]?
} {
  client SmartClient
  
  prompt #"
    Optimize sprint planning:
    
    Available Tasks: {{ availableTasks }}
    Team Capacity (hours): {{ teamCapacity }}
    Sprint Duration: {{ sprintDuration }} days
    Previous Velocity: {{ previousVelocity }}
    
    Optimize for:
    1. Realistic workload given capacity
    2. Balanced distribution across team
    3. Priority and business value
    4. Dependencies and blockers
    5. Mix of task types (features, bugs, tech debt)
    
    Generate:
    - Optimal task selection
    - Task-to-person assignments
    - Clear sprint goal
    - Risk assessment
    - Backup plan if capacity changes
    
    Return optimized sprint plan.
  "#
}

// Effort Estimation
function EstimateEffort(
  task: Task,
  historicalData: {
    similarTasks: {description: string, actualHours: float}[],
    teamVelocity: float,
    accuracy: float
  }
) -> {
  estimatedHours: float,
  confidence: "low" | "medium" | "high",
  range: {min: float, max: float},
  factors: string[],
  similarTasksUsed: string[]
} {
  client FastClient
  
  prompt #"
    Estimate effort for this task:
    
    Task: {{ task }}
    Historical Data: {{ historicalData }}
    
    Analysis:
    1. Find similar completed tasks
    2. Adjust for complexity differences
    3. Consider team velocity trends
    4. Account for unknowns and risks
    5. Factor in review/testing time
    
    Provide:
    - Point estimate in hours
    - Confidence level
    - Min/max range
    - Factors influencing estimate
    - Similar tasks used as reference
    
    Return data-driven estimate.
  "#
}

// Risk Assessment
function AssessProjectRisks(
  project: Project,
  tasks: Task[],
  teamStatus: TeamMember[],
  externalFactors: string[]
) -> {
  risks: Risk[],
  overallRiskLevel: "low" | "medium" | "high",
  mitigation: {[riskId: string]: string},
  earlyWarnings: string[],
  contingencyPlan: string
} {
  client SmartClient
  
  prompt #"
    Perform comprehensive risk assessment:
    
    Project: {{ project }}
    Tasks: {{ tasks }}
    Team Status: {{ teamStatus }}
    External Factors: {{ externalFactors }}
    
    Identify risks in:
    1. Schedule/timeline
    2. Budget/resources
    3. Technical complexity
    4. Team availability/skills
    5. Dependencies/blockers
    6. Stakeholder changes
    7. External factors
    
    For each risk:
    - Assess probability and impact
    - Suggest mitigation strategies
    - Define early warning signs
    - Assign risk owners
    
    Return comprehensive risk register with action plan.
  "#
}

// Team Performance Analysis
function AnalyzeTeamPerformance(
  team: TeamMember[],
  completedTasks: Task[],
  timeEntries: {userId: string, taskId: string, hours: float, date: string}[],
  period: {start: string, end: string}
) -> {
  individualMetrics: {
    [memberId: string]: {
      productivity: float,
      accuracy: float,
      velocity: float,
      strengths: string[],
      improvementAreas: string[]
    }
  },
  teamMetrics: {
    averageVelocity: float,
    collaborationScore: float,
    knowledgeGaps: string[],
    topPerformers: string[]
  },
  recommendations: string[]
} {
  client SmartClient
  
  prompt #"
    Analyze team performance:
    
    Team: {{ team }}
    Completed Tasks: {{ completedTasks }}
    Time Entries: {{ timeEntries }}
    Period: {{ period }}
    
    Calculate:
    1. Individual productivity metrics
    2. Estimation accuracy (estimated vs actual)
    3. Task completion velocity
    4. Quality indicators
    5. Collaboration patterns
    
    Identify:
    - Individual strengths and growth areas
    - Team knowledge gaps
    - Training opportunities
    - Process improvements
    
    Return comprehensive performance analysis with actionable insights.
  "#
}

// Automated Status Report
function GenerateStatusReport(
  project: Project,
  period: {start: string, end: string},
  audience: "team" | "management" | "client"
) -> {
  summary: string,
  achievements: string[],
  inProgress: string[],
  blockers: string[],
  nextSteps: string[],
  metrics: {
    tasksCompleted: int,
    velocity: float,
    budgetStatus: string?,
    scheduleStatus: string
  },
  visualizations: string[]
} {
  client FastClient
  
  prompt #"
    Generate status report for {{ audience }}:
    
    Project: {{ project }}
    Reporting Period: {{ period }}
    
    Include:
    1. Executive summary (2-3 sentences)
    2. Key achievements
    3. Current work in progress
    4. Blockers and issues
    5. Upcoming milestones
    6. Relevant metrics
    
    Tailor for audience:
    - Team: Technical details, individual contributions
    - Management: High-level progress, risks, decisions needed
    - Client: Business value, milestones, timeline
    
    Suggest visualizations:
    - Burndown charts
    - Progress bars
    - Risk matrices
    
    Return formatted report appropriate for audience.
  "#
}