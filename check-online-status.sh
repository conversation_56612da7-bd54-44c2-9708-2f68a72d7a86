#!/bin/bash

# Online Status Check for admin.dxbmeta.com
# Checks if the Leantime system is accessible

echo "🌐 Checking Online Status for admin.dxbmeta.com"
echo "================================================"
echo "📅 $(date)"
echo ""

# Configuration from codebase
SERVER_IP="*************"
DOMAIN="admin.dxbmeta.com"
LEANTIME_PORT="8090"
ODOO_PORT="8073"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 Testing Connectivity...${NC}"
echo ""

# Test 1: Ping server IP
echo -e "${YELLOW}📡 Test 1: Server IP Connectivity${NC}"
if ping -c 3 $SERVER_IP > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Server IP ($SERVER_IP) is reachable${NC}"
else
    echo -e "${RED}❌ Server IP ($SERVER_IP) is not reachable${NC}"
fi
echo ""

# Test 2: Domain resolution
echo -e "${YELLOW}🌐 Test 2: Domain Resolution${NC}"
RESOLVED_IP=$(nslookup $DOMAIN 2>/dev/null | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "FAILED")
if [ "$RESOLVED_IP" != "FAILED" ] && [ ! -z "$RESOLVED_IP" ]; then
    echo -e "${GREEN}✅ Domain resolves to: $RESOLVED_IP${NC}"
    if [ "$RESOLVED_IP" = "$SERVER_IP" ]; then
        echo -e "${GREEN}✅ DNS points to correct server${NC}"
    else
        echo -e "${YELLOW}⚠️  DNS points to different IP than expected${NC}"
    fi
else
    echo -e "${RED}❌ Domain resolution failed${NC}"
fi
echo ""

# Test 3: HTTPS connectivity
echo -e "${YELLOW}🔒 Test 3: HTTPS Connectivity${NC}"
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 --max-time 30 https://$DOMAIN/ 2>/dev/null || echo "FAILED")
if [ "$HTTPS_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ HTTPS working (HTTP 200)${NC}"
elif [ "$HTTPS_STATUS" = "302" ] || [ "$HTTPS_STATUS" = "301" ]; then
    echo -e "${GREEN}✅ HTTPS responding with redirect ($HTTPS_STATUS)${NC}"
elif [ "$HTTPS_STATUS" = "FAILED" ]; then
    echo -e "${RED}❌ HTTPS connection failed${NC}"
else
    echo -e "${YELLOW}⚠️  HTTPS responding with status: $HTTPS_STATUS${NC}"
fi
echo ""

# Test 4: HTTP connectivity (should redirect)
echo -e "${YELLOW}🌍 Test 4: HTTP Connectivity${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 --max-time 30 http://$DOMAIN/ 2>/dev/null || echo "FAILED")
if [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo -e "${GREEN}✅ HTTP redirects to HTTPS ($HTTP_STATUS)${NC}"
elif [ "$HTTP_STATUS" = "200" ]; then
    echo -e "${YELLOW}⚠️  HTTP serving content directly (should redirect)${NC}"
elif [ "$HTTP_STATUS" = "FAILED" ]; then
    echo -e "${RED}❌ HTTP connection failed${NC}"
else
    echo -e "${YELLOW}⚠️  HTTP responding with status: $HTTP_STATUS${NC}"
fi
echo ""

# Test 5: Direct server ports
echo -e "${YELLOW}🔌 Test 5: Direct Port Access${NC}"
echo "Testing direct access to server ports..."

# Test Leantime port
LEANTIME_DIRECT=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 15 http://$SERVER_IP:$LEANTIME_PORT/ 2>/dev/null || echo "FAILED")
if [ "$LEANTIME_DIRECT" = "200" ] || [ "$LEANTIME_DIRECT" = "302" ]; then
    echo -e "${GREEN}✅ Leantime direct access (port $LEANTIME_PORT): $LEANTIME_DIRECT${NC}"
else
    echo -e "${RED}❌ Leantime direct access failed: $LEANTIME_DIRECT${NC}"
fi

# Test Odoo port
ODOO_DIRECT=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 15 http://$SERVER_IP:$ODOO_PORT/ 2>/dev/null || echo "FAILED")
if [ "$ODOO_DIRECT" = "200" ] || [ "$ODOO_DIRECT" = "302" ]; then
    echo -e "${GREEN}✅ Odoo direct access (port $ODOO_PORT): $ODOO_DIRECT${NC}"
else
    echo -e "${RED}❌ Odoo direct access failed: $ODOO_DIRECT${NC}"
fi
echo ""

# Test 6: SSL Certificate
echo -e "${YELLOW}🔐 Test 6: SSL Certificate${NC}"
SSL_INFO=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "FAILED")
if [ "$SSL_INFO" != "FAILED" ]; then
    echo -e "${GREEN}✅ SSL certificate is valid${NC}"
    echo "$SSL_INFO"
else
    echo -e "${RED}❌ SSL certificate check failed${NC}"
fi
echo ""

# Test 7: Service-specific endpoints
echo -e "${YELLOW}🔧 Test 7: Service Endpoints${NC}"

# Health endpoint
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 https://$DOMAIN/health 2>/dev/null || echo "FAILED")
if [ "$HEALTH_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ Health endpoint responding${NC}"
else
    echo -e "${YELLOW}⚠️  Health endpoint: $HEALTH_STATUS${NC}"
fi

# Status endpoint
STATUS_RESPONSE=$(curl -s --connect-timeout 5 --max-time 10 https://$DOMAIN/status 2>/dev/null || echo "FAILED")
if [ "$STATUS_RESPONSE" != "FAILED" ] && echo "$STATUS_RESPONSE" | grep -q "Admin Services"; then
    echo -e "${GREEN}✅ Status page responding${NC}"
else
    echo -e "${YELLOW}⚠️  Status page not accessible${NC}"
fi
echo ""

# Summary
echo -e "${BLUE}📊 Summary${NC}"
echo "=========="

TOTAL_TESTS=7
PASSED_TESTS=0

# Count passed tests
if ping -c 1 $SERVER_IP > /dev/null 2>&1; then ((PASSED_TESTS++)); fi
if [ "$RESOLVED_IP" != "FAILED" ] && [ ! -z "$RESOLVED_IP" ]; then ((PASSED_TESTS++)); fi
if [ "$HTTPS_STATUS" = "200" ] || [ "$HTTPS_STATUS" = "302" ] || [ "$HTTPS_STATUS" = "301" ]; then ((PASSED_TESTS++)); fi
if [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ] || [ "$HTTP_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi
if [ "$LEANTIME_DIRECT" = "200" ] || [ "$LEANTIME_DIRECT" = "302" ]; then ((PASSED_TESTS++)); fi
if [ "$SSL_INFO" != "FAILED" ]; then ((PASSED_TESTS++)); fi
if [ "$HEALTH_STATUS" = "200" ]; then ((PASSED_TESTS++)); fi

echo -e "${BLUE}Tests Passed: $PASSED_TESTS/$TOTAL_TESTS${NC}"

if [ $PASSED_TESTS -ge 5 ]; then
    echo -e "${GREEN}🎉 System appears to be ONLINE and accessible!${NC}"
    echo ""
    echo -e "${GREEN}✅ You can access Leantime at: https://$DOMAIN${NC}"
    echo -e "${GREEN}✅ Email testing should work${NC}"
elif [ $PASSED_TESTS -ge 3 ]; then
    echo -e "${YELLOW}⚠️  System is PARTIALLY accessible${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Some services may need attention${NC}"
    echo -e "${YELLOW}📧 Email testing may work but verify manually${NC}"
else
    echo -e "${RED}❌ System appears to be OFFLINE or has issues${NC}"
    echo ""
    echo -e "${RED}🚨 Critical issues detected${NC}"
    echo -e "${RED}📧 Email testing likely to fail${NC}"
fi

echo ""
echo -e "${BLUE}🔗 Access URLs:${NC}"
echo "Primary: https://$DOMAIN"
echo "Direct:  http://$SERVER_IP:$LEANTIME_PORT"
echo "Status:  https://$DOMAIN/status"
echo "Health:  https://$DOMAIN/health"

echo ""
echo -e "${BLUE}📞 Next Steps:${NC}"
if [ $PASSED_TESTS -ge 5 ]; then
    echo "1. ✅ System is online - proceed with email testing"
    echo "2. 🧪 Run: node test-email-config.js"
    echo "3. 🔐 Test password reset manually"
else
    echo "1. 🔧 Check server status on DigitalOcean"
    echo "2. 🐳 Verify containers are running"
    echo "3. 🌐 Check nginx configuration"
    echo "4. 📧 Email testing should wait until system is online"
fi

echo ""
echo "✅ Status check completed at $(date)"
