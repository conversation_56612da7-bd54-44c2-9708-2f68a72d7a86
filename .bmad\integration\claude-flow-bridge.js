/**
 * BMAD-Claude Flow Integration Bridge
 * Seamlessly integrates BMAD METHOD with existing Claude Flow system
 */

const { spawn, exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class ClaudeFlowBridge {
  constructor() {
    this.claudeFlowAvailable = null;
    this.bmadConfig = null;
    this.agentMapping = this.createAgentMapping();
  }

  /**
   * Initialize the bridge and check for Claude Flow
   */
  async initialize() {
    // Check if Claude Flow is available
    this.claudeFlowAvailable = await this.checkClaudeFlow();
    
    // Load BMAD configuration
    this.bmadConfig = await this.loadBMADConfig();
    
    // Sync agent definitions
    await this.syncAgents();
    
    console.log(`✅ BMAD-Claude Flow Bridge initialized`);
    console.log(`   Claude Flow: ${this.claudeFlowAvailable ? 'Available' : 'Not found'}`);
    
    return {
      claudeFlow: this.claudeFlowAvailable,
      bmad: this.bmadConfig !== null
    };
  }

  /**
   * Map BMAD agents to Claude Flow agent types
   */
  createAgentMapping() {
    return {
      // BMAD Planning agents -> <PERSON> Flow agents
      'analyst': 'researcher',
      'product-manager': 'planner',
      'architect': 'system-architect',
      
      // BMAD Development agents -> Claude Flow agents
      'scrum-master': 'planner',
      'developer': 'coder',
      'qa-engineer': 'tester',
      
      // BMAD Orchestration agents -> Claude Flow agents
      'swarm-coordinator': 'swarm-coordinator',
      'flow-orchestrator': 'task-orchestrator',
      
      // Claude Flow agents -> BMAD roles
      'researcher': 'analyst',
      'planner': 'product-manager',
      'coder': 'developer',
      'tester': 'qa-engineer',
      'reviewer': 'code-reviewer'
    };
  }

  /**
   * Check if Claude Flow is installed and available
   */
  async checkClaudeFlow() {
    return new Promise((resolve) => {
      exec('npx claude-flow --version', (error, stdout) => {
        resolve(!error && stdout.includes('claude-flow'));
      });
    });
  }

  /**
   * Load BMAD configuration
   */
  async loadBMADConfig() {
    try {
      const configPath = path.join(process.cwd(), '.bmad', 'config.json');
      const content = await fs.readFile(configPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      return null;
    }
  }

  /**
   * Spawn a BMAD agent using Claude Flow
   */
  async spawnBMADAgent(bmadType, options = {}) {
    if (!this.claudeFlowAvailable) {
      throw new Error('Claude Flow is not available');
    }

    // Map BMAD agent type to Claude Flow agent type
    const claudeFlowType = this.agentMapping[bmadType] || bmadType;
    
    // Prepare context for Claude Flow agent
    const context = await this.prepareBMADContext(bmadType, options);
    
    // Spawn using Claude Flow
    const result = await this.executeClaudeFlow('agent', 'spawn', {
      type: claudeFlowType,
      task: options.task || `Execute ${bmadType} responsibilities`,
      context: JSON.stringify(context)
    });

    return {
      bmadType,
      claudeFlowType,
      agentId: result.agentId,
      status: 'active'
    };
  }

  /**
   * Initialize a BMAD swarm using Claude Flow
   */
  async initializeBMADSwarm(phase, topology = 'adaptive') {
    if (!this.claudeFlowAvailable) {
      throw new Error('Claude Flow is not available');
    }

    const agents = this.getPhaseAgents(phase);
    
    // Initialize swarm with Claude Flow
    const swarmResult = await this.executeClaudeFlow('swarm', 'init', {
      topology,
      maxAgents: agents.length,
      phase
    });

    // Spawn agents for the phase
    const spawnedAgents = [];
    for (const agentType of agents) {
      const agent = await this.spawnBMADAgent(agentType, {
        swarmId: swarmResult.swarmId,
        phase
      });
      spawnedAgents.push(agent);
    }

    return {
      swarmId: swarmResult.swarmId,
      phase,
      topology,
      agents: spawnedAgents
    };
  }

  /**
   * Prepare BMAD-specific context for Claude Flow agents
   */
  async prepareBMADContext(agentType, options = {}) {
    const context = {
      framework: 'BMAD-METHOD',
      agentType,
      phase: this.getAgentPhase(agentType),
      ...options
    };

    // Load agent-specific templates and instructions
    const agentConfig = await this.loadAgentConfig(agentType);
    if (agentConfig) {
      context.prompt = agentConfig.prompt;
      context.capabilities = agentConfig.capabilities;
      context.workflow = agentConfig.workflow;
    }

    // Add planning documents if in development phase
    if (context.phase === 'development') {
      context.planningDocs = await this.loadPlanningDocuments();
    }

    return context;
  }

  /**
   * Execute Claude Flow command
   */
  async executeClaudeFlow(command, subcommand, options = {}) {
    return new Promise((resolve, reject) => {
      const args = ['claude-flow', command];
      if (subcommand) args.push(subcommand);
      
      // Add options as flags
      Object.entries(options).forEach(([key, value]) => {
        args.push(`--${key}`);
        if (value !== true) args.push(value.toString());
      });

      const process = spawn('npx', args, {
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let error = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            // Try to parse as JSON, otherwise return raw output
            const result = output.includes('{') 
              ? JSON.parse(output.substring(output.indexOf('{')))
              : { output, success: true };
            resolve(result);
          } catch (e) {
            resolve({ output, success: true });
          }
        } else {
          reject(new Error(error || `Command failed with code ${code}`));
        }
      });
    });
  }

  /**
   * Sync BMAD agents with Claude Flow
   */
  async syncAgents() {
    if (!this.claudeFlowAvailable) return;

    const bmadAgents = await this.loadBMADAgents();
    
    // Register BMAD agents with Claude Flow if supported
    for (const agent of bmadAgents) {
      try {
        await this.registerWithClaudeFlow(agent);
      } catch (error) {
        console.warn(`Failed to register ${agent.type} with Claude Flow:`, error.message);
      }
    }
  }

  /**
   * Register a BMAD agent with Claude Flow
   */
  async registerWithClaudeFlow(agent) {
    // This would integrate with Claude Flow's agent registry if available
    // For now, we maintain the mapping internally
    this.agentMapping[agent.type] = agent.claudeFlowType || agent.type;
  }

  /**
   * Load all BMAD agent definitions
   */
  async loadBMADAgents() {
    const agents = [];
    const agentDirs = [
      '.bmad/agents/planning',
      '.bmad/agents/development',
      '.bmad/agents/orchestration'
    ];

    for (const dir of agentDirs) {
      try {
        const files = await fs.readdir(path.join(process.cwd(), dir));
        for (const file of files) {
          if (file.endsWith('.json')) {
            const content = await fs.readFile(path.join(process.cwd(), dir, file), 'utf-8');
            agents.push(JSON.parse(content));
          }
        }
      } catch (error) {
        // Directory might not exist
      }
    }

    return agents;
  }

  /**
   * Load specific agent configuration
   */
  async loadAgentConfig(agentType) {
    const paths = [
      `.bmad/agents/planning/${agentType}.json`,
      `.bmad/agents/development/${agentType}.json`,
      `.bmad/agents/orchestration/${agentType}.json`
    ];

    for (const configPath of paths) {
      try {
        const fullPath = path.join(process.cwd(), configPath);
        const content = await fs.readFile(fullPath, 'utf-8');
        return JSON.parse(content);
      } catch (error) {
        // Try next path
      }
    }

    return null;
  }

  /**
   * Get agents for a specific BMAD phase
   */
  getPhaseAgents(phase) {
    const phaseAgents = {
      planning: ['analyst', 'product-manager', 'architect'],
      development: ['scrum-master', 'developer', 'code-reviewer', 'tester'],
      validation: ['tester', 'qa-engineer', 'production-validator'],
      orchestration: ['swarm-coordinator', 'flow-orchestrator']
    };

    return phaseAgents[phase] || [];
  }

  /**
   * Get the phase for a specific agent type
   */
  getAgentPhase(agentType) {
    const agentPhases = {
      analyst: 'planning',
      'product-manager': 'planning',
      architect: 'planning',
      'scrum-master': 'development',
      developer: 'development',
      'code-reviewer': 'development',
      tester: 'validation',
      'qa-engineer': 'validation'
    };

    return agentPhases[agentType] || 'orchestration';
  }

  /**
   * Load planning documents for development phase
   */
  async loadPlanningDocuments() {
    const docs = {};
    const docTypes = ['requirements', 'prd', 'architecture'];
    const docsPath = path.join(process.cwd(), '.bmad', 'docs');

    try {
      const files = await fs.readdir(docsPath);
      for (const docType of docTypes) {
        const docFile = files.find(f => f.startsWith(docType));
        if (docFile) {
          docs[docType] = await fs.readFile(path.join(docsPath, docFile), 'utf-8');
        }
      }
    } catch (error) {
      // Docs directory might not exist
    }

    return docs;
  }

  /**
   * Execute a complete BMAD workflow using Claude Flow
   */
  async executeBMADWorkflow(workflowName, options = {}) {
    console.log(`🚀 Executing BMAD workflow: ${workflowName}`);

    const workflows = {
      'full-cycle': ['planning', 'development', 'validation'],
      'planning-only': ['planning'],
      'development-only': ['development'],
      'validation-only': ['validation']
    };

    const phases = workflows[workflowName] || [workflowName];
    const results = {};

    for (const phase of phases) {
      console.log(`\n📋 Phase: ${phase}`);
      const swarm = await this.initializeBMADSwarm(phase, options.topology || 'adaptive');
      results[phase] = swarm;
      
      // Wait for phase completion if synchronous
      if (options.synchronous) {
        await this.waitForPhaseCompletion(swarm.swarmId);
      }
    }

    return results;
  }

  /**
   * Wait for a phase to complete
   */
  async waitForPhaseCompletion(swarmId) {
    // Poll Claude Flow for swarm status
    let status = 'running';
    while (status === 'running') {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      try {
        const result = await this.executeClaudeFlow('swarm', 'status', { id: swarmId });
        status = result.status;
      } catch (error) {
        break;
      }
    }
  }
}

// Export singleton instance
module.exports = new ClaudeFlowBridge();