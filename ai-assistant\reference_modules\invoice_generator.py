#!/usr/bin/env python3
"""
Invoice Generator & Billing Module for Leantime MCP Server
Handles invoice creation, billing automation, payment tracking, and financial reporting
Configured for Arab Standard Time (Dubai, UTC+4) and UAE business practices
"""

import os
import json
import uuid
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal, ROUND_HALF_UP
import mysql.connector
import pytz
import re
from jinja2 import Template

class InvoiceGenerator:
    """Comprehensive invoicing and billing system with AI insights"""
    
    def __init__(self, db_pool, openrouter_key: str):
        self.db_pool = db_pool
        self.openrouter_key = openrouter_key
        self.openrouter_base = "https://openrouter.ai/api/v1"
        
        # Dubai timezone
        self.timezone = pytz.timezone('Asia/Dubai')
        
        # UAE business settings
        self.uae_settings = {
            'currency': 'AED',
            'tax_rate': 5.0,  # UAE VAT rate
            'payment_terms_default': 30,  # days
            'late_fee_rate': 1.5,  # % per month
            'business_registration': {
                'required_fields': ['tax_id', 'trade_license'],
                'vat_threshold': 375000  # AED annually
            }
        }
        
        # Payment terms options
        self.payment_terms = {
            'immediate': 0,
            'net_15': 15,
            'net_30': 30,
            'net_45': 45,
            'net_60': 60,
            'net_90': 90
        }
        
        # Invoice templates
        self.invoice_templates = {
            'standard': 'Standard business invoice',
            'detailed': 'Detailed with time breakdown',
            'government': 'UAE government format',
            'minimal': 'Clean minimal design',
            'branded': 'Company branded template'
        }
        
        # Recurring billing frequencies
        self.recurring_frequencies = {
            'weekly': 7,
            'monthly': 30,
            'quarterly': 90,
            'annually': 365
        }
    
    def get_connection(self):
        """Get database connection from pool"""
        return self.db_pool.get_connection()
    
    def get_dubai_time(self) -> datetime:
        """Get current time in Dubai timezone"""
        return datetime.now(self.timezone)
    
    def calculate_uae_tax(self, amount: Decimal, tax_rate: float = None) -> Dict[str, Decimal]:
        """Calculate UAE VAT and return breakdown"""
        
        if tax_rate is None:
            tax_rate = self.uae_settings['tax_rate']
        
        amount = Decimal(str(amount))
        tax_rate_decimal = Decimal(str(tax_rate / 100))
        
        tax_amount = (amount * tax_rate_decimal).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        total_amount = amount + tax_amount
        
        return {
            'subtotal': amount,
            'tax_rate': Decimal(str(tax_rate)),
            'tax_amount': tax_amount,
            'total_amount': total_amount,
            'currency': 'AED'
        }
    
    def generate_invoice_number(self, prefix: str = "INV") -> str:
        """Generate unique invoice number with UAE format"""
        
        dubai_now = self.get_dubai_time()
        year_month = dubai_now.strftime("%Y%m")
        
        # Get next sequential number for this month
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            query = """
                SELECT COUNT(*) + 1 as next_number
                FROM mcp_invoices
                WHERE invoice_number LIKE %s
            """
            
            cursor.execute(query, (f"{prefix}-{year_month}-%",))
            result = cursor.fetchone()
            next_number = result[0] if result else 1
            
            return f"{prefix}-{year_month}-{next_number:04d}"
            
        finally:
            cursor.close()
            conn.close()
    
    async def analyze_invoice_ai(self, invoice_data: Dict[str, Any], 
                                customer_history: List[Dict] = None) -> Dict[str, Any]:
        """AI analysis for invoice optimization and payment prediction"""
        
        customer_context = ""
        if customer_history:
            paid_on_time = len([inv for inv in customer_history if inv.get('payment_status') == 'paid'])
            total_invoices = len(customer_history)
            avg_payment_days = sum([inv.get('payment_days', 30) for inv in customer_history]) / total_invoices if total_invoices > 0 else 30
            
            customer_context = f"""
            Customer Payment History:
            - Total invoices: {total_invoices}
            - Paid on time: {paid_on_time}/{total_invoices}
            - Average payment time: {avg_payment_days:.1f} days
            """
        
        prompt = f"""
        Analyze this invoice for a Dubai-based business:
        
        Invoice Details:
        Amount: {invoice_data.get('total_amount', 0)} AED
        Payment Terms: {invoice_data.get('payment_terms', 30)} days
        Customer Type: {invoice_data.get('customer_type', 'business')}
        Industry: {invoice_data.get('customer_industry', 'unknown')}
        
        {customer_context}
        
        UAE Business Context:
        - 5% VAT standard rate
        - Net 30 is common payment term
        - Consider Ramadan and UAE holidays
        - B2B payments typically slower than B2C
        
        Provide analysis and predictions:
        
        {{
            "payment_probability": 0.85,
            "predicted_payment_date": "2024-02-15",
            "risk_factors": ["factor 1", "factor 2"],
            "optimization_suggestions": ["suggestion 1"],
            "recommended_follow_up_schedule": ["2024-02-01", "2024-02-15"],
            "churn_risk": "low|medium|high",
            "upsell_opportunities": ["opportunity 1"],
            "pricing_feedback": "competitive|high|low",
            "cultural_considerations": "UAE-specific notes",
            "seasonal_factors": "ramadan|summer|normal"
        }}
        
        Respond in JSON format.
        """
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://leantime.io",
                    "X-Title": "Leantime Invoice AI"
                }
                
                payload = {
                    "model": "mistralai/mistral-7b-instruct:free",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1024,
                    "temperature": 0.3
                }
                
                async with session.post(
                    f"{self.openrouter_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        try:
                            if "```json" in content:
                                content = content.split("```json")[1].split("```")[0]
                            elif "```" in content:
                                content = content.split("```")[1].split("```")[0]
                            
                            ai_analysis = json.loads(content.strip())
                            return ai_analysis
                        except json.JSONDecodeError:
                            return self._default_invoice_analysis(invoice_data)
                    else:
                        return self._default_invoice_analysis(invoice_data)
        except Exception as e:
            print(f"AI invoice analysis failed: {e}")
            return self._default_invoice_analysis(invoice_data)
    
    def _default_invoice_analysis(self, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """Default invoice analysis without AI"""
        
        amount = float(invoice_data.get('total_amount', 0))
        payment_terms = int(invoice_data.get('payment_terms', 30))
        
        # Simple risk assessment
        if amount > 10000:
            risk = "high" if payment_terms > 45 else "medium"
        else:
            risk = "low"
        
        predicted_date = self.get_dubai_time() + timedelta(days=payment_terms + 5)
        
        return {
            "payment_probability": 0.8 if risk == "low" else 0.6,
            "predicted_payment_date": predicted_date.strftime("%Y-%m-%d"),
            "risk_factors": ["High amount"] if amount > 10000 else [],
            "optimization_suggestions": ["Send reminder 3 days before due date"],
            "recommended_follow_up_schedule": [
                (self.get_dubai_time() + timedelta(days=payment_terms - 3)).strftime("%Y-%m-%d"),
                (self.get_dubai_time() + timedelta(days=payment_terms + 1)).strftime("%Y-%m-%d")
            ],
            "churn_risk": risk,
            "upsell_opportunities": [],
            "pricing_feedback": "competitive",
            "cultural_considerations": "Standard UAE business practices",
            "seasonal_factors": "normal"
        }
    
    async def create_invoice(self, invoice_data: Dict[str, Any]) -> str:
        """Create a new invoice"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Generate unique identifiers
            invoice_id = f"INV-{uuid.uuid4().hex[:8].upper()}"
            invoice_number = self.generate_invoice_number()
            
            dubai_now = self.get_dubai_time()
            
            # Calculate tax and totals
            line_items = invoice_data.get('line_items', [])
            subtotal = sum(Decimal(str(item['line_total'])) for item in line_items)
            
            tax_calculation = self.calculate_uae_tax(
                subtotal, 
                invoice_data.get('tax_rate', self.uae_settings['tax_rate'])
            )
            
            # Set dates
            invoice_date = invoice_data.get('invoice_date') or dubai_now.date()
            payment_terms = invoice_data.get('payment_terms', self.uae_settings['payment_terms_default'])
            due_date = invoice_date + timedelta(days=payment_terms)
            
            # AI analysis
            ai_analysis = await self.analyze_invoice_ai(invoice_data)
            
            # Insert invoice
            query = """
                INSERT INTO mcp_invoices (
                    invoice_id, invoice_number, customer_id, project_id,
                    title, description, subtotal, tax_rate, tax_amount,
                    discount_amount, total_amount, currency,
                    invoice_date, due_date, status, payment_status,
                    is_recurring, recurring_frequency, recurring_parent_id,
                    template_used, notes, terms,
                    ai_payment_prediction, ai_risk_score
                ) VALUES (
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s,
                    %s, %s
                )
            """
            
            values = (
                invoice_id,
                invoice_number,
                invoice_data['customer_id'],
                invoice_data.get('project_id'),
                invoice_data['title'],
                invoice_data.get('description', ''),
                float(tax_calculation['subtotal']),
                float(tax_calculation['tax_rate']),
                float(tax_calculation['tax_amount']),
                float(invoice_data.get('discount_amount', 0)),
                float(tax_calculation['total_amount']),
                self.uae_settings['currency'],
                invoice_date,
                due_date,
                'draft',
                'unpaid',
                invoice_data.get('is_recurring', False),
                invoice_data.get('recurring_frequency'),
                invoice_data.get('recurring_parent_id'),
                invoice_data.get('template_used', 'standard'),
                invoice_data.get('notes', ''),
                invoice_data.get('terms', ''),
                ai_analysis.get('payment_probability'),
                ai_analysis.get('churn_risk', 'low')
            )
            
            cursor.execute(query, values)
            
            # Insert line items
            for item in line_items:
                await self._create_invoice_line_item(invoice_id, item)
            
            conn.commit()
            
            # Schedule automatic follow-ups if suggested by AI
            follow_up_dates = ai_analysis.get('recommended_follow_up_schedule', [])
            if follow_up_dates:
                await self._schedule_invoice_followups(invoice_id, follow_up_dates)
            
            return invoice_id
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to create invoice: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def _create_invoice_line_item(self, invoice_id: str, item_data: Dict[str, Any]):
        """Create invoice line item"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            query = """
                INSERT INTO mcp_invoice_items (
                    invoice_id, description, quantity, unit_price,
                    line_total, timesheet_entries, product_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                invoice_id,
                item_data['description'],
                float(item_data['quantity']),
                float(item_data['unit_price']),
                float(item_data['line_total']),
                json.dumps(item_data.get('timesheet_entries', [])),
                item_data.get('product_id')
            )
            
            cursor.execute(query, values)
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            print(f"Failed to create line item: {e}")
        finally:
            cursor.close()
            conn.close()
    
    async def _schedule_invoice_followups(self, invoice_id: str, follow_up_dates: List[str]):
        """Schedule automatic follow-up reminders"""
        
        # In a full implementation, this would integrate with a job scheduler
        # For now, we'll store the schedule in the database
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            for i, date_str in enumerate(follow_up_dates):
                query = """
                    INSERT INTO mcp_recurring_tasks (
                        task_id, task_type, title, description,
                        frequency, next_run_at, is_active, config
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                task_id = f"FOLLOW-{invoice_id}-{i+1}"
                
                values = (
                    task_id,
                    'invoice_reminder',
                    f"Invoice Follow-up: {invoice_id}",
                    f"Automated follow-up reminder for invoice {invoice_id}",
                    'once',
                    datetime.fromisoformat(date_str),
                    True,
                    json.dumps({'invoice_id': invoice_id, 'reminder_type': 'payment'})
                )
                
                cursor.execute(query, values)
            
            conn.commit()
            
        except Exception as e:
            print(f"Failed to schedule follow-ups: {e}")
        finally:
            cursor.close()
            conn.close()
    
    async def generate_invoice_from_timesheet(self, project_id: int, 
                                            date_range: Tuple[datetime, datetime],
                                            hourly_rate: float) -> Dict[str, Any]:
        """Generate invoice from Leantime timesheet data"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Get timesheet entries from Leantime
            query = """
                SELECT 
                    t.id,
                    t.userId,
                    t.ticketId,
                    t.hours,
                    t.workDate,
                    t.description,
                    u.firstname,
                    u.lastname,
                    tk.headline as task_name
                FROM zp_timesheets t
                JOIN zp_users u ON t.userId = u.id
                LEFT JOIN zp_tickets tk ON t.ticketId = tk.id
                WHERE t.projectId = %s
                  AND t.workDate BETWEEN %s AND %s
                  AND t.invoicedEmpl = 0
                ORDER BY t.workDate, u.firstname
            """
            
            cursor.execute(query, (project_id, date_range[0], date_range[1]))
            timesheet_entries = cursor.fetchall()
            
            if not timesheet_entries:
                return {"error": "No billable time entries found for the specified period"}
            
            # Group by user and task
            grouped_entries = {}
            total_hours = 0
            
            for entry in timesheet_entries:
                user_key = f"{entry['firstname']} {entry['lastname']}"
                if user_key not in grouped_entries:
                    grouped_entries[user_key] = []
                
                grouped_entries[user_key].append({
                    'date': entry['workDate'].strftime('%Y-%m-%d'),
                    'task': entry['task_name'] or 'General work',
                    'hours': float(entry['hours']),
                    'description': entry['description']
                })
                
                total_hours += float(entry['hours'])
            
            # Create line items
            line_items = []
            for user, entries in grouped_entries.items():
                user_hours = sum(e['hours'] for e in entries)
                user_amount = user_hours * hourly_rate
                
                # Detailed description
                description = f"{user} - {user_hours:.2f} hours\n"
                for entry in entries:
                    description += f"• {entry['date']}: {entry['task']} ({entry['hours']}h)\n"
                
                line_items.append({
                    'description': description.strip(),
                    'quantity': user_hours,
                    'unit_price': hourly_rate,
                    'line_total': user_amount,
                    'timesheet_entries': [e['id'] for e in timesheet_entries if f"{e['firstname']} {e['lastname']}" == user]
                })
            
            return {
                'line_items': line_items,
                'total_hours': total_hours,
                'total_amount': total_hours * hourly_rate,
                'period': f"{date_range[0].strftime('%Y-%m-%d')} to {date_range[1].strftime('%Y-%m-%d')}",
                'timesheet_entries': len(timesheet_entries)
            }
            
        finally:
            cursor.close()
            conn.close()
    
    async def process_payment(self, payment_data: Dict[str, Any]) -> str:
        """Process and record payment against invoice"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            payment_id = f"PAY-{uuid.uuid4().hex[:8].upper()}"
            dubai_now = self.get_dubai_time()
            
            # Insert payment record
            query = """
                INSERT INTO mcp_payments (
                    payment_id, invoice_id, amount, payment_date,
                    payment_method, reference_number, gateway,
                    gateway_transaction_id, status, processing_fee, net_amount, notes
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            processing_fee = float(payment_data.get('processing_fee', 0))
            amount = float(payment_data['amount'])
            net_amount = amount - processing_fee
            
            values = (
                payment_id,
                payment_data['invoice_id'],
                amount,
                payment_data.get('payment_date', dubai_now.date()),
                payment_data['payment_method'],
                payment_data.get('reference_number'),
                payment_data.get('gateway'),
                payment_data.get('gateway_transaction_id'),
                payment_data.get('status', 'completed'),
                processing_fee,
                net_amount,
                payment_data.get('notes', '')
            )
            
            cursor.execute(query, values)
            
            # Update invoice payment status
            cursor.execute("SELECT total_amount, paid_amount FROM mcp_invoices WHERE invoice_id = %s", 
                          (payment_data['invoice_id'],))
            
            invoice_result = cursor.fetchone()
            if invoice_result:
                total_amount = float(invoice_result[0])
                current_paid = float(invoice_result[1] or 0)
                new_paid_amount = current_paid + amount
                
                if new_paid_amount >= total_amount:
                    payment_status = 'paid'
                    invoice_status = 'paid'
                    paid_date = dubai_now.date()
                elif new_paid_amount > 0:
                    payment_status = 'partial'
                    invoice_status = 'sent'
                    paid_date = None
                else:
                    payment_status = 'unpaid'
                    invoice_status = 'sent'
                    paid_date = None
                
                cursor.execute("""
                    UPDATE mcp_invoices 
                    SET paid_amount = %s, payment_status = %s, status = %s, paid_date = %s
                    WHERE invoice_id = %s
                """, (new_paid_amount, payment_status, invoice_status, paid_date, payment_data['invoice_id']))
            
            conn.commit()
            return payment_id
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to process payment: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def generate_financial_report(self, period: str = "month") -> Dict[str, Any]:
        """Generate comprehensive financial report"""
        
        dubai_now = self.get_dubai_time()
        
        # Calculate period
        if period == "week":
            start_date = dubai_now - timedelta(days=7)
        elif period == "month":
            start_date = dubai_now - timedelta(days=30)
        elif period == "quarter":
            start_date = dubai_now - timedelta(days=90)
        else:  # year
            start_date = dubai_now - timedelta(days=365)
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Invoice metrics
            invoice_query = """
                SELECT 
                    COUNT(*) as total_invoices,
                    SUM(total_amount) as total_invoiced,
                    SUM(paid_amount) as total_paid,
                    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_invoices,
                    SUM(CASE WHEN payment_status = 'unpaid' AND due_date < CURDATE() THEN total_amount ELSE 0 END) as overdue_amount,
                    AVG(DATEDIFF(paid_date, invoice_date)) as avg_payment_days
                FROM mcp_invoices
                WHERE invoice_date >= %s AND invoice_date <= %s
            """
            
            cursor.execute(invoice_query, (start_date.date(), dubai_now.date()))
            invoice_metrics = cursor.fetchone()
            
            # Revenue by customer
            revenue_query = """
                SELECT 
                    c.company_name,
                    SUM(i.total_amount) as revenue,
                    COUNT(i.id) as invoice_count
                FROM mcp_invoices i
                JOIN mcp_customers c ON i.customer_id = c.id
                WHERE i.invoice_date >= %s AND i.invoice_date <= %s
                GROUP BY c.id, c.company_name
                ORDER BY revenue DESC
                LIMIT 10
            """
            
            cursor.execute(revenue_query, (start_date.date(), dubai_now.date()))
            top_customers = cursor.fetchall()
            
            # Monthly trend
            trend_query = """
                SELECT 
                    DATE_FORMAT(invoice_date, '%Y-%m') as month,
                    SUM(total_amount) as invoiced,
                    SUM(paid_amount) as collected
                FROM mcp_invoices
                WHERE invoice_date >= %s
                GROUP BY DATE_FORMAT(invoice_date, '%Y-%m')
                ORDER BY month
            """
            
            cursor.execute(trend_query, (start_date.date(),))
            monthly_trend = cursor.fetchall()
            
            # Process results
            for metric in [invoice_metrics]:
                if metric:
                    for key, value in metric.items():
                        if isinstance(value, Decimal):
                            metric[key] = float(value)
            
            for customer in top_customers:
                if customer['revenue']:
                    customer['revenue'] = float(customer['revenue'])
            
            for trend in monthly_trend:
                if trend['invoiced']:
                    trend['invoiced'] = float(trend['invoiced'])
                if trend['collected']:
                    trend['collected'] = float(trend['collected'])
            
            return {
                "period": period,
                "date_range": f"{start_date.date()} to {dubai_now.date()}",
                "summary": {
                    "total_invoices": invoice_metrics['total_invoices'] or 0,
                    "total_invoiced": invoice_metrics['total_invoiced'] or 0,
                    "total_collected": invoice_metrics['total_paid'] or 0,
                    "collection_rate": (invoice_metrics['total_paid'] / invoice_metrics['total_invoiced'] * 100) if invoice_metrics['total_invoiced'] else 0,
                    "paid_invoices": invoice_metrics['paid_invoices'] or 0,
                    "overdue_amount": invoice_metrics['overdue_amount'] or 0,
                    "avg_payment_days": invoice_metrics['avg_payment_days'] or 0,
                    "currency": self.uae_settings['currency']
                },
                "top_customers": top_customers,
                "monthly_trend": monthly_trend,
                "generated_at": dubai_now.isoformat(),
                "timezone": "Asia/Dubai"
            }
            
        finally:
            cursor.close()
            conn.close()
    
    async def create_recurring_invoice(self, template_invoice_id: str) -> Optional[str]:
        """Create new invoice from recurring template"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Get template invoice
            cursor.execute("SELECT * FROM mcp_invoices WHERE invoice_id = %s", (template_invoice_id,))
            template = cursor.fetchone()
            
            if not template or not template['is_recurring']:
                return None
            
            # Get line items
            cursor.execute("SELECT * FROM mcp_invoice_items WHERE invoice_id = %s", (template_invoice_id,))
            template_items = cursor.fetchall()
            
            # Create new invoice data
            dubai_now = self.get_dubai_time()
            
            new_invoice_data = {
                'customer_id': template['customer_id'],
                'project_id': template['project_id'],
                'title': f"{template['title']} - {dubai_now.strftime('%B %Y')}",
                'description': template['description'],
                'tax_rate': float(template['tax_rate']),
                'discount_amount': float(template['discount_amount'] or 0),
                'payment_terms': 30,  # From template or default
                'template_used': template['template_used'],
                'notes': template['notes'],
                'terms': template['terms'],
                'recurring_parent_id': template['id'],
                'line_items': []
            }
            
            # Add line items
            for item in template_items:
                new_invoice_data['line_items'].append({
                    'description': item['description'],
                    'quantity': float(item['quantity']),
                    'unit_price': float(item['unit_price']),
                    'line_total': float(item['line_total']),
                    'product_id': item['product_id']
                })
            
            # Create new invoice
            new_invoice_id = await self.create_invoice(new_invoice_data)
            
            # Update next billing date for template
            frequency = template['recurring_frequency']
            if frequency in self.recurring_frequencies:
                days_to_add = self.recurring_frequencies[frequency]
                next_date = dubai_now + timedelta(days=days_to_add)
                
                # In a full implementation, schedule this in a job queue
                print(f"Next recurring invoice for {template_invoice_id} scheduled for {next_date.date()}")
            
            return new_invoice_id
            
        except Exception as e:
            print(f"Failed to create recurring invoice: {e}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    def generate_invoice_html(self, invoice_id: str) -> str:
        """Generate HTML invoice for email/PDF"""
        
        # This is a simplified version - in production, use proper templating
        html_template = """
        <!DOCTYPE html>
        <html dir="ltr" lang="en">
        <head>
            <meta charset="UTF-8">
            <title>Invoice {{ invoice_number }}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
                .header { border-bottom: 2px solid #0066cc; padding-bottom: 20px; margin-bottom: 30px; }
                .company-info { float: right; text-align: right; }
                .invoice-details { clear: both; margin-bottom: 30px; }
                .line-items { width: 100%; border-collapse: collapse; }
                .line-items th, .line-items td { border: 1px solid #ddd; padding: 10px; text-align: left; }
                .totals { float: right; margin-top: 20px; }
                .footer { margin-top: 50px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>INVOICE</h1>
                <div class="company-info">
                    <strong>Your Company Name</strong><br>
                    Address Line 1<br>
                    Dubai, UAE<br>
                    TRN: 123456789012345
                </div>
            </div>
            
            <div class="invoice-details">
                <strong>Invoice #:</strong> {{ invoice_number }}<br>
                <strong>Date:</strong> {{ invoice_date }}<br>
                <strong>Due Date:</strong> {{ due_date }}<br>
                <strong>Customer:</strong> {{ customer_name }}
            </div>
            
            <table class="line-items">
                <thead>
                    <tr><th>Description</th><th>Qty</th><th>Rate</th><th>Amount</th></tr>
                </thead>
                <tbody>
                    {% for item in line_items %}
                    <tr>
                        <td>{{ item.description }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.unit_price }} AED</td>
                        <td>{{ item.line_total }} AED</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <div class="totals">
                <strong>Subtotal:</strong> {{ subtotal }} AED<br>
                <strong>VAT (5%):</strong> {{ tax_amount }} AED<br>
                <h3>Total: {{ total_amount }} AED</h3>
            </div>
            
            <div class="footer">
                Payment Terms: {{ payment_terms }} days<br>
                Thank you for your business!
            </div>
        </body>
        </html>
        """
        
        # In production, fetch invoice data and render with Jinja2
        return "HTML invoice template placeholder"