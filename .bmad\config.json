{"version": "1.0.0", "framework": "BMAD-Claude Integrated Framework", "description": "BMAD METHOD integrated with Claude Flow for context-engineered AI development", "phases": {"planning": {"enabled": true, "agents": ["analyst", "product-manager", "architect"], "outputs": ["PRD", "architecture-doc", "tech-spec"]}, "development": {"enabled": true, "agents": ["scrum-master", "developer", "reviewer"], "storyFormat": "context-engineered", "embedContext": true}, "validation": {"enabled": true, "agents": ["tester", "qa-engineer", "production-validator"]}}, "agentFactory": {"dynamicCreation": true, "templateSources": [".bmad/agents/", ".swarm/agents/", "awesome-claude-patterns"], "autoSpawn": {"enabled": true, "maxAgents": 10, "topology": "adaptive"}}, "contextEngineering": {"autoEmbed": true, "includeArchitecture": true, "includeRequirements": true, "includeExamples": true, "maxContextSize": 32000}, "workflow": {"commands": {"init": "Initialize BMAD project structure", "plan": "Run planning phase with AI agents", "develop": "Execute context-engineered development", "validate": "Run validation and testing phase", "agent": "Manage dynamic agent creation"}, "hooks": {"prePlanning": [], "postPlanning": ["generate-stories"], "preDevelopment": ["load-context"], "postDevelopment": ["run-tests", "update-docs"]}}, "integration": {"claudeFlow": {"enabled": true, "syncAgents": true, "sharedMemory": true}, "githubActions": {"enabled": false, "automate": false}}}