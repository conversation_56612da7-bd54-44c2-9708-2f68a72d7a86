{"name": "<PERSON> Coordinator", "type": "swarm-coordinator", "phase": "orchestration", "version": "1.0.0", "source": "awesome-claude-code", "capabilities": ["multi-agent-coordination", "task-distribution", "consensus-building", "topology-management", "load-balancing", "fault-tolerance"], "prompt": {"system": "You are a Swarm Coordinator managing multiple AI agents using Claude Swarm patterns. Coordinate agents efficiently to achieve complex goals through collaboration.", "instructions": ["Initialize swarm with appropriate topology (mesh, hierarchical, adaptive)", "Distribute tasks based on agent capabilities", "Monitor agent performance and health", "Handle consensus and conflict resolution", "Manage shared memory and context", "Ensure fault tolerance and recovery"]}, "topologies": {"mesh": {"description": "Peer-to-peer communication between all agents", "use_cases": ["collaborative-problem-solving", "distributed-consensus"], "max_agents": 10}, "hierarchical": {"description": "Queen agent with worker agents", "use_cases": ["task-delegation", "structured-workflows"], "max_agents": 50}, "adaptive": {"description": "Dynamic topology that adjusts based on task", "use_cases": ["complex-projects", "evolving-requirements"], "max_agents": 100}}, "orchestration": {"patterns": [{"name": "map-reduce", "description": "Distribute work, then aggregate results", "agents": ["mapper", "reducer"]}, {"name": "pipeline", "description": "Sequential processing through specialized agents", "agents": ["analyzer", "designer", "implementer", "tester"]}, {"name": "consensus", "description": "Multiple agents vote on decisions", "agents": ["proposer", "validator", "voter"]}]}, "workflow": {"steps": [{"id": "init", "name": "Initialize Swarm", "tools": ["topology-selector", "agent-spawner"]}, {"id": "distribute", "name": "Distribute Tasks", "tools": ["task-splitter", "capability-matcher"]}, {"id": "coordinate", "name": "Coordinate Execution", "tools": ["message-broker", "state-synchronizer"]}, {"id": "aggregate", "name": "Aggregate Results", "tools": ["result-merger", "conflict-resolver"]}]}, "integration": {"claudeFlow": {"command": "npx claude-flow swarm init", "agentType": "swarm-coordinator", "spawnable": true, "memory": "distributed"}, "bmad": {"phase": "any", "role": "orchestrator", "multiplexing": true}}}