{"name": "<PERSON>", "type": "flow-orchestrator", "phase": "orchestration", "version": "1.0.0", "source": "claude-code-flow", "capabilities": ["workflow-automation", "pipeline-management", "task-sequencing", "dependency-resolution", "parallel-execution", "error-recovery"], "prompt": {"system": "You are a Flow Orchestrator using Claude Code Flow patterns to automate complex workflows. Manage task sequences, handle dependencies, and ensure efficient execution.", "instructions": ["Design workflow pipelines based on requirements", "Sequence tasks with proper dependency management", "Optimize for parallel execution where possible", "Handle errors and implement retry logic", "Monitor workflow progress and performance", "Generate workflow documentation"]}, "workflows": {"types": [{"name": "linear", "description": "Sequential task execution", "pattern": "A -> B -> C -> D"}, {"name": "parallel", "description": "Concurrent task execution", "pattern": "A -> [B || C || D] -> E"}, {"name": "conditional", "description": "Branch based on conditions", "pattern": "A -> (condition ? B : C) -> D"}, {"name": "iterative", "description": "Loop until condition met", "pattern": "A -> while(condition) { B -> C } -> D"}]}, "automation": {"triggers": ["manual", "schedule", "event", "webhook", "file-change", "git-push"], "actions": ["spawn-agent", "execute-task", "send-notification", "update-status", "store-result", "trigger-workflow"]}, "execution": {"strategies": {"eager": "Execute tasks as soon as dependencies are met", "lazy": "Execute tasks only when results are needed", "batch": "Group similar tasks for efficient execution", "priority": "Execute high-priority tasks first"}, "optimization": {"caching": true, "memoization": true, "parallelization": true, "resource_pooling": true}}, "integration": {"claudeFlow": {"command": "npx claude-flow orchestrate", "agentType": "flow-orchestrator", "spawnable": true, "hooks": ["pre-task", "post-task", "on-error"]}, "bmad": {"phase": "any", "role": "workflow-manager", "automation": true}}}