#!/usr/bin/env python3
"""
Network Configuration Specialist Agent - Odoo-Leantime MCP Integration
Deploy standalone bridge server and resolve networking issues
"""

import json
import os
import asyncio
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import structlog

logger = structlog.get_logger()

class NetworkConfigAgent:
    """Network Configuration specialist agent for deployment"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.deployment_results = {
            "environment_setup": [],
            "dependencies_installed": [],
            "server_deployed": False,
            "network_configuration": [],
            "service_status": {}
        }

    async def setup_python_environment(self):
        """Setup Python virtual environment and dependencies"""
        logger.info("Setting up Python environment for bridge server")

        venv_path = self.project_root / "venv"

        try:
            # Create virtual environment if it doesn't exist
            if not venv_path.exists():
                subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
                self.deployment_results["environment_setup"].append("Created Python virtual environment")

            # Determine pip path based on OS
            if os.name == 'nt':  # Windows
                pip_path = venv_path / "Scripts" / "pip.exe"
                python_path = venv_path / "Scripts" / "python.exe"
            else:  # Linux/Mac
                pip_path = venv_path / "bin" / "pip"
                python_path = venv_path / "bin" / "python"

            # Install required dependencies
            dependencies = [
                "fastapi>=0.104.0",
                "uvicorn[standard]>=0.24.0",
                "mysql-connector-python>=8.2.0",
                "aiohttp>=3.9.0",
                "structlog>=23.2.0",
                "cryptography>=41.0.0",
                "pydantic>=2.5.0",
                "pydantic-settings>=2.1.0",
                "pytz>=2023.3",
                "slowapi>=0.1.9",
                "python-multipart>=0.0.6"
            ]

            for dep in dependencies:
                try:
                    subprocess.run([str(pip_path), "install", dep], check=True, capture_output=True)
                    self.deployment_results["dependencies_installed"].append(dep)
                    logger.info("Installed dependency", dependency=dep)
                except subprocess.CalledProcessError as e:
                    logger.warning("Failed to install dependency", dependency=dep, error=str(e))

            return str(python_path)

        except Exception as e:
            logger.error("Error setting up Python environment", error=str(e))
            raise

    async def create_deployment_scripts(self):
        """Create deployment and startup scripts"""
        logger.info("Creating deployment scripts")

        scripts_dir = self.project_root / "scripts"
        scripts_dir.mkdir(exist_ok=True)

        # Windows startup script
        windows_script = scripts_dir / "start_bridge_server.bat"
        with open(windows_script, 'w') as f:
            f.write(f'''@echo off
echo Starting Odoo-Leantime MCP Bridge Server...

cd /d "{self.project_root}"

REM Activate virtual environment
call venv\\Scripts\\activate.bat

REM Set environment variables
set PYTHONPATH={self.project_root}

REM Start the bridge server
python odoo-bridge\\odoo_bridge_server.py

pause
''')

        # Linux startup script
        linux_script = scripts_dir / "start_bridge_server.sh"
        with open(linux_script, 'w') as f:
            f.write(f'''#!/bin/bash
echo "Starting Odoo-Leantime MCP Bridge Server..."

cd "{self.project_root}"

# Activate virtual environment
source venv/bin/activate

# Set environment variables
export PYTHONPATH="{self.project_root}"

# Start the bridge server
python odoo-bridge/odoo_bridge_server.py
''')

        # Make Linux script executable
        if os.name != 'nt':
            os.chmod(linux_script, 0o755)

        # Create systemd service file for Linux
        systemd_service = scripts_dir / "odoo-leantime-bridge.service"
        with open(systemd_service, 'w') as f:
            f.write(f'''[Unit]
Description=Odoo-Leantime MCP Bridge Server
After=network.target mysql.service

[Service]
Type=simple
User=nobody
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}
ExecStart={self.project_root}/venv/bin/python {self.project_root}/odoo-bridge/odoo_bridge_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
''')

        # Create Windows service installer
        windows_service_script = scripts_dir / "install_windows_service.bat"
        with open(windows_service_script, 'w') as f:
            f.write(f'''@echo off
echo Installing Odoo-Leantime Bridge as Windows Service...

REM Install NSSM (Non-Sucking Service Manager) if not present
where nssm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo NSSM not found. Please install NSSM first.
    echo Download from: https://nssm.cc/download
    pause
    exit /b 1
)

REM Install service
nssm install "OdooLeantimeBridge" "{self.project_root}\\venv\\Scripts\\python.exe"
nssm set "OdooLeantimeBridge" AppParameters "{self.project_root}\\odoo-bridge\\odoo_bridge_server.py"
nssm set "OdooLeantimeBridge" AppDirectory "{self.project_root}"
nssm set "OdooLeantimeBridge" DisplayName "Odoo-Leantime MCP Bridge"
nssm set "OdooLeantimeBridge" Description "MCP Bridge Server for Odoo-Leantime Integration"

echo Service installed successfully!
echo Use 'net start OdooLeantimeBridge' to start the service
echo Use 'net stop OdooLeantimeBridge' to stop the service
echo Use 'nssm remove OdooLeantimeBridge' to uninstall the service

pause
''')

        self.deployment_results["environment_setup"].extend([
            "Created Windows startup script",
            "Created Linux startup script",
            "Created systemd service file",
            "Created Windows service installer"
        ])

        logger.info("Deployment scripts created successfully")

    async def configure_environment_variables(self):
        """Configure environment variables for bridge server"""
        logger.info("Configuring environment variables")

        env_file = self.project_root / ".env"

        # Read existing .env
        env_content = ""
        if env_file.exists():
            with open(env_file, 'r') as f:
                env_content = f.read()

        # Add bridge server specific configurations
        bridge_config = '''
# Bridge Server Configuration
BRIDGE_HOST=0.0.0.0
BRIDGE_PORT=8070
BRIDGE_DEBUG=false
BRIDGE_WORKERS=4

# Security Configuration
BRIDGE_ENABLE_HTTPS=false
BRIDGE_SSL_CERT_PATH=
BRIDGE_SSL_KEY_PATH=

# Performance Configuration
BRIDGE_CONNECTION_POOL_SIZE=10
BRIDGE_MAX_CONNECTIONS=100
BRIDGE_REQUEST_TIMEOUT=30

# Logging Configuration
BRIDGE_LOG_LEVEL=INFO
BRIDGE_LOG_FILE=logs/bridge_server.log

# Health Check Configuration
BRIDGE_HEALTH_CHECK_INTERVAL=60
BRIDGE_ENABLE_METRICS=true
'''

        # Only add if not already present
        if "BRIDGE_HOST" not in env_content:
            with open(env_file, 'a') as f:
                f.write('\n' + bridge_config)

            self.deployment_results["network_configuration"].append("Added bridge server environment variables")

        # Create logs directory
        logs_dir = self.project_root / "logs"
        logs_dir.mkdir(exist_ok=True)

        logger.info("Environment variables configured")

    async def test_database_connections(self):
        """Test database connections before deployment"""
        logger.info("Testing database connections")

        connection_results = {
            "odoo": {"status": "unknown", "error": None},
            "leantime": {"status": "unknown", "error": None}
        }

        # Test Leantime database connection
        try:
            import mysql.connector
            from dotenv import load_dotenv

            load_dotenv(self.project_root / ".env")

            # Leantime connection test
            leantime_config = {
                'host': os.getenv('LEAN_DB_HOST', 'localhost'),
                'user': os.getenv('LEAN_DB_USER', 'lean'),
                'password': os.getenv('LEAN_DB_PASSWORD', ''),
                'database': os.getenv('LEAN_DB_DATABASE', 'leantime'),
                'port': int(os.getenv('LEAN_DB_PORT', 3306))
            }

            conn = mysql.connector.connect(**leantime_config)
            if conn.is_connected():
                connection_results["leantime"]["status"] = "connected"
                conn.close()
            else:
                connection_results["leantime"]["status"] = "failed"

        except Exception as e:
            connection_results["leantime"]["status"] = "failed"
            connection_results["leantime"]["error"] = str(e)

        # Test Odoo connection
        try:
            import xmlrpc.client

            odoo_url = os.getenv('ODOO_URL', 'http://localhost:8069')
            odoo_db = os.getenv('ODOO_DB', 'odoo')
            odoo_username = os.getenv('ODOO_USERNAME', 'admin')
            odoo_password = os.getenv('ODOO_PASSWORD', 'admin')

            common = xmlrpc.client.ServerProxy(f'{odoo_url}/xmlrpc/2/common')
            uid = common.authenticate(odoo_db, odoo_username, odoo_password, {})

            if uid:
                connection_results["odoo"]["status"] = "connected"
            else:
                connection_results["odoo"]["status"] = "authentication_failed"

        except Exception as e:
            connection_results["odoo"]["status"] = "failed"
            connection_results["odoo"]["error"] = str(e)

        self.deployment_results["service_status"]["database_connections"] = connection_results
        logger.info("Database connection test completed", results=connection_results)

        return connection_results

    async def deploy_bridge_server(self, python_path: str):
        """Deploy the bridge server"""
        logger.info("Deploying bridge server")

        try:
            # Test the bridge server startup
            bridge_server_path = self.project_root / "odoo-bridge" / "odoo_bridge_server.py"

            if not bridge_server_path.exists():
                raise FileNotFoundError("Bridge server file not found")

            # Start server in test mode to verify it loads
            test_cmd = [
                str(python_path),
                str(bridge_server_path),
                "--test-mode"
            ]

            # Set environment variables
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.project_root)

            # Quick syntax check
            syntax_check = subprocess.run([
                str(python_path), "-m", "py_compile", str(bridge_server_path)
            ], capture_output=True, text=True, env=env)

            if syntax_check.returncode == 0:
                self.deployment_results["server_deployed"] = True
                self.deployment_results["environment_setup"].append("Bridge server syntax validation passed")
                logger.info("Bridge server deployed successfully")
            else:
                logger.error("Bridge server syntax validation failed", error=syntax_check.stderr)
                raise Exception(f"Syntax validation failed: {syntax_check.stderr}")

        except Exception as e:
            logger.error("Error deploying bridge server", error=str(e))
            raise

    async def create_health_monitoring(self):
        """Create health monitoring and status endpoints"""
        logger.info("Setting up health monitoring")

        monitoring_script = self.project_root / "scripts" / "health_monitor.py"

        monitoring_content = '''#!/usr/bin/env python3
"""
Health Monitoring Script for Odoo-Leantime Bridge Server
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import time

class HealthMonitor:
    """Monitor bridge server health and status"""

    def __init__(self, bridge_url: str = "http://localhost:8070"):
        self.bridge_url = bridge_url
        self.health_history = []

    async def check_health(self):
        """Check bridge server health"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.bridge_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        health_data['timestamp'] = datetime.now().isoformat()
                        health_data['response_time'] = time.time()
                        self.health_history.append(health_data)
                        return health_data
                    else:
                        return {"status": "unhealthy", "http_status": response.status}
        except Exception as e:
            return {"status": "unreachable", "error": str(e)}

    async def check_endpoints(self):
        """Check all MCP endpoints"""
        endpoints = [
            "/health",
            "/mcp/projects",
            "/mcp/customers",
            "/mcp/users",
            "/admin/status"
        ]

        results = {}

        async with aiohttp.ClientSession() as session:
            for endpoint in endpoints:
                try:
                    async with session.get(f"{self.bridge_url}{endpoint}") as response:
                        results[endpoint] = {
                            "status": response.status,
                            "accessible": response.status < 500
                        }
                except Exception as e:
                    results[endpoint] = {
                        "status": "error",
                        "error": str(e),
                        "accessible": False
                    }

        return results

    async def generate_status_report(self):
        """Generate comprehensive status report"""
        health = await self.check_health()
        endpoints = await self.check_endpoints()

        report = {
            "timestamp": datetime.now().isoformat(),
            "bridge_health": health,
            "endpoint_status": endpoints,
            "overall_status": "healthy" if health.get("status") == "healthy" else "unhealthy"
        }

        return report

async def main():
    monitor = HealthMonitor()
    report = await monitor.generate_status_report()
    print(json.dumps(report, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
'''

        with open(monitoring_script, 'w') as f:
            f.write(monitoring_content)

        # Make executable on Linux
        if os.name != 'nt':
            os.chmod(monitoring_script, 0o755)

        self.deployment_results["environment_setup"].append("Created health monitoring script")
        logger.info("Health monitoring setup completed")

    async def save_deployment_results(self):
        """Save deployment results to file"""
        output_dir = self.project_root / ".swarm" / "deployment"
        output_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().isoformat()

        # Save deployment results
        deployment_file = output_dir / f"network_deployment_{timestamp.replace(':', '-')}.json"
        with open(deployment_file, 'w') as f:
            json.dump({
                "timestamp": timestamp,
                "agent": "NetworkConfig",
                "deployment_results": self.deployment_results
            }, f, indent=2)

        logger.info("Deployment results saved", file=str(deployment_file))
        return str(deployment_file)

    async def execute_deployment(self):
        """Execute complete network configuration and deployment"""
        logger.info("Starting Network Configuration Agent execution")

        try:
            # Execute deployment phases
            python_path = await self.setup_python_environment()
            await self.create_deployment_scripts()
            await self.configure_environment_variables()

            # Test connections (non-blocking)
            connection_results = await self.test_database_connections()

            await self.deploy_bridge_server(python_path)
            await self.create_health_monitoring()

            # Save results
            output_file = await self.save_deployment_results()

            deployment_success = self.deployment_results["server_deployed"]
            environment_items = len(self.deployment_results["environment_setup"])

            logger.info("Network Configuration Agent completed successfully",
                       output=output_file,
                       deployment_success=deployment_success)

            return {
                "status": "completed",
                "agent": "NetworkConfig",
                "output_file": output_file,
                "deployment_summary": {
                    "server_deployed": deployment_success,
                    "environment_setup_items": environment_items,
                    "dependencies_installed": len(self.deployment_results["dependencies_installed"]),
                    "network_configurations": len(self.deployment_results["network_configuration"])
                },
                "database_connections": connection_results,
                "next_actions": [
                    f"Start bridge server using: {python_path} odoo-bridge/odoo_bridge_server.py",
                    "Monitor health using: python scripts/health_monitor.py",
                    "Check logs in: logs/bridge_server.log",
                    "Access API docs at: http://localhost:8070/docs"
                ]
            }

        except Exception as e:
            logger.error("Network Configuration Agent failed", error=str(e))
            return {
                "status": "failed",
                "agent": "NetworkConfig",
                "error": str(e)
            }

async def main():
    """Main execution for Network Configuration Agent"""
    project_root = r"C:\Users\<USER>\projects\docker-leantime"
    agent = NetworkConfigAgent(project_root)
    result = await agent.execute_deployment()
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())