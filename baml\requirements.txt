# BAML Service Dependencies for Leantime Integration

# Core dependencies
baml-py>=0.46.0
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# Database and caching
mysql-connector-python>=8.2.0
redis>=5.0.1
sqlalchemy>=2.0.23

# HTTP and networking
httpx>=0.25.2
aiohttp>=3.9.1
requests>=2.31.0

# Data processing and validation
pandas>=2.1.4
numpy>=1.24.0
python-dateutil>=2.8.2
pytz>=2023.3

# Configuration and environment
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2

# Logging and monitoring
structlog>=23.2.0
prometheus-client>=0.19.0
opencensus-ext-flask>=0.8.0

# Security and authentication
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
cryptography>=41.0.8

# Testing (development)
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
httpx>=0.25.2

# Data science and ML utilities
scikit-learn>=1.3.2
matplotlib>=3.8.2
seaborn>=0.13.0

# Async utilities
asyncio-mqtt>=0.16.1
aiofiles>=23.2.1
aioredis>=2.0.1

# Development and debugging
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.1
pre-commit>=3.6.0

# Documentation
mkdocs>=1.5.3
mkdocs-material>=9.4.14

# Performance monitoring
psutil>=5.9.6
memory-profiler>=0.61.0