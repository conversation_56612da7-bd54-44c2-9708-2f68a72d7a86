# Hybrid Email Architecture Documentation
## Leantime System with Cloudflare Email Enhancement

**Version:** 1.0
**Date:** 2025-09-25
**Maintainer:** <PERSON>

---

## 🎯 Executive Summary

The Leantime system implements a comprehensive hybrid email architecture that combines multiple technologies to provide robust, secure, and professional email capabilities:

- **Inbound Processing:** Cloudflare Email Routing for professional addresses
- **Outbound Delivery:** Node.js Email Service with Resend API integration
- **Security:** SPF, DKIM, DMARC records with multi-layer authentication
- **Reliability:** Multi-provider failover and queue-based processing
- **Professional Branding:** Custom domain emails (@dxbmeta.com)

---

## 📊 System Architecture Overview

```mermaid
graph TB
    subgraph "External Email Sources"
        A1[External Senders]
        A2[Leantime Application]
        A3[System Notifications]
    end

    subgraph "Cloudflare Email Routing"
        B1[Email Routing Rules]
        B2[Security Filters]
        B3[Forwarding Logic]
        B4[Webhook Notifications]
    end

    subgraph "Node.js Email Service"
        C1[SMTP Server :2525]
        C2[HTTP API :3000]
        C3[Email Queue Redis]
        C4[Provider Manager]
    end

    subgraph "Email Providers"
        D1[Resend API Primary]
        D2[Brevo SMTP Fallback]
        D3[Gmail OAuth2 Backup]
    end

    subgraph "Destinations"
        E1[<EMAIL>]
        E2[Support Teams]
        E3[Department Addresses]
    end

    A1 --> B1
    A2 --> C1
    A3 --> C1

    B1 --> B2
    B2 --> B3
    B3 --> E1
    B4 --> C2

    C1 --> C3
    C2 --> C3
    C3 --> C4
    C4 --> D1
    C4 --> D2
    C4 --> D3

    D1 --> E1
    D2 --> E2
    D3 --> E3
```

---

## 🔄 Email Flow Diagrams

### Inbound Email Flow (Cloudflare Email Routing)

```mermaid
sequenceDiagram
    participant ES as External Sender
    participant CF as Cloudflare Email Routing
    participant CW as Cloudflare Worker
    participant LT as Leantime Webhook
    participant FD as Final Destination

    ES->>CF: <NAME_EMAIL>
    CF->>CW: Process Email
    CW->>CW: Security Checks (SPF/DKIM/Spam)
    CW->>CW: Apply Routing Rules
    CW->>FD: <NAME_EMAIL>
    CW->>LT: Send Webhook Notification (Optional)
    LT-->>CW: Acknowledge Receipt
    FD->>FD: Email Delivered
```

### Outbound Email Flow (Node.js Email Service)

```mermaid
sequenceDiagram
    participant LT as Leantime Application
    participant ES as Email Service
    participant RD as Redis Queue
    participant PM as Provider Manager
    participant RP as Resend API
    participant BF as Brevo Fallback
    participant RX as Recipient

    LT->>ES: SMTP Connection (port 2525)
    ES->>RD: Queue Email
    RD->>PM: Process Queue Job
    PM->>RP: Send via Resend API
    alt Success
        RP->>RX: Email Delivered
        RP-->>PM: Success Response
    else Failure
        RP-->>PM: Error Response
        PM->>BF: Fallback to Brevo
        BF->>RX: Email Delivered
    end
    PM-->>ES: Update Status
    ES-->>LT: SMTP Response
```

---

## 🏗️ Component Architecture

### 1. Cloudflare Email Routing

**Purpose:** Handle inbound emails to professional addresses
**Location:** Cloudflare Dashboard → Email Routing
**Domain:** dxbmeta.com

#### Professional Email Addresses
```
<EMAIL>        → <EMAIL>
<EMAIL>     → <EMAIL>
<EMAIL>       → <EMAIL>
<EMAIL>     → <EMAIL>
<EMAIL> → <EMAIL>
<EMAIL>      → <EMAIL>
```

#### Key Features
- **Zero Cost:** Unlimited email forwarding
- **Instant Processing:** Sub-5 second forwarding latency
- **Security Integration:** Built-in spam and security filtering
- **Custom Rules:** Advanced routing based on sender/subject patterns

### 2. Cloudflare Worker (Advanced Processing)

**File:** `cloudflare-email-worker.js`
**Purpose:** Enhanced email processing with custom logic

#### Capabilities
- **Security Filtering:** SPF/DKIM validation, spam detection
- **Custom Routing:** Pattern-based email routing rules
- **Webhook Integration:** Notify Leantime of incoming emails
- **Threat Protection:** Block suspicious domains and content

#### Configuration Variables
```javascript
// Required
DEFAULT_EMAIL: "<EMAIL>"

// Optional
SUPPORT_EMAIL: "<EMAIL>"
ADMIN_EMAIL: "<EMAIL>"
INFO_EMAIL: "<EMAIL>"
LEANTIME_WEBHOOK_URL: "https://admin.dxbmeta.com/api/email-webhook"
LEANTIME_WEBHOOK_TOKEN: "[secure_token]"
```

### 3. Node.js Email Service

**Directory:** `email-service/`
**Container:** `leantime-email-service`
**Purpose:** Outbound email processing with multi-provider support

#### Core Components
- **SMTP Server:** Internal relay on port 2525
- **HTTP API:** REST endpoints on port 3000
- **Redis Queue:** Reliable email queuing and retry logic
- **Provider Manager:** Multi-provider failover system
- **Dashboard:** Web-based monitoring interface

#### Service Architecture
```javascript
// Main Components
- server.js           // Main application server
- providers/manager.js // Email provider management
- templates/manager.js // Email template system
- queue/             // Redis queue configuration
- config/            // Service configuration
```

#### API Endpoints
```
GET  /health                    # Service health check
POST /api/send                  # Send email via API
GET  /api/queue/status          # Queue statistics
GET  /api/providers/status      # Provider health
POST /api/test                  # Test provider connectivity
GET  /                          # Dashboard UI
```

### 4. Email Provider Stack

#### Primary Provider: Resend API
- **Purpose:** High-deliverability transactional emails
- **Authentication:** API key based
- **Features:** Advanced analytics, webhook support
- **Rate Limits:** 3,000 emails/month (free tier)

#### Fallback Provider: Brevo (Sendinblue)
- **Purpose:** Backup SMTP service
- **Authentication:** Username/password SMTP
- **Features:** Traditional SMTP reliability
- **Rate Limits:** 300 emails/day (free tier)

#### Backup Provider: Gmail OAuth2
- **Purpose:** Emergency fallback
- **Authentication:** OAuth2 refresh tokens
- **Features:** Personal Gmail account integration
- **Rate Limits:** Standard Gmail sending limits

### 5. Redis Queue System

**Container:** `redis_email`
**Purpose:** Reliable email queue management

#### Features
- **Persistent Storage:** Email jobs survive service restarts
- **Retry Logic:** Exponential backoff for failed sends
- **Queue Monitoring:** Real-time queue statistics
- **Memory Efficiency:** Optimized for email workloads

#### Configuration
```yaml
# Resource Limits
CPU: 0.25 cores max, 0.05 cores reserved
Memory: 128MB max, 32MB reserved

# Security
Password Protected: Yes
External Access: Disabled
TLS: Internal network only
```

---

## 🔒 Security Implementation

### DNS Security Records

#### SPF Record
```
Type: TXT
Name: dxbmeta.com
Value: v=spf1 include:_spf.mx.cloudflare.net include:_spf.resend.com ~all
```
**Purpose:** Authorize Cloudflare and Resend to send emails for domain

#### DKIM Record (Resend)
```
Type: TXT
Name: resend._domainkey.dxbmeta.com
Value: [DKIM public key from Resend dashboard]
```
**Purpose:** Cryptographic email authentication

#### DMARC Record
```
Type: TXT
Name: _dmarc.dxbmeta.com
Value: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; sp=quarantine; adkim=r; aspf=r;
```
**Purpose:** Domain protection and reporting

### Application Security

#### Authentication Layers
1. **API Key Authentication:** All API endpoints require valid API key
2. **SMTP Authentication:** Internal SMTP requires username/password
3. **Rate Limiting:** 5 requests per minute per IP for email endpoints
4. **Input Validation:** Comprehensive email content sanitization
5. **Security Headers:** Helmet.js with CSP protection

#### Threat Mitigation
- **SQL Injection:** Parameterized queries and input validation
- **XSS Protection:** Content sanitization and escape-html
- **CSRF Protection:** API-only architecture with token validation
- **DDoS Protection:** Rate limiting and Cloudflare proxy
- **Email Spoofing:** SPF/DKIM/DMARC validation

---

## ⚙️ Configuration Details

### Environment Variables (.env)

#### Core Leantime Configuration
```bash
# Email System Integration
LEAN_EMAIL_USE_SMTP=true
LEAN_EMAIL_SMTP_HOSTS=email-service
LEAN_EMAIL_SMTP_PORT=2525
LEAN_EMAIL_SMTP_USERNAME=leantime
LEAN_EMAIL_SMTP_PASSWORD=[secure_password]
LEAN_EMAIL_RETURN=<EMAIL>
```

#### Email Service Configuration
```bash
# Service Settings
HTTP_PORT=3000
SMTP_PORT=2525
LOG_LEVEL=info
DEFAULT_FROM_EMAIL=<EMAIL>
EMAIL_API_KEY=[64-character-secure-key]

# Redis Configuration
REDIS_URL=redis://redis_email:6379
REDIS_PASSWORD=[secure_password]

# Provider API Keys
RESEND_API_KEY=[resend_api_key]
BREVO_USERNAME=[brevo_username]
BREVO_SMTP_KEY=[brevo_api_key]
```

### Docker Compose Configuration

#### Main Service Stack
```yaml
# docker-compose.email.yml
services:
  email-service:
    build: ./email-service
    restart: unless-stopped
    ports:
      - "3000:3000"  # Dashboard
    networks:
      - leantime-net
    depends_on:
      - redis_email

  redis_email:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - leantime-net
    volumes:
      - redis-email-data:/data
```

---

## 📈 Monitoring and Health Checks

### Service Health Monitoring

#### Health Check Endpoints
```bash
# Email Service Health
curl https://admin.dxbmeta.com:3000/health

# Expected Response
{
  "status": "healthy",
  "smtp": true,
  "queue": true,
  "providers": {
    "resend": "active",
    "brevo": "standby",
    "gmail": "standby"
  },
  "timestamp": "2025-09-25T10:30:00.000Z"
}
```

#### Queue Monitoring
```bash
# Queue Status
curl -H "x-api-key: [API_KEY]" https://admin.dxbmeta.com:3000/api/queue/status

# Expected Response
{
  "waiting": 0,
  "active": 1,
  "completed": 245,
  "failed": 3,
  "total": 249
}
```

### Email Deliverability Monitoring

#### Key Metrics to Track
- **Delivery Rate:** Target >95%
- **Bounce Rate:** Target <5%
- **Spam Score:** Target <2/10
- **Queue Processing Time:** Target <30 seconds
- **Provider Failover Rate:** Target <10%

#### Testing Commands
```bash
# Test email delivery
curl -X POST \
  -H "x-api-key: [API_KEY]" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "text": "This is a test email"
  }' \
  https://admin.dxbmeta.com:3000/api/send

# Test SMTP connectivity
telnet admin.dxbmeta.com 2525
```

### Log Analysis

#### Log Locations
```bash
# Email Service Logs
docker logs leantime-email-service

# Email Service File Logs
docker exec leantime-email-service cat /app/logs/email-service.log

# Redis Logs
docker logs redis_email

# Leantime Application Logs
docker logs docker-leantime-leantime-1
```

#### Key Log Patterns
```bash
# Successful email processing
grep "Email sent successfully" /app/logs/email-service.log

# Failed email attempts
grep "Failed to send email" /app/logs/email-service.log

# Provider failover events
grep "Fallback provider" /app/logs/email-service.log

# Authentication failures
grep "Invalid credentials" /app/logs/email-service.log
```

---

## 🛠️ Troubleshooting Procedures

### Common Issues and Solutions

#### 1. Emails Not Sending from Leantime

**Symptoms:**
- Leantime shows "Email sent" but recipients don't receive emails
- SMTP connection errors in Leantime logs

**Diagnosis Steps:**
```bash
# Check email service status
curl https://admin.dxbmeta.com:3000/health

# Check queue status
curl -H "x-api-key: [API_KEY]" https://admin.dxbmeta.com:3000/api/queue/status

# Check container logs
docker logs leantime-email-service
```

**Common Solutions:**
1. **Service Down:** Restart email service container
2. **Redis Connection:** Check Redis container health
3. **API Key Issues:** Verify EMAIL_API_KEY in environment
4. **Provider Issues:** Check provider API keys and quotas

#### 2. Inbound Emails Not Forwarding

**Symptoms:**
- Emails sent to @dxbmeta.com addresses are not received
- Bounce messages about undeliverable emails

**Diagnosis Steps:**
```bash
# Check DNS records
nslookup -type=MX dxbmeta.com
nslookup -type=TXT dxbmeta.com

# Test email routing
# Send <NAME_EMAIL> and monitor forwarding
```

**Common Solutions:**
1. **DNS Issues:** Verify MX records point to Cloudflare
2. **Cloudflare Configuration:** Check Email Routing settings
3. **Destination Verification:** Ensure forwarding addresses are verified
4. **SPF/DKIM Issues:** Update DNS records

#### 3. High Bounce Rate / Delivery Issues

**Symptoms:**
- Emails marked as spam
- High bounce rate in provider dashboards
- Low email deliverability scores

**Diagnosis Steps:**
```bash
# Test email authentication
# Send test email and check headers for SPF/DKIM/DMARC

# Check domain reputation
# Use tools like MXToolbox to check domain blacklists
```

**Common Solutions:**
1. **Authentication Failure:** Fix SPF/DKIM/DMARC records
2. **Content Issues:** Review email templates for spam triggers
3. **Sending Volume:** Implement gradual volume increases
4. **Domain Warming:** Establish sending reputation gradually

#### 4. Provider API Quota Exceeded

**Symptoms:**
- Sudden email sending failures
- "Quota exceeded" errors in logs
- Automatic fallback to secondary providers

**Diagnosis Steps:**
```bash
# Check provider status
curl -H "x-api-key: [API_KEY]" https://admin.dxbmeta.com:3000/api/providers/status

# Review usage in provider dashboards
# Resend: https://resend.com/dashboard
# Brevo: https://my.brevo.com/
```

**Common Solutions:**
1. **Upgrade Provider Plan:** Increase quota limits
2. **Distribute Load:** Configure multiple providers
3. **Queue Management:** Implement sending rate limits
4. **Usage Monitoring:** Set up quota alerts

### Emergency Recovery Procedures

#### Complete Email Service Failure
```bash
# 1. Stop all services
docker-compose -f docker-compose.email.yml down

# 2. Check system resources
docker system df
docker system prune

# 3. Restart with full logs
docker-compose -f docker-compose.email.yml up -d
docker logs -f leantime-email-service

# 4. Test basic functionality
curl https://admin.dxbmeta.com:3000/health
```

#### Cloudflare Email Routing Failure
```bash
# 1. Check Cloudflare status
curl https://api.cloudflare.com/client/v4/status

# 2. Verify DNS propagation
dig MX dxbmeta.com
dig TXT dxbmeta.com

# 3. Test direct SMTP if needed
# Configure Leantime to use backup SMTP directly
```

#### Database Connection Issues
```bash
# 1. Check Leantime database connectivity
docker logs mysql_leantime

# 2. Verify email service Redis
docker logs redis_email

# 3. Restart dependent services in order
docker-compose restart redis_email
docker-compose restart email-service
docker-compose restart leantime
```

---

## 🔧 Maintenance Procedures

### Regular Maintenance Tasks

#### Daily Monitoring
```bash
# Check service health (automated via monitoring)
curl https://admin.dxbmeta.com:3000/health

# Review email queue status
curl -H "x-api-key: [API_KEY]" https://admin.dxbmeta.com:3000/api/queue/status

# Check for failed emails in logs
docker logs leantime-email-service | grep -i error | tail -10
```

#### Weekly Maintenance
```bash
# Analyze email delivery statistics
# Review provider dashboards for trends
# Check log file sizes and rotate if needed
# Update Docker images to latest security patches

# Update Docker images
docker-compose pull
docker-compose up -d

# Clean up old logs
docker exec leantime-email-service find /app/logs -name "*.log*" -mtime +7 -delete
```

#### Monthly Maintenance
```bash
# Review and update security configurations
# Audit email provider usage and costs
# Test disaster recovery procedures
# Update DNS records if needed
# Review and update documentation

# Backup configurations
tar -czf email-config-backup-$(date +%Y%m%d).tar.gz \
  docker-compose*.yml \
  .env \
  email-service/ \
  cloudflare-email-worker.js
```

### Update Procedures

#### Updating Email Service
```bash
# 1. Create backup
docker-compose -f docker-compose.email.yml stop email-service
docker commit leantime-email-service email-service-backup

# 2. Update service code
# Make changes to email-service/ directory

# 3. Rebuild and deploy
docker-compose -f docker-compose.email.yml build email-service
docker-compose -f docker-compose.email.yml up -d email-service

# 4. Test functionality
curl https://admin.dxbmeta.com:3000/health
```

#### Updating Cloudflare Worker
```bash
# 1. Test worker code locally
# 2. Deploy to Cloudflare Workers
# 3. Test email routing functionality
# 4. Monitor for any routing issues
```

#### Updating DNS Records
```bash
# 1. Make changes in Cloudflare dashboard
# 2. Verify propagation
dig TXT dxbmeta.com
dig MX dxbmeta.com

# 3. Test email authentication
# Send test emails and verify headers
```

### Performance Optimization

#### Queue Optimization
```bash
# Monitor Redis memory usage
docker exec redis_email redis-cli INFO memory

# Optimize queue processing
# Adjust concurrency settings in email service
# Configure appropriate retry intervals
```

#### Provider Load Balancing
```javascript
// Configure provider weights in email service
const providerConfig = {
  resend: { weight: 70, maxRetries: 3 },
  brevo: { weight: 20, maxRetries: 2 },
  gmail: { weight: 10, maxRetries: 1 }
};
```

---

## 📋 Appendices

### A. Environment Variables Reference

#### Required Variables
```bash
# Core Service
EMAIL_API_KEY=                  # 64-char secure API key
SMTP_PASSWORD=                  # SMTP auth password
DEFAULT_FROM_EMAIL=             # Default sender address
REDIS_PASSWORD=                 # Redis authentication

# Provider APIs
RESEND_API_KEY=                # Resend API key
BREVO_USERNAME=                # Brevo account username
BREVO_SMTP_KEY=               # Brevo SMTP password
```

#### Optional Variables
```bash
# Service Configuration
HTTP_PORT=3000                 # API server port
SMTP_PORT=2525                # SMTP server port
LOG_LEVEL=info                # Logging verbosity
ALLOWED_ORIGINS=              # CORS origins

# Backup Provider
GMAIL_CLIENT_ID=              # Gmail OAuth client
GMAIL_CLIENT_SECRET=          # Gmail OAuth secret
GMAIL_REFRESH_TOKEN=          # Gmail OAuth token
GMAIL_USER=                   # Gmail account email
```

### B. Docker Configuration Reference

#### Complete Docker Compose
```yaml
version: '3.8'
services:
  email-service:
    build: ./email-service
    restart: unless-stopped
    environment:
      - HTTP_PORT=3000
      - SMTP_PORT=2525
      - EMAIL_API_KEY=${EMAIL_API_KEY}
      - REDIS_PASSWORD=${EMAIL_REDIS_PASSWORD}
    ports:
      - "3000:3000"
    networks:
      - leantime-net
    depends_on:
      - redis_email

  redis_email:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass ${EMAIL_REDIS_PASSWORD}
    networks:
      - leantime-net
    volumes:
      - redis-email-data:/data

volumes:
  redis-email-data:

networks:
  leantime-net:
    external: true
```

### C. API Reference

#### Send Email Endpoint
```bash
POST /api/send
Content-Type: application/json
x-api-key: [API_KEY]

{
  "to": "<EMAIL>",
  "subject": "Email Subject",
  "html": "<p>HTML content</p>",
  "text": "Plain text content",
  "from": "<EMAIL>"  # Optional
}
```

#### Response Format
```json
{
  "success": true,
  "jobId": "12345",
  "message": "Email queued for delivery"
}
```

### D. Security Checklist

#### Pre-Deployment Security Verification
- [ ] API keys are at least 32 characters long
- [ ] SMTP passwords are strong and unique
- [ ] Redis is password-protected
- [ ] Rate limiting is configured
- [ ] Input validation is implemented
- [ ] Security headers are enabled
- [ ] DNS records include SPF/DKIM/DMARC
- [ ] Email content is sanitized
- [ ] Logs don't contain sensitive data
- [ ] Containers run with minimal privileges

#### Ongoing Security Monitoring
- [ ] Regular security updates applied
- [ ] API access logs monitored
- [ ] Failed authentication attempts tracked
- [ ] Email authentication scores maintained
- [ ] Provider API usage monitored
- [ ] DNS records periodically verified

---

## 📞 Support and Contact Information

**System Administrator:** Harold Kanousei
**Primary Contact:** <EMAIL>
**System URL:** https://admin.dxbmeta.com
**Documentation Version:** 1.0
**Last Updated:** 2025-09-25

### Emergency Contacts
- **Production Issues:** Immediate <NAME_EMAIL>
- **Security Issues:** <NAME_EMAIL>
- **Provider Support:** Check respective provider documentation

### Related Documentation
- [Leantime Installation Guide](./LEANTIME_SETUP.md)
- [Docker Deployment Guide](./DOCKER_DEPLOYMENT.md)
- [Security Configuration](./SECURITY_CONFIG.md)
- [Monitoring Setup](./MONITORING_SETUP.md)

---

**End of Document**