2025-09-19 12:43:20 [info     ] Starting Network Configuration Agent execution
2025-09-19 12:43:20 [info     ] Setting up Python environment for bridge server
2025-09-19 12:43:35 [info     ] Installed dependency           dependency='fastapi>=0.104.0'
2025-09-19 12:43:41 [info     ] Installed dependency           dependency='uvicorn[standard]>=0.24.0'
2025-09-19 12:43:44 [info     ] Installed dependency           dependency='mysql-connector-python>=8.2.0'
2025-09-19 12:43:49 [info     ] Installed dependency           dependency='aiohttp>=3.9.0'
2025-09-19 12:43:51 [info     ] Installed dependency           dependency='structlog>=23.2.0'
2025-09-19 12:43:55 [info     ] Installed dependency           dependency='cryptography>=41.0.0'
2025-09-19 12:43:56 [info     ] Installed dependency           dependency='pydantic>=2.5.0'
2025-09-19 12:43:58 [info     ] Installed dependency           dependency='pydantic-settings>=2.1.0'
2025-09-19 12:44:00 [info     ] Installed dependency           dependency='pytz>=2023.3'
2025-09-19 12:44:03 [info     ] Installed dependency           dependency='slowapi>=0.1.9'
2025-09-19 12:44:04 [info     ] Installed dependency           dependency='python-multipart>=0.0.6'
2025-09-19 12:44:04 [info     ] Creating deployment scripts
2025-09-19 12:44:04 [info     ] Deployment scripts created successfully
2025-09-19 12:44:04 [info     ] Configuring environment variables
2025-09-19 12:44:04 [info     ] Environment variables configured
2025-09-19 12:44:04 [info     ] Testing database connections
Python-dotenv could not parse statement starting at line 246
2025-09-19 12:44:13 [info     ] Database connection test completed results={'odoo': {'status': 'failed', 'error': '[WinError 10061] No connection could be made because the target machine actively refused it'}, 'leantime': {'status': 'failed', 'error': "2003: Can't connect to MySQL server on 'mysql_leantime:3306' (Errno 11001: getaddrinfo failed)"}}
2025-09-19 12:44:13 [info     ] Deploying bridge server
2025-09-19 12:44:13 [error    ] Bridge server syntax validation failed error='  File "C:\\Users\\<USER>\\projects\\docker-leantime\\odoo-bridge\\odoo_bridge_server.py", line 793\n    sync_result = await sync_manager.sync_users(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSyntaxError: \'await\' outside async function\n'
2025-09-19 12:44:13 [error    ] Error deploying bridge server  error='Syntax validation failed:   File "C:\\Users\\<USER>\\projects\\docker-leantime\\odoo-bridge\\odoo_bridge_server.py", line 793\n    sync_result = await sync_manager.sync_users(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSyntaxError: \'await\' outside async function\n'
2025-09-19 12:44:13 [error    ] Network Configuration Agent failed error='Syntax validation failed:   File "C:\\Users\\<USER>\\projects\\docker-leantime\\odoo-bridge\\odoo_bridge_server.py", line 793\n    sync_result = await sync_manager.sync_users(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSyntaxError: \'await\' outside async function\n'
{
  "status": "failed",
  "agent": "NetworkConfig",
  "error": "Syntax validation failed:   File \"C:\\Users\\<USER>\\projects\\docker-leantime\\odoo-bridge\\odoo_bridge_server.py\", line 793\n    sync_result = await sync_manager.sync_users(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nSyntaxError: 'await' outside async function\n"
}
