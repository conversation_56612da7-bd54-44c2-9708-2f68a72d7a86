[global]
error_log = /proc/self/fd/2
daemonize = no

[www]
user = www-data
group = www-data

listen = 127.0.0.1:9000
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

clear_env = no

catch_workers_output = yes
decorate_workers_output = no

chdir = /var/www/html
php_admin_value[error_log] = /proc/self/fd/2
php_admin_flag[log_errors] = on
