#!/usr/bin/env python3
"""
Support Ticket System Module for Leantime MCP Server
Handles customer support tickets with AI-powered triage, auto-assignment, and sentiment analysis
Configured for Arab Standard Time (Dubai, UTC+4)
"""

import os
import json
import uuid
import asyncio
import aiohttp
import email
import imaplib
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import mysql.connector
import pytz
import re

class SupportTicketSystem:
    """Comprehensive support ticket management with AI-powered features"""
    
    def __init__(self, db_pool, openrouter_key: str):
        self.db_pool = db_pool
        self.openrouter_key = openrouter_key
        self.openrouter_base = "https://openrouter.ai/api/v1"
        
        # Dubai timezone
        self.timezone = pytz.timezone('Asia/Dubai')
        
        # SLA configurations for Dubai business hours
        self.sla_config = {
            'critical': {'first_response_hours': 1, 'resolution_hours': 4},
            'high': {'first_response_hours': 2, 'resolution_hours': 8},
            'medium': {'first_response_hours': 8, 'resolution_hours': 24},
            'low': {'first_response_hours': 24, 'resolution_hours': 72}
        }
        
        # Business hours (Dubai time)
        self.business_hours = {
            'start': 8,  # 8 AM
            'end': 18,   # 6 PM
            'days': [0, 1, 2, 3, 4]  # Monday to Friday
        }
        
        # Common ticket categories and keywords
        self.category_keywords = {
            'technical_issue': ['error', 'bug', 'crash', 'not working', 'broken', 'failed'],
            'feature_request': ['feature', 'enhancement', 'improvement', 'add', 'new'],
            'account_access': ['login', 'password', 'access', 'account', 'authentication'],
            'billing_inquiry': ['billing', 'payment', 'invoice', 'charge', 'subscription'],
            'integration': ['api', 'integration', 'webhook', 'sync', 'connect'],
            'performance': ['slow', 'performance', 'speed', 'timeout', 'loading'],
            'data_migration': ['import', 'export', 'migration', 'transfer', 'data'],
            'training_support': ['training', 'how to', 'tutorial', 'guide', 'help'],
            'mobile_app': ['mobile', 'app', 'ios', 'android', 'phone'],
            'security': ['security', 'breach', 'hack', 'vulnerability', 'privacy']
        }
        
        # Team expertise mapping for auto-assignment
        self.team_expertise = {
            'technical': ['technical_issue', 'performance', 'integration', 'security'],
            'product': ['feature_request', 'mobile_app', 'data_migration'],
            'support': ['account_access', 'training_support', 'billing_inquiry'],
            'sales': ['billing_inquiry', 'account_access']
        }
    
    def get_connection(self):
        """Get database connection from pool"""
        return self.db_pool.get_connection()
    
    def get_dubai_time(self) -> datetime:
        """Get current time in Dubai timezone"""
        return datetime.now(self.timezone)
    
    def is_business_hours(self, dt: datetime = None) -> bool:
        """Check if given time is within business hours"""
        if dt is None:
            dt = self.get_dubai_time()
        
        # Convert to Dubai timezone if needed
        if dt.tzinfo != self.timezone:
            dt = dt.astimezone(self.timezone)
        
        # Check day of week (0=Monday, 6=Sunday)
        if dt.weekday() not in self.business_hours['days']:
            return False
        
        # Check hour
        hour = dt.hour
        return self.business_hours['start'] <= hour < self.business_hours['end']
    
    def calculate_sla_due_time(self, priority: str, created_at: datetime) -> datetime:
        """Calculate SLA due time considering business hours"""
        
        config = self.sla_config.get(priority, self.sla_config['medium'])
        hours_to_add = config['first_response_hours']
        
        # Start from creation time
        current_time = created_at.astimezone(self.timezone)
        hours_added = 0
        
        while hours_added < hours_to_add:
            current_time += timedelta(hours=1)
            
            # Only count business hours
            if self.is_business_hours(current_time):
                hours_added += 1
        
        return current_time
    
    async def analyze_ticket_ai(self, subject: str, description: str, sender_email: str = "") -> Dict[str, Any]:
        """AI analysis of ticket for categorization, priority, and sentiment"""
        
        prompt = f"""
        Analyze this customer support ticket for a software company in Dubai:
        
        Subject: {subject}
        Description: {description}
        Sender: {sender_email}
        
        Please analyze and categorize:
        
        Available categories:
        - technical_issue: Bugs, errors, system problems
        - feature_request: New features, enhancements
        - account_access: Login, password, authentication issues
        - billing_inquiry: Payment, subscription, billing questions
        - integration: API, webhooks, third-party integrations
        - performance: Speed, loading, timeout issues
        - data_migration: Import, export, data transfer
        - training_support: How-to questions, tutorials
        - mobile_app: Mobile application issues
        - security: Security concerns, vulnerabilities
        
        Priority levels:
        - critical: System down, data loss, security breach
        - high: Major functionality affected, many users impacted
        - medium: Single user affected, workaround available
        - low: Minor issues, enhancement requests
        
        Respond in JSON format:
        {{
            "category": "most_likely_category",
            "priority": "priority_level",
            "severity": "minor|moderate|major|critical",
            "sentiment": "positive|neutral|negative|angry",
            "urgency_score": 0.75,
            "confidence": 0.85,
            "key_issues": ["main issue 1", "main issue 2"],
            "suggested_assignee_type": "technical|product|support|sales",
            "estimated_effort": "quick|medium|complex",
            "customer_type": "new|existing|enterprise",
            "language_detected": "english|arabic|other",
            "escalation_needed": false,
            "auto_response_suggested": "Quick response suggestion if applicable"
        }}
        """
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://leantime.io",
                    "X-Title": "Leantime Support System"
                }
                
                payload = {
                    "model": "mistralai/mistral-7b-instruct:free",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1024,
                    "temperature": 0.3
                }
                
                async with session.post(
                    f"{self.openrouter_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        try:
                            if "```json" in content:
                                content = content.split("```json")[1].split("```")[0]
                            elif "```" in content:
                                content = content.split("```")[1].split("```")[0]
                            
                            ai_analysis = json.loads(content.strip())
                            return ai_analysis
                        except json.JSONDecodeError:
                            return self._simple_categorize_ticket(subject, description)
                    else:
                        return self._simple_categorize_ticket(subject, description)
        except Exception as e:
            print(f"AI ticket analysis failed: {e}")
            return self._simple_categorize_ticket(subject, description)
    
    def _simple_categorize_ticket(self, subject: str, description: str) -> Dict[str, Any]:
        """Fallback categorization using keyword matching"""
        
        text = f"{subject} {description}".lower()
        
        # Find category
        category = 'technical_issue'  # default
        confidence = 0.3
        
        for cat, keywords in self.category_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in text)
            if matches > 0:
                category = cat
                confidence = min(0.8, 0.3 + (matches * 0.1))
                break
        
        # Determine priority based on keywords
        urgent_keywords = ['urgent', 'critical', 'down', 'emergency', 'asap', 'immediately']
        high_keywords = ['important', 'needed soon', 'affecting users', 'broken']
        
        if any(keyword in text for keyword in urgent_keywords):
            priority = 'critical'
        elif any(keyword in text for keyword in high_keywords):
            priority = 'high'
        elif 'feature' in text or 'enhancement' in text:
            priority = 'low'
        else:
            priority = 'medium'
        
        return {
            "category": category,
            "priority": priority,
            "severity": "moderate",
            "sentiment": "neutral",
            "urgency_score": 0.5,
            "confidence": confidence,
            "key_issues": [category.replace('_', ' ')],
            "suggested_assignee_type": "support",
            "estimated_effort": "medium",
            "customer_type": "existing",
            "language_detected": "english",
            "escalation_needed": False,
            "auto_response_suggested": None
        }
    
    async def create_ticket(self, ticket_data: Dict[str, Any]) -> str:
        """Create a new support ticket"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Generate unique ticket ID
            ticket_id = f"TKT-{uuid.uuid4().hex[:8].upper()}"
            
            dubai_now = self.get_dubai_time()
            
            # AI analysis if not provided
            if not ticket_data.get('category'):
                ai_analysis = await self.analyze_ticket_ai(
                    ticket_data['subject'],
                    ticket_data['description'],
                    ticket_data.get('reporter_email', '')
                )
                
                ticket_data.update({
                    'category': ai_analysis['category'],
                    'priority': ai_analysis['priority'],
                    'severity': ai_analysis['severity'],
                    'ai_sentiment': ai_analysis['sentiment'],
                    'ai_urgency_score': ai_analysis['urgency_score'],
                    'ai_category_confidence': ai_analysis['confidence']
                })
            
            # Auto-assign if enabled
            suggested_assignee = await self._suggest_assignee(ticket_data)
            if suggested_assignee:
                ticket_data['assignee_id'] = suggested_assignee
            
            # Calculate SLA due time
            sla_due_at = self.calculate_sla_due_time(ticket_data['priority'], dubai_now)
            
            # Insert ticket
            query = """
                INSERT INTO mcp_support_tickets (
                    ticket_id, customer_id, project_id,
                    subject, description, priority, severity, category, subcategory,
                    status, assignee_id, reporter_email, reporter_name,
                    sla_due_at, ai_sentiment, ai_urgency_score, ai_category_confidence,
                    source, tags, external_ref
                ) VALUES (
                    %s, %s, %s,
                    %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s
                )
            """
            
            values = (
                ticket_id,
                ticket_data.get('customer_id'),
                ticket_data.get('project_id'),
                ticket_data['subject'],
                ticket_data['description'],
                ticket_data.get('priority', 'medium'),
                ticket_data.get('severity', 'moderate'),
                ticket_data.get('category', 'technical_issue'),
                ticket_data.get('subcategory'),
                'open',
                ticket_data.get('assignee_id'),
                ticket_data['reporter_email'],
                ticket_data['reporter_name'],
                sla_due_at,
                ticket_data.get('ai_sentiment'),
                ticket_data.get('ai_urgency_score'),
                ticket_data.get('ai_category_confidence'),
                ticket_data.get('source', 'email'),
                json.dumps(ticket_data.get('tags', [])),
                ticket_data.get('external_ref')
            )
            
            cursor.execute(query, values)
            conn.commit()
            
            # Create initial message thread
            await self._create_ticket_message(
                ticket_id,
                ticket_data['reporter_email'],
                ticket_data['reporter_name'],
                ticket_data['description'],
                'customer'
            )
            
            # Send auto-response if suggested
            ai_response = ticket_data.get('auto_response_suggested')
            if ai_response:
                await self._send_auto_response(ticket_id, ai_response)
            
            return ticket_id
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to create ticket: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def _suggest_assignee(self, ticket_data: Dict[str, Any]) -> Optional[int]:
        """Suggest best assignee based on category and team expertise"""
        
        category = ticket_data.get('category')
        if not category:
            return None
        
        # Find team type for this category
        assignee_type = None
        for team_type, categories in self.team_expertise.items():
            if category in categories:
                assignee_type = team_type
                break
        
        if not assignee_type:
            return None
        
        # Find available team member (simplified - in production, check workload)
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Look for users with matching role/department
            query = """
                SELECT u.id, u.firstname, u.lastname,
                       COUNT(t.id) as current_tickets
                FROM zp_users u
                LEFT JOIN mcp_support_tickets t ON u.id = t.assignee_id 
                                                AND t.status IN ('open', 'in_progress')
                WHERE u.status = 'active'
                  AND (u.role LIKE %s OR u.department LIKE %s)
                GROUP BY u.id
                ORDER BY current_tickets ASC
                LIMIT 1
            """
            
            cursor.execute(query, (f'%{assignee_type}%', f'%{assignee_type}%'))
            assignee = cursor.fetchone()
            
            return assignee['id'] if assignee else None
            
        finally:
            cursor.close()
            conn.close()
    
    async def _create_ticket_message(self, ticket_id: str, sender_email: str, 
                                   sender_name: str, message_body: str, message_type: str):
        """Create a message in the ticket thread"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            message_id = f"MSG-{uuid.uuid4().hex[:8].upper()}"
            dubai_now = self.get_dubai_time()
            
            query = """
                INSERT INTO mcp_ticket_messages (
                    ticket_id, message_id, sender_email, sender_name,
                    message_body, message_type, sent_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(query, (
                ticket_id, message_id, sender_email, sender_name,
                message_body, message_type, dubai_now
            ))
            
            # Update ticket timestamps
            if message_type == 'customer':
                update_query = "UPDATE mcp_support_tickets SET last_customer_reply = %s, reply_count = reply_count + 1 WHERE ticket_id = %s"
            else:
                update_query = "UPDATE mcp_support_tickets SET last_agent_reply = %s, reply_count = reply_count + 1 WHERE ticket_id = %s"
            
            cursor.execute(update_query, (dubai_now, ticket_id))
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to create message: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def _send_auto_response(self, ticket_id: str, response_text: str):
        """Send automated response to ticket"""
        
        # Get ticket details
        ticket = await self.get_ticket(ticket_id)
        if not ticket:
            return
        
        # Create auto-response message
        await self._create_ticket_message(
            ticket_id,
            "<EMAIL>",  # Configure this
            "Support Bot",
            f"Thank you for contacting support. {response_text}\n\nA team member will review your request and respond soon.",
            'auto'
        )
        
        # Update first response time
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            query = "UPDATE mcp_support_tickets SET first_response_at = %s WHERE ticket_id = %s AND first_response_at IS NULL"
            cursor.execute(query, (self.get_dubai_time(), ticket_id))
            conn.commit()
        finally:
            cursor.close()
            conn.close()
    
    async def get_ticket(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get ticket details"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            query = """
                SELECT 
                    t.*,
                    c.company_name as customer_name,
                    p.name as project_name,
                    u.firstname as assignee_firstname,
                    u.lastname as assignee_lastname
                FROM mcp_support_tickets t
                LEFT JOIN mcp_customers c ON t.customer_id = c.id
                LEFT JOIN zp_projects p ON t.project_id = p.id
                LEFT JOIN zp_users u ON t.assignee_id = u.id
                WHERE t.ticket_id = %s
            """
            
            cursor.execute(query, (ticket_id,))
            ticket = cursor.fetchone()
            
            if ticket:
                # Convert decimals and parse JSON
                if ticket['ai_urgency_score']:
                    ticket['ai_urgency_score'] = float(ticket['ai_urgency_score'])
                if ticket['ai_category_confidence']:
                    ticket['ai_category_confidence'] = float(ticket['ai_category_confidence'])
                if ticket['tags']:
                    try:
                        ticket['tags'] = json.loads(ticket['tags'])
                    except:
                        ticket['tags'] = []
                
                # Check SLA status
                dubai_now = self.get_dubai_time()
                if ticket['sla_due_at'] and dubai_now > ticket['sla_due_at'].replace(tzinfo=self.timezone):
                    ticket['sla_breached'] = True
            
            return ticket
            
        finally:
            cursor.close()
            conn.close()
    
    async def get_tickets(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Get tickets with optional filters"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            query = """
                SELECT 
                    t.*,
                    c.company_name as customer_name,
                    u.firstname as assignee_firstname,
                    u.lastname as assignee_lastname
                FROM mcp_support_tickets t
                LEFT JOIN mcp_customers c ON t.customer_id = c.id
                LEFT JOIN zp_users u ON t.assignee_id = u.id
                WHERE 1=1
            """
            
            params = []
            
            if filters:
                if filters.get('status'):
                    query += " AND t.status = %s"
                    params.append(filters['status'])
                
                if filters.get('priority'):
                    query += " AND t.priority = %s"
                    params.append(filters['priority'])
                
                if filters.get('category'):
                    query += " AND t.category = %s"
                    params.append(filters['category'])
                
                if filters.get('assignee_id'):
                    query += " AND t.assignee_id = %s"
                    params.append(filters['assignee_id'])
                
                if filters.get('sla_breached'):
                    dubai_now = self.get_dubai_time()
                    query += " AND t.sla_due_at < %s AND t.status IN ('open', 'in_progress')"
                    params.append(dubai_now)
            
            query += " ORDER BY t.priority DESC, t.created_at ASC"
            
            if filters and filters.get('limit'):
                query += f" LIMIT {int(filters['limit'])}"
            
            cursor.execute(query, params)
            tickets = cursor.fetchall()
            
            # Process results
            for ticket in tickets:
                if ticket['ai_urgency_score']:
                    ticket['ai_urgency_score'] = float(ticket['ai_urgency_score'])
                if ticket['ai_category_confidence']:
                    ticket['ai_category_confidence'] = float(ticket['ai_category_confidence'])
                if ticket['tags']:
                    try:
                        ticket['tags'] = json.loads(ticket['tags'])
                    except:
                        ticket['tags'] = []
            
            return tickets
            
        finally:
            cursor.close()
            conn.close()
    
    async def update_ticket_status(self, ticket_id: str, new_status: str, user_id: int, notes: str = "") -> bool:
        """Update ticket status"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            dubai_now = self.get_dubai_time()
            
            # Prepare update fields based on status
            update_fields = ["status = %s"]
            values = [new_status]
            
            if new_status == 'resolved':
                update_fields.append("resolved_at = %s")
                values.append(dubai_now)
            elif new_status == 'closed':
                update_fields.append("closed_at = %s")
                values.append(dubai_now)
            
            query = f"""
                UPDATE mcp_support_tickets 
                SET {', '.join(update_fields)}
                WHERE ticket_id = %s
            """
            values.append(ticket_id)
            
            cursor.execute(query, values)
            
            if cursor.rowcount > 0:
                # Add status change message
                if notes:
                    await self._create_ticket_message(
                        ticket_id,
                        "<EMAIL>",
                        "System",
                        f"Status changed to {new_status}. {notes}",
                        'system'
                    )
                
                conn.commit()
                return True
            else:
                return False
                
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to update ticket status: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def generate_support_metrics(self, timeframe: str = "month") -> Dict[str, Any]:
        """Generate support performance metrics"""
        
        dubai_now = self.get_dubai_time()
        
        if timeframe == "week":
            start_date = dubai_now - timedelta(days=7)
        elif timeframe == "month":
            start_date = dubai_now - timedelta(days=30)
        else:  # quarter
            start_date = dubai_now - timedelta(days=90)
        
        filters = {"created_after": start_date}
        tickets = await self.get_tickets(filters)
        
        if not tickets:
            return {"message": "No tickets found for the specified timeframe"}
        
        # Calculate metrics
        total_tickets = len(tickets)
        resolved_tickets = len([t for t in tickets if t['status'] == 'resolved'])
        sla_breached = len([t for t in tickets if t.get('sla_breached')])
        
        # Response times (simplified calculation)
        response_times = []
        resolution_times = []
        
        for ticket in tickets:
            if ticket['first_response_at'] and ticket['created_at']:
                response_delta = ticket['first_response_at'] - ticket['created_at']
                response_times.append(response_delta.total_seconds() / 3600)  # hours
            
            if ticket['resolved_at'] and ticket['created_at']:
                resolution_delta = ticket['resolved_at'] - ticket['created_at']
                resolution_times.append(resolution_delta.total_seconds() / 3600)  # hours
        
        metrics = {
            "timeframe": timeframe,
            "period": f"{start_date.date()} to {dubai_now.date()}",
            "total_tickets": total_tickets,
            "resolved_tickets": resolved_tickets,
            "resolution_rate": (resolved_tickets / total_tickets * 100) if total_tickets > 0 else 0,
            "sla_breached": sla_breached,
            "sla_compliance": ((total_tickets - sla_breached) / total_tickets * 100) if total_tickets > 0 else 100,
            "avg_first_response_hours": sum(response_times) / len(response_times) if response_times else 0,
            "avg_resolution_hours": sum(resolution_times) / len(resolution_times) if resolution_times else 0,
            "priority_breakdown": {},
            "category_breakdown": {},
            "agent_performance": {},
            "timezone": "Asia/Dubai",
            "currency": "N/A"
        }
        
        # Breakdowns
        for ticket in tickets:
            # Priority breakdown
            priority = ticket['priority']
            metrics['priority_breakdown'][priority] = metrics['priority_breakdown'].get(priority, 0) + 1
            
            # Category breakdown
            category = ticket['category']
            metrics['category_breakdown'][category] = metrics['category_breakdown'].get(category, 0) + 1
        
        return metrics