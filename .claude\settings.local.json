{"permissions": {"allow": ["<PERSON><PERSON>(openssl rand:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(chmod:*)", "WebSearch", "Bash(python test_odoo_integration.py)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker build:*)", "Bash(cmd.exe /c package-for-transfer.bat)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "<PERSON><PERSON>(docker:*)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "<PERSON><PERSON>(curl:*)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime\\odoo-bridge/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime\\odoo-bridge/**)", "Read(/C:\\Users\\<USER>\\projects\\docker-leantime/**)", "Bash(tree:*)", "<PERSON><PERSON>(podman:*)", "<PERSON><PERSON>(wsl podman:*)", "Bash(cmd.exe:*)", "Bash(powershell.exe:*)", "Bash(ssh:*)", "<PERSON><PERSON>(scp:*)", "WebFetch(domain:admin.dxbmeta.com)", "WebFetch(domain:docs.leantime.io)", "WebFetch(domain:support.leantime.io)", "WebFetch(domain:leantime.io)", "<PERSON><PERSON>(python test:*)", "SlashCommand(/claude-flow-help)"], "deny": [], "ask": []}}