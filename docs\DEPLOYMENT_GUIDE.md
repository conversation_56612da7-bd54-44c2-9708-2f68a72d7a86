# Complete Odoo Integration Deployment Guide

## 📋 Pre-Deployment Checklist

### 1. **Validate Everything First**
```bash
# Run the validation script
chmod +x docker-check-and-validate.sh
./docker-check-and-validate.sh
```

This will check:
- ✅ Docker installation
- ✅ Required files
- ✅ Environment configuration
- ✅ Docker images
- ✅ Port availability
- ✅ Network configuration
- ✅ Disk space
- ✅ Docker Compose syntax
- ✅ Dubai/UAE settings

### 2. **Environment Setup**
```bash
# Copy and configure environment
cp .env.template .env
nano .env

# Required settings to change:
ODOO_ADMIN_PASSWORD=your_secure_password
ODOO_DB_PASSWORD=your_db_password
BRIDGE_API_KEY=your_api_key
LEAN_DB_PASSWORD=your_leantime_password
```

## 🐳 Docker Build Process

### Option 1: Build Everything Locally
```bash
# Build the bridge image
docker build -t odoo-bridge:latest ./odoo-bridge

# Pull other images
docker pull odoo:17.0
docker pull postgres:15-alpine
docker pull redis:7-alpine

# Verify images
docker images | grep -E "(odoo|postgres|redis)"
```

### Option 2: Use Docker Compose to Build
```bash
# Build and pull all at once
docker-compose -f docker-compose.odoo.yml build
docker-compose -f docker-compose.odoo.yml pull
```

## 🚀 Deployment Steps

### 1. **Start Services**
```bash
# Start in background
docker-compose -f docker-compose.odoo.yml up -d

# Or start with logs visible (for debugging)
docker-compose -f docker-compose.odoo.yml up
```

### 2. **Monitor Startup**
```bash
# Watch logs
docker-compose -f docker-compose.odoo.yml logs -f

# Check specific service
docker logs odoo-business -f
docker logs odoo-bridge -f
```

### 3. **Verify Services**
```bash
# Check all services are running
docker-compose -f docker-compose.odoo.yml ps

# Should show:
# odoo-business       ... Up     0.0.0.0:8069->8069/tcp
# postgres_odoo       ... Up     5432/tcp
# redis_odoo          ... Up     6379/tcp  
# odoo-bridge         ... Up     0.0.0.0:8070->8070/tcp
```

## 🧪 Testing

### 1. **Basic Health Checks**
```bash
# Check bridge health
curl http://localhost:8070/health

# Check Odoo
curl -I http://localhost:8069

# Test bridge API
curl -H "Authorization: Bearer your_api_key" http://localhost:8070/admin/status
```

### 2. **Run Integration Tests**
```bash
# Full test suite
python test_odoo_integration.py

# Should test:
# ✓ Bridge health
# ✓ Odoo connection
# ✓ Leantime connection
# ✓ Data sync
# ✓ AI insights
# ✓ Dubai timezone
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. **Bridge Can't Connect to Odoo**
```bash
# Check Odoo is ready
docker exec odoo-business curl -I http://localhost:8069

# Check network
docker network inspect docker-leantime_odoo-local-net

# Restart bridge
docker-compose -f docker-compose.odoo.yml restart odoo-bridge
```

#### 2. **Port Already in Use**
```bash
# Find what's using the port
netstat -tulpn | grep 8069
lsof -i :8069

# Change port in docker-compose.odoo.yml
ports:
  - "8169:8069"  # Use different external port
```

#### 3. **Database Connection Issues**
```bash
# Check PostgreSQL
docker exec postgres_odoo pg_isready

# Check credentials
docker exec postgres_odoo psql -U odoo -d odoo_business -c "SELECT 1"

# Reset database if needed
docker-compose -f docker-compose.odoo.yml down -v
docker-compose -f docker-compose.odoo.yml up -d
```

#### 4. **Memory/Performance Issues**
```bash
# Check resource usage
docker stats

# Limit resources in docker-compose
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '2'
```

## 📊 Post-Deployment Configuration

### 1. **Initialize Odoo Modules**
```bash
# Access Odoo container
docker exec -it odoo-business bash

# Install required modules via Odoo CLI
odoo -d odoo_business -i project,sale,account,hr_timesheet --stop-after-init
```

### 2. **Configure MCP Client**
```json
// Add to Claude Desktop config
{
  "mcpServers": {
    "odoo-bridge": {
      "command": "python",
      "args": ["path/to/mcp_tools.py"],
      "env": {
        "BRIDGE_URL": "http://localhost:8070",
        "BRIDGE_API_KEY": "your_api_key"
      }
    }
  }
}
```

### 3. **Set Up Dubai Business Settings**
Access Odoo at http://localhost:8069
1. Login as admin
2. Go to Settings → General Settings
3. Set:
   - Timezone: Asia/Dubai
   - Currency: AED
   - Fiscal Position: UAE VAT 5%

## 🔐 Security Hardening

### 1. **Change Default Passwords**
```bash
# Odoo admin password (via UI)
# PostgreSQL password (in .env)
# Bridge API key (in .env)
```

### 2. **Network Security**
```bash
# Create internal network if not exposing
docker network create --internal odoo-internal

# Use reverse proxy for external access
# Add nginx/traefik configuration
```

### 3. **Backup Strategy**
```bash
# Backup script
#!/bin/bash
DATE=$(date +%Y%m%d-%H%M%S)

# Backup database
docker exec postgres_odoo pg_dump -U odoo odoo_business > backup-$DATE.sql

# Backup volumes
docker run --rm -v odoo-data:/data -v $(pwd):/backup alpine tar czf /backup/odoo-data-$DATE.tar.gz /data
```

## 📈 Monitoring

### 1. **Service Monitoring**
```bash
# Create monitoring script
#!/bin/bash
while true; do
  curl -f http://localhost:8070/health || echo "Bridge down!"
  curl -f http://localhost:8069/web/health || echo "Odoo down!"
  sleep 60
done
```

### 2. **Log Monitoring**
```bash
# Aggregate logs
docker-compose -f docker-compose.odoo.yml logs -f > odoo-all.log 2>&1 &

# Watch for errors
tail -f odoo-all.log | grep -E "(ERROR|CRITICAL|WARNING)"
```

## ✅ Success Criteria

Your deployment is successful when:

1. **All services are running**
   ```bash
   docker-compose -f docker-compose.odoo.yml ps
   # All show "Up" status
   ```

2. **Health checks pass**
   ```bash
   curl http://localhost:8070/health
   # Returns {"status": "healthy"}
   ```

3. **Odoo is accessible**
   - Browse to http://localhost:8069
   - Can login as admin

4. **Bridge API works**
   ```bash
   curl -H "Authorization: Bearer your_api_key" \
        http://localhost:8070/mcp/projects
   # Returns project data
   ```

5. **Integration tests pass**
   ```bash
   python test_odoo_integration.py
   # Shows 80%+ success rate
   ```

## 🎯 What You Can Do After Deployment

### Via Claude Desktop:
- "Show me all Odoo projects"
- "Sync customers to Leantime"
- "Generate business insights"
- "Create invoice from timesheets"

### Via Odoo UI:
- Manage projects and tasks
- Track timesheets
- Generate invoices
- View client portal

### Via API:
- GET /health - System health
- GET /mcp/projects - Project data
- POST /mcp/sync - Sync data
- GET /mcp/business-insights - AI insights

## 📞 Support

If you encounter issues:

1. **Check logs first**
   ```bash
   docker-compose -f docker-compose.odoo.yml logs --tail=50
   ```

2. **Run validation script**
   ```bash
   ./docker-check-and-validate.sh
   ```

3. **Test individual components**
   ```bash
   docker exec odoo-bridge python -c "import odoo_bridge_server; print('Bridge OK')"
   docker exec odoo-business odoo --version
   ```

Your Dubai Software Development Business Management System is ready! 🚀