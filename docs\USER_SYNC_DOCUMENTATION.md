# Unified User Account Management - Odoo & Leantime Integration

## Overview

This documentation describes the implementation of unified user account management between Odoo and Leantime, allowing users to access both systems with the same email and password credentials.

## Architecture

```
┌─────────────────┐         ┌──────────────────┐         ┌─────────────────┐
│                 │         │                  │         │                 │
│     Odoo        │────────▶│  Bridge Server   │────────▶│    Leantime     │
│   (Primary)     │         │   Port 8070      │         │                 │
│                 │         │                  │         │                 │
└─────────────────┘         └──────────────────┘         └─────────────────┘
     Users DB                   Sync Engine                  Users DB
```

## Features

### 1. User Synchronization
- **Automatic sync** from Odoo to Leantime
- **Email-based matching** for existing users
- **Role mapping** from Odoo groups to Leantime roles
- **Profile synchronization** (name, phone, job title, department)
- **Active/Inactive status** management

### 2. Role Mapping

| Odoo Group | Leantime Role | Permissions |
|------------|---------------|-------------|
| base.group_system | Owner (50) | Full system access |
| base.group_erp_manager | Admin (40) | Admin access except company settings |
| project.group_project_manager | Company Manager (30) | Manage projects, no user management |
| project.group_project_user | Editor (20) | Create/edit tasks and projects |
| base.group_portal | Commenter (10) | View and comment only (clients) |
| base.group_public | Read-Only (5) | View only access |

### 3. Password Management
- **Temporary password** generated for new users
- **Password reset email** sent to users (requires email configuration)
- **Secure hashing** using bcrypt
- **Option for API-based authentication** (future enhancement)

## API Endpoints

### Get Users
```http
GET /mcp/users?source=both
Authorization: Bearer bridge_secure_key
```

Returns users from Odoo, Leantime, or both systems with matching analysis.

### Sync Users
```http
POST /mcp/sync
Authorization: Bearer bridge_secure_key
Content-Type: application/json

{
  "source": "odoo",
  "target": "leantime",
  "entity_type": "users",
  "entity_ids": [1, 2, 3]  // Optional: specific user IDs
}
```

### Webhook for Automatic Sync
```http
POST /webhooks/odoo/user-change
Content-Type: application/json

{
  "user_id": 123,
  "action": "create"  // or "update", "delete"
}
```

## Setup Instructions

### 1. Environment Configuration

Add to your `.env` file:
```bash
# Bridge Configuration
BRIDGE_API_KEY=your_secure_api_key
BRIDGE_PORT=8070

# Odoo Configuration
ODOO_URL=http://odoo:8069
ODOO_DB=odoo_business
ODOO_ADMIN_USERNAME=admin
ODOO_ADMIN_PASSWORD=admin_password

# Leantime Database
LEANTIME_DB_HOST=mysql_leantime
LEANTIME_DB_USER=lean
LEANTIME_DB_PASSWORD=leantime_password
LEANTIME_DB_DATABASE=leantime
```

### 2. Deploy the Bridge

```bash
# Start all services including the bridge
docker-compose -f docker-compose.yml -f docker-compose.odoo.yml up -d

# Check bridge health
curl http://localhost:8070/health

# Check admin status
curl -H "Authorization: Bearer bridge_secure_key" \
     http://localhost:8070/admin/status
```

### 3. Initial User Sync

```bash
# Sync all users from Odoo to Leantime
curl -X POST -H "Authorization: Bearer bridge_secure_key" \
     -H "Content-Type: application/json" \
     -d '{"source":"odoo","target":"leantime","entity_type":"users"}' \
     http://localhost:8070/mcp/sync

# Check sync results
curl -H "Authorization: Bearer bridge_secure_key" \
     http://localhost:8070/mcp/users?source=both
```

### 4. Configure Odoo Webhook (Optional)

In Odoo, create an automated action for user changes:
1. Go to Settings → Technical → Automation → Automated Actions
2. Create new action:
   - Model: res.users
   - Trigger: On Creation & Update & Deletion
   - Action: Execute Python Code
   ```python
   import requests
   webhook_url = "http://odoo-bridge:8070/webhooks/odoo/user-change"
   payload = {
       "user_id": record.id,
       "action": "create" if record.create_date == record.write_date else "update"
   }
   requests.post(webhook_url, json=payload)
   ```

## User Workflow

### For Administrators

1. **Create User in Odoo**:
   - Go to Odoo → Users & Companies → Users
   - Create new user with email and role
   - Set initial password

2. **Automatic Sync to Leantime**:
   - Bridge detects new user (via webhook or manual sync)
   - Creates corresponding Leantime user
   - Maps role based on Odoo groups
   - Generates temporary password

3. **User Notification**:
   - Send welcome email with login instructions
   - Include both system URLs
   - Request password reset on first login

### For End Users (Clients)

1. **Receive Credentials**:
   - Single email/password for both systems
   - Links to both platforms

2. **Access Systems**:
   - **Odoo Portal**: `https://odoo.dxbmeta.com`
   - **Leantime**: `https://leantime.dxbmeta.com`
   - Use same email/password

3. **View Projects**:
   - Odoo: Customer portal for invoices, quotes
   - Leantime: Project management, tasks, progress

## Monitoring & Troubleshooting

### Check User Sync Status
```bash
# View bridge logs
docker logs odoo-bridge --tail 50 -f

# Check specific user
curl -H "Authorization: Bearer bridge_secure_key" \
     "http://localhost:8070/mcp/users?source=both" | \
     jq '.matched_users'
```

### Common Issues

#### Users Not Syncing
1. Check bridge connectivity:
   ```bash
   curl http://localhost:8070/admin/status
   ```
2. Verify Odoo connection
3. Check Leantime database access

#### Password Issues
1. Temporary password not working:
   - Check bcrypt hashing compatibility
   - Verify Leantime PHP version supports bcrypt
   - Use password reset flow

#### Role Mapping Incorrect
1. Check user's Odoo groups:
   ```python
   # In Odoo shell
   user.groups_id.mapped('full_name')
   ```
2. Update role_mapping in sync_manager.py if needed

### Manual User Sync
```python
# Python script for manual sync
import requests

api_key = "bridge_secure_key"
bridge_url = "http://localhost:8070"

# Sync specific user
response = requests.post(
    f"{bridge_url}/mcp/sync",
    headers={"Authorization": f"Bearer {api_key}"},
    json={
        "source": "odoo",
        "target": "leantime", 
        "entity_type": "users",
        "entity_ids": [user_id]
    }
)
print(response.json())
```

## Security Considerations

1. **API Security**:
   - Always use strong API keys
   - Implement webhook signature validation
   - Use HTTPS in production

2. **Password Security**:
   - Passwords are hashed, never stored in plain text
   - Temporary passwords should expire
   - Enforce password policies in both systems

3. **Data Privacy**:
   - User data synchronized includes only necessary fields
   - No sensitive data in logs
   - Audit trail maintained

## Future Enhancements

1. **SSO Implementation**:
   - OAuth2/OIDC provider
   - SAML integration
   - Session sharing

2. **Advanced Features**:
   - Bidirectional sync
   - Real-time sync via websockets
   - Group/team synchronization
   - Permission granularity

3. **Password Sync Options**:
   - API-based authentication proxy
   - Password change propagation
   - Single password reset flow

## Support

For issues or questions:
1. Check bridge logs: `docker logs odoo-bridge`
2. Verify database connectivity
3. Review this documentation
4. Check webhook delivery (if configured)

## Testing

Test the user sync with:
```bash
# Run integration test
python test_user_sync.py

# Manual test
curl -X POST -H "Authorization: Bearer bridge_secure_key" \
     -H "Content-Type: application/json" \
     -d '{"source":"odoo","target":"leantime","entity_type":"users"}' \
     http://localhost:8070/mcp/sync
```

## Conclusion

This unified account management system provides:
- ✅ Single credentials for both systems
- ✅ Automatic user provisioning
- ✅ Role-based access control
- ✅ Centralized user management in Odoo
- ✅ Seamless client experience

Users can now access both Odoo (for business operations) and Leantime (for project management) with the same email and password, significantly improving user experience and reducing administrative overhead.