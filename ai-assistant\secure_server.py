#!/usr/bin/env python3
"""
Secure MCP Server with Authentication, Rate Limiting, and Security Hardening
"""

import asyncio
import json
import logging
import sys
import time
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from collections import defaultdict

from aiohttp import web
import aiohttp_cors
import jwt
from functools import wraps

# Import configuration
from config.config import config

# Setup logging with secure formatting
logging.basicConfig(
    level=getattr(logging, config.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)

class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self, rate: int = 60, per: int = 60):
        self.rate = rate  # requests
        self.per = per    # seconds
        self.allowance = defaultdict(lambda: rate)
        self.last_check = defaultdict(lambda: time.time())
    
    def is_allowed(self, identifier: str) -> bool:
        """Check if request is allowed"""
        current = time.time()
        time_passed = current - self.last_check[identifier]
        self.last_check[identifier] = current
        
        # Add tokens based on time passed
        self.allowance[identifier] += time_passed * (self.rate / self.per)
        
        # Cap at rate limit
        if self.allowance[identifier] > self.rate:
            self.allowance[identifier] = self.rate
        
        # Check if we have tokens
        if self.allowance[identifier] < 1.0:
            return False
        
        self.allowance[identifier] -= 1.0
        return True

class SecureAuth:
    """Enhanced authentication with secure token handling"""
    
    def __init__(self):
        self.secret = config.security.jwt_secret
        self.algorithm = config.security.jwt_algorithm
        self.expiry_hours = config.security.jwt_expiry_hours
        self.active_tokens = set()  # Track active tokens for revocation
    
    def generate_token(self, user_id: int, username: str, role: str = 'user') -> str:
        """Generate secure JWT token"""
        jti = secrets.token_urlsafe(32)  # Unique token ID
        
        payload = {
            'user_id': user_id,
            'username': username,
            'role': role,
            'jti': jti,
            'exp': datetime.utcnow() + timedelta(hours=self.expiry_hours),
            'iat': datetime.utcnow(),
            'iss': 'ai-assistant'
        }
        
        token = jwt.encode(payload, self.secret, algorithm=self.algorithm)
        self.active_tokens.add(jti)
        return token
    
    def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate JWT token with revocation check"""
        try:
            payload = jwt.decode(
                token,
                self.secret,
                algorithms=[self.algorithm],
                options={"verify_exp": True, "verify_iat": True}
            )
            
            # Check if token is revoked
            if payload.get('jti') not in self.active_tokens:
                logger.warning(f"Token {payload.get('jti')} is revoked")
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
    
    def revoke_token(self, jti: str):
        """Revoke a token"""
        if jti in self.active_tokens:
            self.active_tokens.remove(jti)

class SecureMCPServer:
    """Secure MCP Server with comprehensive security features"""
    
    def __init__(self):
        self.app = web.Application()
        self.start_time = datetime.utcnow()
        self.request_count = 0
        self.auth = SecureAuth()
        self.rate_limiter = RateLimiter(
            rate=config.rate_limit_per_minute,
            per=60
        )
        self.setup_middleware()
        self.setup_routes()
        self.setup_cors()
        
        # Security headers middleware
        @web.middleware
        async def security_headers(request, handler):
            response = await handler(request)
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Content-Security-Policy'] = "default-src 'self'"
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            return response
        
        self.app.middlewares.append(security_headers)
    
    def setup_middleware(self):
        """Setup security middleware"""
        
        @web.middleware
        async def rate_limit_middleware(request, handler):
            """Rate limiting middleware"""
            # Get client identifier (IP or token)
            identifier = request.remote or request.headers.get('X-Forwarded-For', 'unknown')
            
            # Check rate limit
            if not self.rate_limiter.is_allowed(identifier):
                return web.json_response(
                    {'error': 'Rate limit exceeded'},
                    status=429,
                    headers={'Retry-After': '60'}
                )
            
            return await handler(request)
        
        @web.middleware
        async def error_handling(request, handler):
            """Secure error handling"""
            try:
                return await handler(request)
            except web.HTTPException as ex:
                # Don't leak internal information
                return web.json_response(
                    {'error': 'Request failed'},
                    status=ex.status
                )
            except Exception as e:
                logger.error(f"Unhandled error: {e}")
                # Generic error message
                return web.json_response(
                    {'error': 'Internal server error'},
                    status=500
                )
        
        self.app.middlewares.append(rate_limit_middleware)
        self.app.middlewares.append(error_handling)
    
    def setup_routes(self):
        """Setup server routes"""
        # Public endpoints
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_post('/auth/login', self.login)
        
        # Protected endpoints
        self.app.router.add_get('/status', self.protected_status)
        self.app.router.add_post('/api/v1/process', self.protected_process)
        self.app.router.add_post('/auth/logout', self.logout)
        self.app.router.add_post('/auth/refresh', self.refresh_token)
    
    def setup_cors(self):
        """Setup CORS with security restrictions"""
        cors = aiohttp_cors.setup(self.app, defaults={
            "http://localhost:*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods=["GET", "POST"]
            )
        })
        
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def health_check(self, request: web.Request) -> web.Response:
        """Health check endpoint (public)"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    async def login(self, request: web.Request) -> web.Response:
        """Login endpoint to get JWT token"""
        try:
            data = await request.json()
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return web.json_response(
                    {'error': 'Username and password required'},
                    status=400
                )
            
            # Hash password for comparison (in production, check against database)
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # Simple auth check (replace with database lookup)
            # Default admin account for testing
            if username == 'admin' and password_hash == hashlib.sha256('admin123'.encode()).hexdigest():
                token = self.auth.generate_token(1, username, 'admin')
                
                logger.info(f"User {username} logged in successfully")
                
                return web.json_response({
                    'success': True,
                    'token': token,
                    'expires_in': self.auth.expiry_hours * 3600
                })
            
            logger.warning(f"Failed login attempt for user {username}")
            
            return web.json_response(
                {'error': 'Invalid credentials'},
                status=401
            )
            
        except json.JSONDecodeError:
            return web.json_response(
                {'error': 'Invalid JSON'},
                status=400
            )
    
    def require_auth(self, func):
        """Decorator for protected endpoints"""
        async def wrapper(request: web.Request):
            # Extract token
            auth_header = request.headers.get('Authorization', '')
            
            if not auth_header.startswith('Bearer '):
                return web.json_response(
                    {'error': 'Missing or invalid authorization header'},
                    status=401
                )
            
            token = auth_header[7:]
            payload = self.auth.validate_token(token)
            
            if not payload:
                return web.json_response(
                    {'error': 'Invalid or expired token'},
                    status=401
                )
            
            # Add user info to request
            request['user'] = payload
            
            return await func(request)
        
        return wrapper
    
    async def protected_status(self, request: web.Request) -> web.Response:
        """Protected status endpoint"""
        # Check auth
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return web.json_response({'error': 'Authentication required'}, status=401)
        
        token = auth_header[7:]
        payload = self.auth.validate_token(token)
        if not payload:
            return web.json_response({'error': 'Invalid token'}, status=401)
        
        uptime = (datetime.utcnow() - self.start_time).total_seconds()
        
        return web.json_response({
            'status': 'running',
            'version': '2.0.0',
            'uptime_seconds': uptime,
            'request_count': self.request_count,
            'user': payload.get('username'),
            'role': payload.get('role')
        })
    
    async def protected_process(self, request: web.Request) -> web.Response:
        """Protected API processing endpoint"""
        # Check auth
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return web.json_response({'error': 'Authentication required'}, status=401)
        
        token = auth_header[7:]
        payload = self.auth.validate_token(token)
        if not payload:
            return web.json_response({'error': 'Invalid token'}, status=401)
        
        self.request_count += 1
        
        try:
            data = await request.json()
            
            # Input validation
            if 'action' not in data:
                return web.json_response(
                    {'error': 'Missing required field: action'},
                    status=400
                )
            
            # Sanitize action to prevent injection
            action = str(data['action']).replace('/', '').replace('\\', '').replace('..', '')
            
            # Log the request (without sensitive data)
            logger.info(f"User {payload.get('username')} requested action: {action}")
            
            # Process based on action
            result = await self.process_action(action, data, payload)
            
            return web.json_response({
                'success': True,
                'action': action,
                'result': result
            })
            
        except json.JSONDecodeError:
            return web.json_response(
                {'error': 'Invalid JSON'},
                status=400
            )
    
    async def logout(self, request: web.Request) -> web.Response:
        """Logout endpoint to revoke token"""
        # Check auth
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return web.json_response({'error': 'Authentication required'}, status=401)
        
        token = auth_header[7:]
        payload = self.auth.validate_token(token)
        if not payload:
            return web.json_response({'error': 'Invalid token'}, status=401)
        
        # Revoke token
        self.auth.revoke_token(payload.get('jti'))
        
        logger.info(f"User {payload.get('username')} logged out")
        
        return web.json_response({'success': True, 'message': 'Logged out successfully'})
    
    async def refresh_token(self, request: web.Request) -> web.Response:
        """Refresh JWT token"""
        # Check auth
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return web.json_response({'error': 'Authentication required'}, status=401)
        
        token = auth_header[7:]
        payload = self.auth.validate_token(token)
        if not payload:
            return web.json_response({'error': 'Invalid token'}, status=401)
        
        # Revoke old token
        self.auth.revoke_token(payload.get('jti'))
        
        # Generate new token
        new_token = self.auth.generate_token(
            payload.get('user_id'),
            payload.get('username'),
            payload.get('role')
        )
        
        return web.json_response({
            'success': True,
            'token': new_token,
            'expires_in': self.auth.expiry_hours * 3600
        })
    
    async def process_action(self, action: str, data: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """Process action with user context"""
        # Available actions based on user role
        if user.get('role') == 'admin':
            allowed_actions = [
                'expense.process', 'ticket.process', 
                'knowledge.search', 'workflow.execute',
                'system.status', 'system.metrics'
            ]
        else:
            allowed_actions = [
                'expense.process', 'ticket.process',
                'knowledge.search'
            ]
        
        if action not in allowed_actions:
            raise ValueError(f"Unauthorized action: {action}")
        
        # Process action (placeholder implementations)
        return {
            'message': f'Action {action} processed for user {user.get("username")}',
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def run(self):
        """Run the server"""
        logger.info(f"Starting Secure MCP Server on {config.server_host}:{config.server_port}")
        logger.info("Security features enabled: Authentication, Rate Limiting, CORS, Security Headers")
        
        web.run_app(
            self.app,
            host='0.0.0.0',  # Bind to all interfaces in container
            port=config.server_port
        )

def main():
    """Main entry point"""
    try:
        server = SecureMCPServer()
        server.run()
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()