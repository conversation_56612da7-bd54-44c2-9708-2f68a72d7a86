#!/usr/bin/env python3
"""
OpenRouter Integration for Leantime
Uses OpenRouter API to access 100+ models including open-source options
"""

import os
import json
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from enum import Enum
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenRouterConfig:
    """OpenRouter API Configuration"""
    API_KEY = os.getenv("OPENROUTER_API_KEY")
    BASE_URL = "https://openrouter.ai/api/v1"
    APP_URL = os.getenv("LEANTIME_URL", "https://leantime.io")
    APP_NAME = "Leantime Project Management"
    
    # Model configurations with pricing (per million tokens)
    MODELS = {
        # Free Models
        "mistral-7b-free": {
            "id": "mistralai/mistral-7b-instruct:free",
            "name": "Mistral 7B (Free)",
            "context": 8192,
            "cost": 0,
            "speed": "fast",
            "quality": "good"
        },
        "mythomist-free": {
            "id": "gryphe/mythomist-7b:free",
            "name": "Mythomist 7B (Free)",
            "context": 8192,
            "cost": 0,
            "speed": "fast",
            "quality": "good"
        },
        
        # Open Source Models
        "llama-3.1-70b": {
            "id": "meta-llama/llama-3.1-70b-instruct",
            "name": "Llama 3.1 70B",
            "context": 131072,
            "cost": 0.59,  # $0.59/million tokens
            "speed": "medium",
            "quality": "excellent"
        },
        "llama-3.1-8b": {
            "id": "meta-llama/llama-3.1-8b-instruct",
            "name": "Llama 3.1 8B",
            "context": 131072,
            "cost": 0.06,
            "speed": "fast",
            "quality": "good"
        },
        "mixtral-8x7b": {
            "id": "mistralai/mixtral-8x7b-instruct",
            "name": "Mixtral 8x7B",
            "context": 32768,
            "cost": 0.24,
            "speed": "fast",
            "quality": "very_good"
        },
        "deepseek-coder": {
            "id": "deepseek/deepseek-coder-33b-instruct",
            "name": "DeepSeek Coder 33B",
            "context": 16384,
            "cost": 0.08,
            "speed": "fast",
            "quality": "excellent_for_code"
        },
        "phi-3": {
            "id": "microsoft/phi-3-mini-128k-instruct",
            "name": "Phi-3 Mini",
            "context": 128000,
            "cost": 0.02,
            "speed": "very_fast",
            "quality": "good_for_simple"
        },
        "qwen-2.5": {
            "id": "qwen/qwen-2.5-72b-instruct",
            "name": "Qwen 2.5 72B",
            "context": 32768,
            "cost": 0.35,
            "speed": "medium",
            "quality": "excellent_analysis"
        },
        
        # Auto Router
        "auto": {
            "id": "openrouter/auto",
            "name": "Auto (Best Match)",
            "context": 0,
            "cost": 0,  # Varies
            "speed": "varies",
            "quality": "optimized"
        }
    }

class TaskType(Enum):
    """Task types for model selection"""
    PROJECT_ANALYSIS = "project_analysis"
    TASK_BREAKDOWN = "task_breakdown"
    EFFORT_ESTIMATION = "effort_estimation"
    SPRINT_PLANNING = "sprint_planning"
    REPORT_GENERATION = "report_generation"
    RISK_ASSESSMENT = "risk_assessment"
    CODE_GENERATION = "code_generation"
    SIMPLE_QUERY = "simple_query"

class OpenRouterClient:
    """OpenRouter API Client"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or OpenRouterConfig.API_KEY
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable not set")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": OpenRouterConfig.APP_URL,
            "X-Title": OpenRouterConfig.APP_NAME,
            "Content-Type": "application/json"
        }
    
    def select_model(self, task_type: TaskType, complexity: str = "medium", 
                    prefer_free: bool = True, max_cost: float = 0.5) -> str:
        """Select appropriate model based on task type and constraints"""
        
        model_selection = {
            TaskType.PROJECT_ANALYSIS: {
                "simple": "llama-3.1-8b",
                "medium": "mixtral-8x7b",
                "complex": "llama-3.1-70b"
            },
            TaskType.TASK_BREAKDOWN: {
                "simple": "mistral-7b-free",
                "medium": "llama-3.1-8b",
                "complex": "mixtral-8x7b"
            },
            TaskType.EFFORT_ESTIMATION: {
                "simple": "phi-3",
                "medium": "mistral-7b-free",
                "complex": "llama-3.1-8b"
            },
            TaskType.SPRINT_PLANNING: {
                "simple": "llama-3.1-8b",
                "medium": "mixtral-8x7b",
                "complex": "llama-3.1-70b"
            },
            TaskType.REPORT_GENERATION: {
                "simple": "mistral-7b-free",
                "medium": "llama-3.1-8b",
                "complex": "llama-3.1-70b"
            },
            TaskType.RISK_ASSESSMENT: {
                "simple": "llama-3.1-8b",
                "medium": "mixtral-8x7b",
                "complex": "qwen-2.5"
            },
            TaskType.CODE_GENERATION: {
                "simple": "deepseek-coder",
                "medium": "deepseek-coder",
                "complex": "deepseek-coder"
            },
            TaskType.SIMPLE_QUERY: {
                "simple": "mistral-7b-free",
                "medium": "mistral-7b-free",
                "complex": "llama-3.1-8b"
            }
        }
        
        # Get recommended model
        model_key = model_selection.get(task_type, {}).get(complexity, "mistral-7b-free")
        model = OpenRouterConfig.MODELS[model_key]
        
        # Check cost constraints
        if prefer_free and model["cost"] > 0:
            # Try to find a free alternative
            for key, m in OpenRouterConfig.MODELS.items():
                if m["cost"] == 0 and "free" in key:
                    return m["id"]
        
        if model["cost"] > max_cost:
            # Find cheaper alternative
            cheaper = sorted(
                [(k, m) for k, m in OpenRouterConfig.MODELS.items() if m["cost"] <= max_cost],
                key=lambda x: x[1]["cost"],
                reverse=True
            )
            if cheaper:
                return cheaper[0][1]["id"]
        
        return model["id"]
    
    async def complete(self, 
                      prompt: str,
                      model: Optional[str] = None,
                      task_type: Optional[TaskType] = None,
                      complexity: str = "medium",
                      max_tokens: int = 2048,
                      temperature: float = 0.7,
                      stream: bool = False) -> Dict[str, Any]:
        """Send completion request to OpenRouter"""
        
        # Auto-select model if not provided
        if not model:
            if task_type:
                model = self.select_model(task_type, complexity)
            else:
                model = "mistralai/mistral-7b-instruct:free"  # Default free model
        
        # Ensure model ID format
        if model in OpenRouterConfig.MODELS:
            model = OpenRouterConfig.MODELS[model]["id"]
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream
        }
        
        logger.info(f"Using model: {model}")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{OpenRouterConfig.BASE_URL}/chat/completions",
                headers=self.headers,
                json=payload
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"OpenRouter API error: {error_text}")
                    raise Exception(f"API error: {response.status} - {error_text}")
                
                result = await response.json()
                
                # Extract response
                if "choices" in result and result["choices"]:
                    content = result["choices"][0]["message"]["content"]
                    usage = result.get("usage", {})
                    
                    # Calculate cost if available
                    cost = 0
                    if usage and model in OpenRouterConfig.MODELS:
                        model_info = OpenRouterConfig.MODELS[model]
                        total_tokens = usage.get("total_tokens", 0)
                        cost = (total_tokens / 1_000_000) * model_info.get("cost", 0)
                    
                    return {
                        "content": content,
                        "model": result.get("model", model),
                        "usage": usage,
                        "cost": cost
                    }
                else:
                    raise Exception("No response from model")
    
    async def complete_with_fallback(self,
                                    prompt: str,
                                    task_type: TaskType,
                                    complexity: str = "medium",
                                    max_retries: int = 3) -> Dict[str, Any]:
        """Complete with automatic fallback to other models"""
        
        # Model fallback chain
        fallback_chain = [
            self.select_model(task_type, complexity, prefer_free=False),
            self.select_model(task_type, "simple", prefer_free=False),
            "mistralai/mistral-7b-instruct:free",
            "gryphe/mythomist-7b:free"
        ]
        
        for i, model in enumerate(fallback_chain[:max_retries]):
            try:
                logger.info(f"Attempt {i+1}: Using model {model}")
                result = await self.complete(
                    prompt=prompt,
                    model=model,
                    task_type=task_type,
                    complexity=complexity
                )
                return result
            except Exception as e:
                logger.warning(f"Model {model} failed: {e}")
                if i == max_retries - 1:
                    raise
        
        raise Exception("All models failed")

# Leantime-specific functions using OpenRouter

class LeantimeAI:
    """AI-powered features for Leantime using OpenRouter"""
    
    def __init__(self):
        self.client = OpenRouterClient()
    
    async def analyze_project_health(self, project_data: Dict) -> Dict:
        """Analyze project health using AI"""
        
        prompt = f"""
        Analyze the health of this project and provide insights:
        
        Project: {project_data['name']}
        Status: {project_data.get('status', 'active')}
        Tasks: {len(project_data.get('tasks', []))} total
        Completed: {len([t for t in project_data.get('tasks', []) if t.get('status') == 'done'])}
        Team Size: {len(project_data.get('team', []))}
        
        Provide:
        1. Overall health assessment (healthy/at-risk/critical)
        2. Key issues identified
        3. Specific recommendations
        4. Predicted completion date
        5. Risk factors
        
        Format as JSON.
        """
        
        result = await self.client.complete_with_fallback(
            prompt=prompt,
            task_type=TaskType.PROJECT_ANALYSIS,
            complexity="medium"
        )
        
        try:
            # Parse JSON response
            content = result["content"]
            # Extract JSON from response (handle markdown code blocks)
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            analysis = json.loads(content)
            analysis["model_used"] = result["model"]
            analysis["cost"] = result.get("cost", 0)
            return analysis
        except:
            # Fallback to text response
            return {
                "analysis": result["content"],
                "model_used": result["model"],
                "cost": result.get("cost", 0)
            }
    
    async def estimate_task_effort(self, task: Dict) -> Dict:
        """Estimate task effort using AI"""
        
        prompt = f"""
        Estimate the effort required for this task:
        
        Title: {task.get('title', '')}
        Description: {task.get('description', '')}
        Tags: {task.get('tags', [])}
        Priority: {task.get('priority', 'medium')}
        
        Provide:
        1. Estimated hours (min, likely, max)
        2. Complexity level (simple/medium/complex)
        3. Confidence level (low/medium/high)
        4. Factors affecting estimate
        5. Suggested subtasks if complex
        
        Be realistic and consider testing, review, and documentation time.
        Format as JSON.
        """
        
        result = await self.client.complete_with_fallback(
            prompt=prompt,
            task_type=TaskType.EFFORT_ESTIMATION,
            complexity="simple"
        )
        
        return {
            "estimate": result["content"],
            "model_used": result["model"],
            "cost": result.get("cost", 0)
        }
    
    async def generate_status_report(self, project_data: Dict, audience: str = "team") -> Dict:
        """Generate status report using AI"""
        
        prompt = f"""
        Generate a {audience} status report for:
        
        Project: {project_data['name']}
        Period: Last 7 days
        Completed Tasks: {len([t for t in project_data.get('tasks', []) if t.get('status') == 'done'])}
        In Progress: {len([t for t in project_data.get('tasks', []) if t.get('status') == 'in-progress'])}
        Blocked: {len([t for t in project_data.get('tasks', []) if 'blocked' in str(t.get('tags', ''))])}
        
        Create a professional status report including:
        1. Executive summary
        2. Key achievements
        3. Current challenges
        4. Next steps
        5. Metrics and KPIs
        
        Tailor the tone and detail level for {audience}.
        """
        
        result = await self.client.complete_with_fallback(
            prompt=prompt,
            task_type=TaskType.REPORT_GENERATION,
            complexity="medium"
        )
        
        return {
            "report": result["content"],
            "model_used": result["model"],
            "cost": result.get("cost", 0)
        }
    
    async def optimize_sprint(self, tasks: List[Dict], capacity: Dict) -> Dict:
        """Optimize sprint planning using AI"""
        
        prompt = f"""
        Optimize sprint planning with:
        
        Available Tasks: {len(tasks)} tasks
        Team Capacity: {capacity}
        Sprint Duration: 2 weeks
        
        Task Priorities: {[t.get('priority') for t in tasks[:10]]}
        
        Recommend:
        1. Which tasks to include in sprint
        2. Optimal task assignments
        3. Sprint goal
        4. Risk factors
        5. Dependencies to watch
        
        Consider team capacity, task priorities, and dependencies.
        Format as actionable recommendations.
        """
        
        result = await self.client.complete_with_fallback(
            prompt=prompt,
            task_type=TaskType.SPRINT_PLANNING,
            complexity="complex"
        )
        
        return {
            "recommendations": result["content"],
            "model_used": result["model"],
            "cost": result.get("cost", 0)
        }

# Example usage
async def main():
    """Example usage of OpenRouter with Leantime"""
    
    # Initialize
    ai = LeantimeAI()
    
    # Example project data
    project = {
        "name": "Website Redesign",
        "status": "active",
        "tasks": [
            {"title": "Design mockups", "status": "done", "priority": "high"},
            {"title": "Implement homepage", "status": "in-progress", "priority": "high"},
            {"title": "Setup database", "status": "todo", "priority": "medium"},
            {"title": "Write tests", "status": "todo", "priority": "low"}
        ],
        "team": ["Alice", "Bob", "Charlie"]
    }
    
    # Analyze project health
    print("Analyzing project health...")
    health = await ai.analyze_project_health(project)
    print(f"Health Analysis: {json.dumps(health, indent=2)}")
    
    # Estimate task effort
    print("\nEstimating task effort...")
    task = {"title": "Implement user authentication", "description": "Add login and registration"}
    estimate = await ai.estimate_task_effort(task)
    print(f"Effort Estimate: {estimate}")
    
    # Generate report
    print("\nGenerating status report...")
    report = await ai.generate_status_report(project, "management")
    print(f"Status Report: {report['report'][:500]}...")
    print(f"Model used: {report['model_used']}, Cost: ${report['cost']:.4f}")

if __name__ == "__main__":
    asyncio.run(main())