# BAML Integration for Leantime Project Management

## What is this?

This integrates **BAML (Boundary ML)** - an AI prompt engineering framework - with **Leantime**, your open-source project management tool. It adds AI-powered features to help teams work more efficiently.

## What does it actually do?

The BAML integration adds intelligent features to Leantime:

### 🏥 **Project Health Monitoring**
- Automatically analyzes your project's status
- Identifies risks before they become problems
- Predicts realistic completion dates
- Gives actionable recommendations

### 📝 **Smart Task Management**
- Breaks down complex tasks into manageable subtasks
- Estimates effort based on historical data
- Suggests optimal task assignments
- Identifies dependencies automatically

### 🚀 **Sprint Optimization**
- Recommends which tasks to include in sprints
- Balances workload across team members
- Considers team capacity and skills
- Generates clear sprint goals

### 📊 **Automated Reporting**
- Generates status reports for different audiences
- Creates client-friendly updates
- Produces management dashboards
- Provides team performance insights

### ⚡ **Effort Estimation**
- Predicts task duration based on complexity
- Learns from your team's historical data
- Provides confidence levels for estimates
- Identifies factors affecting timeline

## Architecture

```
Your Leantime Docker Setup
    ↓
MySQL Database (your project data)
    ↓
BAML Integration Layer (FastAPI)
    ↓
AI Models (Claude/GPT-4)
    ↓
Enhanced Insights Back to Leantime
```

## Quick Start

### 1. Set up API Keys
Add to your `.env` file:
```bash
ANTHROPIC_API_KEY=your_claude_api_key
# or
OPENAI_API_KEY=your_openai_api_key
```

### 2. Start the BAML Service
```bash
# Install dependencies
cd baml/python
pip install fastapi uvicorn mysql-connector-python

# Run the service
python leantime_baml.py
```

### 3. Access the API
- API Docs: http://localhost:8000/docs
- Health Check: http://localhost:8000/

## API Examples

### Check Project Health
```bash
curl -X POST "http://localhost:8000/api/project-health" \
  -H "Content-Type: application/json" \
  -d '{"project_id": 1}'
```

Response:
```json
{
  "overall_health": "at-risk",
  "score": 65,
  "issues": ["Low task completion rate", "3 overdue tasks"],
  "recommendations": ["Review task priorities", "Redistribute workload"],
  "predicted_completion_date": "2025-10-15"
}
```

### Optimize Sprint Planning
```bash
curl -X POST "http://localhost:8000/api/optimize-sprint" \
  -H "Content-Type: application/json" \
  -d '{"project_id": 1, "sprint_duration": 14}'
```

### Estimate Task Effort
```bash
curl -X POST "http://localhost:8000/api/estimate-effort" \
  -H "Content-Type: application/json" \
  -d '{
    "task_title": "Implement user authentication",
    "task_description": "Add login and registration features",
    "tags": ["backend", "security"]
  }'
```

## Docker Deployment

Add to your `docker-compose.yml`:

```yaml
services:
  leantime-baml:
    build: ./baml
    container_name: leantime-baml
    environment:
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - LEAN_DB_HOST=mysql_leantime
      - LEAN_DB_USER=${LEAN_DB_USER}
      - LEAN_DB_PASSWORD=${LEAN_DB_PASSWORD}
      - LEAN_DB_DATABASE=${LEAN_DB_DATABASE}
    ports:
      - "8000:8000"
    depends_on:
      - mysql_leantime
    networks:
      - leantime-net
```

## Benefits

- **Save Time**: Automate repetitive project management tasks
- **Better Estimates**: AI-powered predictions based on your data
- **Reduce Risk**: Early warning system for project issues
- **Improved Planning**: Data-driven sprint and resource allocation
- **Clear Communication**: Automated reports for stakeholders

## How It Works

1. **Connects to Your Database**: Reads your existing Leantime data
2. **Analyzes with AI**: Uses Claude or GPT-4 to find patterns and insights
3. **Returns Actionable Info**: Provides specific recommendations
4. **No Data Storage**: Doesn't store any data separately - uses your existing database

## Security

- API keys are kept secure in environment variables
- Read-only access to analyze data (writes only when you explicitly save)
- No data leaves your infrastructure (except API calls to AI providers)
- Can be configured to use local AI models for complete privacy

## Customization

The BAML functions in `baml/baml_src/leantime_core.baml` can be customized to:
- Add your company's specific processes
- Include custom metrics
- Adjust estimation algorithms
- Change report formats
- Add new AI capabilities

## Troubleshooting

### Database Connection Issues
- Verify MySQL is running: `docker ps`
- Check credentials in `.env` file
- Ensure network connectivity between containers

### API Key Issues
- Verify API key is valid
- Check environment variables are set
- Ensure sufficient API credits

### Performance
- API responses typically take 1-3 seconds
- For large projects, consider pagination
- Cache frequently accessed data

## Next Steps

1. **Try the health check** on one of your projects
2. **Generate a status report** for your next meeting
3. **Use sprint optimization** for planning
4. **Monitor the results** and adjust as needed

## Support

- Leantime Docs: https://docs.leantime.io
- BAML Docs: https://docs.boundaryml.com
- Issues: Create an issue in this repository

---

**Note**: This is an AI enhancement layer for Leantime. It provides insights and recommendations but doesn't replace human judgment in project management decisions.