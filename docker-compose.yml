version: '3.3'

services:
  leantime_db:
    image: mysql:8.4
    container_name: mysql_leantime
    volumes:
      - db_data:/var/lib/mysql
    restart: unless-stopped
    env_file: ./.env                                        # Environment file with settings
    networks:
      - leantime-net
    command: --character-set-server=UTF8MB4 --collation-server=UTF8MB4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  leantime:
    image: leantime/leantime:${LEAN_VERSION:-latest}
    restart: unless-stopped
    env_file: ./.env                                        # Environment file with settings
    # Add security options
    security_opt:
      - no-new-privileges:true
    # Add capabilities
    cap_add:
      # - CAP_NET_BIND_SERVICE
      - CAP_CHOWN
      - CAP_SETGID
      - CAP_SETUID
    ports:
      - "${LEAN_PORT:-8080}:8080"
    networks:
      - leantime-net
    volumes:
      - public_userfiles:/var/www/html/public/userfiles     # Volume to store public files, logo etc
      - userfiles:/var/www/html/userfiles                   # Original volume name for compatibility
      - plugins:/var/www/html/app/Plugins                   # Plugin storage
      - logs:/var/www/html/storage/logs                     # Log storage
    depends_on:
      leantime_db:
        condition: service_healthy
      email-service:
        condition: service_healthy

  # Email Service - SMTP to Resend API Bridge
  email-service:
    build:
      context: .
      dockerfile: Dockerfile.resend-api
    container_name: email-service
    restart: unless-stopped
    environment:
      - RESEND_API_KEY=${RESEND_API_KEY}
      - EMAIL_PORT=2525
      - SMTP_PORT=2525
      - FROM_DOMAIN=dxbmeta.com
      - FROM_ADDRESS=<EMAIL>
    ports:
      - "2525:2525"  # HTTP API port
      - "2525:2525"  # SMTP port (unified)
    networks:
      - leantime-net
    volumes:
      - email_logs:/app/logs
      - email_queue:/app/queue
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:2525/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Add a helper container for volume permissions
  # Run via docker compose --profile mysql_helper up -d
  mysql_helper:
    image: mysql:8.4
    command: chown -R mysql:mysql /var/lib/mysql
    volumes:
      - db_data:/var/lib/mysql
    user: root
    profiles: [ "helper" ]

volumes:
  db_data:
  userfiles:                                               # New volume for public files
  public_userfiles:
  plugins:
  logs:
  email_logs:                                              # Email service logs
  email_queue:                                             # Email queue storage

networks:
  leantime-net:
