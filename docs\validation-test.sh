#!/bin/bash

# Comprehensive Podman Migration Validation Script
# Validates all Podman configurations and readiness for production migration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="/tmp/podman-validation-$(date +%Y%m%d-%H%M%S).log"

# Test counters
PASSED=0
FAILED=0
WARNINGS=0

log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    case $level in
        "PASS") echo -e "${GREEN}[✓]${NC} $message" ;;
        "FAIL") echo -e "${RED}[✗]${NC} $message" ;;
        "WARN") echo -e "${YELLOW}[!]${NC} $message" ;;
        "INFO") echo -e "${BLUE}[i]${NC} $message" ;;
        "TEST") echo -e "${CYAN}[TEST]${NC} $message" ;;
    esac
}

test_result() {
    local status=$1
    local message=$2
    
    case $status in
        "PASS")
            ((PASSED++))
            log "PASS" "$message"
            ;;
        "FAIL")
            ((FAILED++))
            log "FAIL" "$message"
            ;;
        "WARN")
            ((WARNINGS++))
            log "WARN" "$message"
            ;;
    esac
}

# 1. Environment Verification
test_environment() {
    log "TEST" "Testing environment setup..."
    
    # Check Podman installation
    if command -v podman >/dev/null 2>&1; then
        local version=$(podman --version 2>/dev/null)
        test_result "PASS" "Podman installed: $version"
        
        # Check Podman machine
        if podman machine list | grep -q "Currently running"; then
            test_result "PASS" "Podman machine is running"
        else
            test_result "WARN" "Podman machine not running"
        fi
    else
        test_result "FAIL" "Podman not installed"
        return 1
    fi
    
    # Check podman-compose
    if python -m podman_compose --version >/dev/null 2>&1; then
        local version=$(python -m podman_compose --version 2>/dev/null | tail -n1)
        test_result "PASS" "podman-compose available: $version"
    else
        test_result "WARN" "podman-compose not available, will use 'podman compose'"
    fi
    
    # Check required tools
    local tools=("curl" "jq" "mysqladmin" "git")
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            test_result "PASS" "$tool is available"
        else
            test_result "WARN" "$tool not available"
        fi
    done
    
    return 0
}

# 2. Configuration Testing
test_configurations() {
    log "TEST" "Testing Podman configurations..."
    
    # Test basic compose file
    if [ -f "$SCRIPT_DIR/podman-compose.yml" ]; then
        if python -m podman_compose -f "$SCRIPT_DIR/podman-compose.yml" config --services >/dev/null 2>&1; then
            test_result "PASS" "Basic podman-compose.yml syntax valid"
        else
            test_result "FAIL" "Basic podman-compose.yml syntax invalid"
        fi
    else
        test_result "FAIL" "podman-compose.yml not found"
    fi
    
    # Test production compose file
    if [ -f "$SCRIPT_DIR/podman-compose.production.yml" ]; then
        if python -m podman_compose -f "$SCRIPT_DIR/podman-compose.production.yml" config --services >/dev/null 2>&1; then
            test_result "PASS" "Production podman-compose.yml syntax valid"
        else
            test_result "FAIL" "Production podman-compose.yml syntax invalid"
        fi
    else
        test_result "FAIL" "podman-compose.production.yml not found"
    fi
    
    # Test environment file
    if [ -f "$SCRIPT_DIR/.env" ]; then
        # Check for basic required variables
        local required_vars=("MYSQL_ROOT_PASSWORD" "MYSQL_DATABASE" "MYSQL_USER" "MYSQL_PASSWORD")
        for var in "${required_vars[@]}"; do
            if grep -q "^$var" "$SCRIPT_DIR/.env"; then
                test_result "PASS" ".env contains $var"
            else
                test_result "WARN" ".env missing $var"
            fi
        done
    else
        test_result "FAIL" ".env file not found"
    fi
    
    return 0
}

# 3. Script Testing
test_scripts() {
    log "TEST" "Testing deployment scripts..."
    
    # Test script syntax
    local scripts=(
        "scripts/migrate-to-podman.sh"
        "scripts/deploy-direct-podman.sh"
        "scripts/rollback-podman.sh"
        "scripts/podman-health-check.sh"
        "scripts/zero-downtime-deploy-podman.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$SCRIPT_DIR/$script" ]; then
            if bash -n "$SCRIPT_DIR/$script" 2>/dev/null; then
                test_result "PASS" "$script syntax valid"
            else
                test_result "FAIL" "$script syntax invalid"
            fi
        else
            test_result "WARN" "$script not found"
        fi
    done
    
    # Test PowerShell scripts if available
    if command -v pwsh >/dev/null 2>&1; then
        local ps_scripts=(
            "scripts/podman-windows-manager.ps1"
            "scripts/build-and-deploy-podman.ps1"
            "scripts/orchestrate-deployment-podman.ps1"
        )
        
        for script in "${ps_scripts[@]}"; do
            if [ -f "$SCRIPT_DIR/$script" ]; then
                if pwsh -Command "& { try { . '$SCRIPT_DIR/$script' } catch { exit 1 } }" >/dev/null 2>&1; then
                    test_result "PASS" "$script syntax valid"
                else
                    test_result "FAIL" "$script syntax invalid"
                fi
            else
                test_result "WARN" "$script not found"
            fi
        done
    else
        test_result "WARN" "PowerShell not available - skipping PS1 script tests"
    fi
    
    return 0
}

# 4. Network and Security Testing
test_network_security() {
    log "TEST" "Testing network and security configurations..."
    
    # Check if we can create networks
    local test_network="podman-validation-test"
    if podman network create "$test_network" >/dev/null 2>&1; then
        test_result "PASS" "Can create Podman networks"
        podman network rm "$test_network" >/dev/null 2>&1
    else
        test_result "FAIL" "Cannot create Podman networks"
    fi
    
    # Check volume creation
    local test_volume="podman-validation-vol"
    if podman volume create "$test_volume" >/dev/null 2>&1; then
        test_result "PASS" "Can create Podman volumes"
        podman volume rm "$test_volume" >/dev/null 2>&1
    else
        test_result "FAIL" "Cannot create Podman volumes"
    fi
    
    return 0
}

# 5. Simple Service Integration Test
test_service_integration() {
    log "TEST" "Testing basic service integration..."
    
    # Create a minimal test setup
    local test_dir="/tmp/podman-test-$(date +%H%M%S)"
    mkdir -p "$test_dir"
    
    cat > "$test_dir/test-compose.yml" << 'EOF'
version: '3.8'
services:
  test-nginx:
    image: docker.io/nginx:alpine
    container_name: test-nginx-integration
    ports:
      - "18888:80"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 5s
      timeout: 3s
      retries: 3
EOF
    
    cd "$test_dir"
    
    # Try to start and test the service
    if timeout 60 python -m podman_compose -f test-compose.yml up -d >/dev/null 2>&1; then
        sleep 10
        
        if curl -f http://localhost:18888 >/dev/null 2>&1; then
            test_result "PASS" "Service integration test successful"
        else
            test_result "WARN" "Service started but not responding"
        fi
        
        # Cleanup
        python -m podman_compose -f test-compose.yml down >/dev/null 2>&1
    else
        test_result "WARN" "Could not start test service (may be permission issue)"
    fi
    
    # Cleanup
    rm -rf "$test_dir"
    
    return 0
}

# 6. Performance and Resource Testing
test_performance() {
    log "TEST" "Testing performance and resource constraints..."
    
    # Check available resources
    local available_memory=$(free -m 2>/dev/null | awk '/^Mem:/ {print $7}' || echo "unknown")
    if [ "$available_memory" != "unknown" ] && [ "$available_memory" -gt 2000 ]; then
        test_result "PASS" "Sufficient memory available: ${available_memory}MB"
    else
        test_result "WARN" "Limited memory available: ${available_memory}MB"
    fi
    
    # Check disk space
    local available_disk=$(df /tmp 2>/dev/null | awk 'NR==2 {print int($4/1024)}' || echo "unknown")
    if [ "$available_disk" != "unknown" ] && [ "$available_disk" -gt 5000 ]; then
        test_result "PASS" "Sufficient disk space: ${available_disk}MB"
    else
        test_result "WARN" "Limited disk space: ${available_disk}MB"
    fi
    
    return 0
}

# 7. Windows/WSL Specific Tests
test_windows_wsl() {
    log "TEST" "Testing Windows/WSL specific configurations..."
    
    # Check if running on Windows/WSL
    if grep -qi microsoft /proc/version 2>/dev/null; then
        test_result "INFO" "Running on WSL"
        
        # Check WSL version
        if grep -qi "WSL2" /proc/version 2>/dev/null; then
            test_result "PASS" "Running on WSL2"
        else
            test_result "WARN" "Running on WSL1 - WSL2 recommended"
        fi
        
        # Check Windows Docker Desktop integration
        if [ -S "/var/run/docker.sock" ]; then
            test_result "INFO" "Docker Desktop integration available"
        fi
        
    elif uname -a | grep -qi "MINGW\|MSYS\|CYGWIN"; then
        test_result "INFO" "Running on Windows Git Bash/MinGW"
        
        # Check if running in elevated terminal
        if powershell.exe -Command "[bool]([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] 'Administrator')" 2>/dev/null | grep -q "True"; then
            test_result "PASS" "Running with administrator privileges"
        else
            test_result "WARN" "Not running with administrator privileges - some operations may fail"
        fi
    else
        test_result "INFO" "Running on native Linux"
    fi
    
    return 0
}

# 8. Generate Report
generate_report() {
    echo ""
    echo "=========================================="
    echo -e "${CYAN}Podman Migration Validation Report${NC}"
    echo "=========================================="
    echo "Validation run: $(date)"
    echo -e "Tests passed: ${GREEN}$PASSED${NC}"
    echo -e "Tests failed: ${RED}$FAILED${NC}"
    echo -e "Warnings: ${YELLOW}$WARNINGS${NC}"
    echo ""
    
    local total=$((PASSED + FAILED + WARNINGS))
    if [ $total -gt 0 ]; then
        local success_rate=$(( (PASSED * 100) / total ))
        echo "Success rate: ${success_rate}%"
    fi
    
    echo "Log file: $LOG_FILE"
    echo ""
    
    # Migration readiness assessment
    if [ $FAILED -eq 0 ]; then
        if [ $WARNINGS -eq 0 ]; then
            echo -e "${GREEN}✓ MIGRATION READY${NC} - All tests passed"
            echo "  Recommendation: Proceed with production migration"
        else
            echo -e "${YELLOW}⚠ MIGRATION READY WITH WARNINGS${NC}"
            echo "  Recommendation: Address warnings before production migration"
        fi
    else
        echo -e "${RED}✗ NOT READY FOR MIGRATION${NC}"
        echo "  Recommendation: Fix failed tests before attempting migration"
    fi
    
    echo "=========================================="
    
    return $FAILED
}

# Main execution
main() {
    echo "Podman Migration Validation Suite"
    echo "=================================="
    echo "Starting validation at $(date)"
    echo ""
    
    # Initialize log
    echo "Podman Migration Validation - $(date)" > "$LOG_FILE"
    
    # Run all tests
    test_environment
    test_configurations
    test_scripts
    test_network_security
    test_service_integration
    test_performance
    test_windows_wsl
    
    # Generate final report
    generate_report
    
    return $?
}

# Execute if run directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi