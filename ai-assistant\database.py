#!/usr/bin/env python3
"""
Database connection pool for AI Assistant System
"""

import logging
import mysql.connector
from mysql.connector import pooling
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

from config.config import config

logger = logging.getLogger(__name__)

class DatabasePool:
    """MySQL database connection pool"""
    
    def __init__(self):
        self.pool = None
        self.initialize_pool()
    
    def initialize_pool(self):
        """Initialize the connection pool"""
        try:
            self.pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name="ai_assistant_pool",
                pool_size=config.database.pool_size,
                pool_reset_session=True,
                host=config.database.host,
                user=config.database.user,
                password=config.database.password,
                database=config.database.database,
                charset='utf8mb4',
                collation='utf8mb4_unicode_ci',
                autocommit=False
            )
            logger.info(f"Database pool created with size {config.database.pool_size}")
        except mysql.connector.Error as e:
            logger.error(f"Failed to create database pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool with context manager"""
        connection = None
        try:
            connection = self.pool.get_connection()
            yield connection
            connection.commit()
        except mysql.connector.Error as e:
            if connection:
                connection.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute SELECT query and return results"""
        with self.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params or ())
            results = cursor.fetchall()
            cursor.close()
            return results
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute INSERT/UPDATE/DELETE and return affected rows"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params or ())
            affected_rows = cursor.rowcount
            cursor.close()
            return affected_rows
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """Execute INSERT and return last insert ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params or ())
            last_id = cursor.lastrowid
            cursor.close()
            return last_id
    
    def close_pool(self):
        """Close all connections in the pool"""
        if self.pool:
            logger.info("Closing database pool")
            # Note: MySQL connector doesn't have explicit pool closure

# Global database instance
db = DatabasePool()