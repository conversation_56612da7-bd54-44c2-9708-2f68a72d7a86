-- AI Assistant System Database Migration
-- Version: 1.0.0
-- Date: 2025-09-12

-- Knowledge Base table for storing Q&A and documentation
CREATE TABLE IF NOT EXISTS knowledge_base (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    keywords TEXT,
    embeddings JSON,
    source VARCHAR(255),
    source_id INT,
    confidence_score FLOAT DEFAULT 1.0,
    usage_count INT DEFAULT 0,
    last_used DATETIME,
    created_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_category (category),
    INDEX idx_usage (usage_count),
    INDEX idx_active (is_active),
    FULLTEXT idx_search (question, answer, keywords)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- AI Agent Activity Logs for tracking all AI operations
CREATE TABLE IF NOT EXISTS ai_agent_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    agent_type VARCHAR(50) NOT NULL,
    agent_version VARCHAR(20),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    user_id INT,
    input JSON,
    output JSON,
    tokens_used INT,
    response_time_ms INT,
    confidence FLOAT,
    status ENUM('success', 'failed', 'partial') DEFAULT 'success',
    error_message TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(100),
    correlation_id VARCHAR(100),
    INDEX idx_agent_type (agent_type),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp),
    INDEX idx_status (status),
    INDEX idx_user (user_id),
    INDEX idx_session (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Automation Workflows for defining triggered actions
CREATE TABLE IF NOT EXISTS automation_workflows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    trigger_type ENUM('webhook', 'schedule', 'event', 'manual', 'condition') NOT NULL,
    trigger_config JSON,
    conditions JSON,
    actions JSON,
    priority INT DEFAULT 5,
    max_retries INT DEFAULT 3,
    retry_delay_seconds INT DEFAULT 60,
    timeout_seconds INT DEFAULT 300,
    is_active BOOLEAN DEFAULT TRUE,
    last_run DATETIME,
    next_run DATETIME,
    run_count INT DEFAULT 0,
    success_count INT DEFAULT 0,
    failure_count INT DEFAULT 0,
    created_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_trigger (trigger_type),
    INDEX idx_next_run (next_run),
    INDEX idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Expense Processing Queue
CREATE TABLE IF NOT EXISTS expense_queue (
    id INT PRIMARY KEY AUTO_INCREMENT,
    receipt_path VARCHAR(500),
    user_id INT,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    extracted_data JSON,
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Ticket Response Templates
CREATE TABLE IF NOT EXISTS ticket_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100),
    template_name VARCHAR(200),
    template_body TEXT,
    placeholders JSON,
    usage_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;