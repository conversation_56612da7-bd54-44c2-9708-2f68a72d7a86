# AI Assistant Configuration Template
# Copy this to .env and fill in your values

# OpenRouter AI Configuration
OPENROUTER_API_KEY=sk-or-v1-your-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
AI_MODEL=anthropic/claude-3-opus  # or openai/gpt-4-turbo

# Database Configuration (Leantime MySQL)
LEAN_DB_HOST=mysql_leantime_podman
LEAN_DB_USER=lean
LEAN_DB_PASSWORD=your_password_here
LEAN_DB_DATABASE=leantime
DB_POOL_SIZE=10

# Redis Configuration (Task Queue)
REDIS_HOST=redis_podman
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=1

# ChromaDB Configuration (Vector Store)
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_COLLECTION=knowledge_base

# Security Configuration
MCP_SECRET_KEY=generate_a_secure_key_here
MCP_JWT_SECRET=generate_another_secure_key
JWT_ALGORITHM=HS256
JWT_EXPIRY_HOURS=24

# Server Configuration
AI_SERVER_HOST=0.0.0.0
AI_SERVER_PORT=8444
MCP_PUBLIC_PORT=8443
ALLOWED_CLIENTS=client1,client2  # Comma-separated list

# Business Configuration
TIMEZONE=Asia/Dubai
DEFAULT_CURRENCY=AED
UAE_VAT_RATE=5.0

# Feature Flags
ENABLE_EXPENSE_AUTOMATION=true
ENABLE_TICKET_AUTOMATION=true
ENABLE_KNOWLEDGE_BASE=true
ENABLE_PROJECT_AUTOMATION=true
ENABLE_ANALYTICS=true

# Automation Settings
AUTO_ORGANIZE=true
AUTO_ASSIGN_TASKS=true
AUTO_PRIORITIZE=true
AUTO_LEARN_FROM_TICKETS=true

# Monitoring
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
LOG_FILE=/app/logs/ai_assistant.log

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=AI Assistant <<EMAIL>>

# Slack/Teams Integration (optional)
SLACK_WEBHOOK_URL=
TEAMS_WEBHOOK_URL=

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Storage Paths
UPLOAD_PATH=/app/uploads
KNOWLEDGE_PATH=/app/knowledge
TEMP_PATH=/tmp