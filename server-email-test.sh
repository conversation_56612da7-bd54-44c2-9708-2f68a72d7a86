#!/bin/bash

# Password Reset Email Test Script
# Run this on your DigitalOcean server to test email functionality

set -e

echo "🔐 Password Reset Email Test"
echo "============================"
echo "📅 $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LEANTIME_URL="https://admin.dxbmeta.com"
TEST_EMAIL="<EMAIL>"
EMAIL_SERVICE_PORT="2525"

echo -e "${BLUE}🔍 Testing Email Configuration...${NC}"
echo ""

# Test 1: Check if email service is running
echo -e "${YELLOW}📋 Test 1: Email Service Status${NC}"
if podman ps | grep -q "email-service\|leantime-email-service"; then
    echo -e "${GREEN}✅ Email service container is running${NC}"
    
    # Get container name
    EMAIL_CONTAINER=$(podman ps --format "{{.Names}}" | grep -E "email-service|leantime-email-service" | head -1)
    echo "📦 Container: $EMAIL_CONTAINER"
else
    echo -e "${RED}❌ Email service container not found${NC}"
    echo "🔧 Starting email service..."
    podman-compose up -d email-service || echo "Failed to start email service"
fi
echo ""

# Test 2: Check email service health
echo -e "${YELLOW}📋 Test 2: Email Service Health${NC}"
if curl -f -s http://localhost:$EMAIL_SERVICE_PORT/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Email service health check passed${NC}"
    
    # Get health details
    HEALTH_RESPONSE=$(curl -s http://localhost:$EMAIL_SERVICE_PORT/health)
    echo "📊 Health Response: $HEALTH_RESPONSE"
else
    echo -e "${RED}❌ Email service health check failed${NC}"
    echo "🔍 Checking if port $EMAIL_SERVICE_PORT is open..."
    
    if netstat -tlnp 2>/dev/null | grep -q ":$EMAIL_SERVICE_PORT "; then
        echo -e "${YELLOW}⚠️  Port $EMAIL_SERVICE_PORT is open but service not responding${NC}"
    else
        echo -e "${RED}❌ Port $EMAIL_SERVICE_PORT is not open${NC}"
    fi
fi
echo ""

# Test 3: Test email sending via HTTP API
echo -e "${YELLOW}📋 Test 3: Direct Email API Test${NC}"
EMAIL_RESPONSE=$(curl -s -X POST http://localhost:$EMAIL_SERVICE_PORT/send \
    -H "Content-Type: application/json" \
    -d "{
        \"to\": \"$TEST_EMAIL\",
        \"subject\": \"Email Service Test - $(date '+%Y-%m-%d %H:%M:%S')\",
        \"html\": \"<h2>Email Service Test</h2><p>This is a test email to verify the email service is working correctly.</p><p><strong>Timestamp:</strong> $(date)</p><p><em>If you receive this email, the email service is functioning properly!</em></p>\"
    }" 2>/dev/null || echo "API call failed")

if echo "$EMAIL_RESPONSE" | grep -q "success.*true\|Email sent successfully"; then
    echo -e "${GREEN}✅ Email API test successful${NC}"
    echo "📧 Response: $EMAIL_RESPONSE"
    
    # Extract email ID if present
    EMAIL_ID=$(echo "$EMAIL_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$EMAIL_ID" ]; then
        echo "📬 Email ID: $EMAIL_ID"
    fi
else
    echo -e "${RED}❌ Email API test failed${NC}"
    echo "📄 Response: $EMAIL_RESPONSE"
fi
echo ""

# Test 4: Check Leantime connectivity
echo -e "${YELLOW}📋 Test 4: Leantime Connectivity${NC}"
if curl -f -s -I "$LEANTIME_URL" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Leantime is accessible${NC}"
    
    # Check if it's responding properly
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$LEANTIME_URL")
    echo "📊 HTTP Status: $HTTP_STATUS"
else
    echo -e "${RED}❌ Leantime is not accessible${NC}"
    echo "🔍 Checking Leantime container..."
    
    if podman ps | grep -q "leantime"; then
        echo -e "${YELLOW}⚠️  Leantime container is running but not responding${NC}"
    else
        echo -e "${RED}❌ Leantime container not found${NC}"
    fi
fi
echo ""

# Test 5: Check email service logs
echo -e "${YELLOW}📋 Test 5: Email Service Logs${NC}"
if [ ! -z "$EMAIL_CONTAINER" ]; then
    echo "📝 Recent email service logs:"
    echo "----------------------------------------"
    podman logs "$EMAIL_CONTAINER" --tail 10 2>/dev/null || echo "Could not retrieve logs"
    echo "----------------------------------------"
else
    echo -e "${RED}❌ No email container found to check logs${NC}"
fi
echo ""

# Test 6: Database connectivity (check if user exists)
echo -e "${YELLOW}📋 Test 6: Database User Check${NC}"
DB_RESULT=$(podman exec mysql_leantime_podman mysql -u lean -pJaNtSb3LQBpz5qQYC5uMsxmhfIsFpiop leantime -e "SELECT id, username, email, status FROM zp_user WHERE email='$TEST_EMAIL';" 2>/dev/null || echo "Database query failed")

if echo "$DB_RESULT" | grep -q "$TEST_EMAIL"; then
    echo -e "${GREEN}✅ User found in database${NC}"
    echo "👤 User details:"
    echo "$DB_RESULT"
else
    echo -e "${RED}❌ User not found in database${NC}"
    echo "📄 Query result: $DB_RESULT"
fi
echo ""

# Summary
echo -e "${BLUE}📊 Test Summary${NC}"
echo "==============="

# Count passed tests
TESTS_PASSED=0

if podman ps | grep -q "email-service\|leantime-email-service"; then
    echo -e "📦 Email Service Running: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "📦 Email Service Running: ${RED}❌ FAIL${NC}"
fi

if curl -f -s http://localhost:$EMAIL_SERVICE_PORT/health > /dev/null 2>&1; then
    echo -e "🏥 Email Service Health: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "🏥 Email Service Health: ${RED}❌ FAIL${NC}"
fi

if echo "$EMAIL_RESPONSE" | grep -q "success.*true\|Email sent successfully"; then
    echo -e "📧 Email API Test: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "📧 Email API Test: ${RED}❌ FAIL${NC}"
fi

if curl -f -s -I "$LEANTIME_URL" > /dev/null 2>&1; then
    echo -e "🌐 Leantime Access: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "🌐 Leantime Access: ${RED}❌ FAIL${NC}"
fi

if echo "$DB_RESULT" | grep -q "$TEST_EMAIL"; then
    echo -e "👤 User in Database: ${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "👤 User in Database: ${RED}❌ FAIL${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Overall Result: $TESTS_PASSED/5 tests passed${NC}"

if [ $TESTS_PASSED -ge 4 ]; then
    echo -e "${GREEN}🎉 Email system is working correctly!${NC}"
    echo ""
    echo -e "${BLUE}💡 Next Steps:${NC}"
    echo "1. Test password reset manually in Leantime"
    echo "2. Check $TEST_EMAIL for the test email sent"
    echo "3. Verify reset email arrives when requested"
elif [ $TESTS_PASSED -ge 2 ]; then
    echo -e "${YELLOW}⚠️  Email system partially working - needs attention${NC}"
    echo ""
    echo -e "${BLUE}🔧 Recommended Actions:${NC}"
    echo "1. Check failed tests above"
    echo "2. Restart email service if needed"
    echo "3. Verify container network connectivity"
else
    echo -e "${RED}❌ Email system has significant issues${NC}"
    echo ""
    echo -e "${BLUE}🚨 Critical Actions Needed:${NC}"
    echo "1. Check all container services are running"
    echo "2. Verify network configuration"
    echo "3. Check logs for detailed error messages"
fi

echo ""
echo -e "${BLUE}📞 For Support:${NC}"
echo "- Email service logs: podman logs $EMAIL_CONTAINER"
echo "- Leantime logs: podman logs mysql_leantime_podman"
echo "- Container status: podman ps"
echo ""
echo "✅ Test completed at $(date)"
