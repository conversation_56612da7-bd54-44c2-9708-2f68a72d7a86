#!/usr/bin/env node

/**
 * Resend API Email Service
 * Provides SMTP-like interface using Resend HTTP API
 * Solves container network connectivity issues
 */

const express = require('express');
const axios = require('axios');
const app = express();

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const RESEND_API_KEY = process.env.RESEND_API_KEY || 're_4V59PXub_GEiFvXAEp11e1Fudtcrk5Lw9';
const PORT = process.env.EMAIL_PORT || 2525;

console.log('🚀 Starting Resend API Email Service...');
console.log(`📧 Listening on port ${PORT}`);
console.log(`🔑 Using API key: ${RESEND_API_KEY.substring(0, 8)}...`);

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'resend-api-mailer',
        timestamp: new Date().toISOString()
    });
});

// SMTP-compatible email sending endpoint
app.post('/send', async (req, res) => {
    try {
        const { from, to, subject, html, text } = req.body;

        console.log(`📤 Sending email: ${subject}`);
        console.log(`   From: ${from}`);
        console.log(`   To: ${to}`);

        const emailData = {
            from: from || '<EMAIL>',
            to: Array.isArray(to) ? to : [to],
            subject: subject || 'No Subject',
            html: html || text || 'No content'
        };

        if (text && !html) {
            emailData.text = text;
            delete emailData.html;
        }

        const response = await axios.post('https://api.resend.com/emails', emailData, {
            headers: {
                'Authorization': `Bearer ${RESEND_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        console.log(`✅ Email sent successfully: ${response.data.id}`);

        res.json({
            success: true,
            messageId: response.data.id,
            message: 'Email sent successfully'
        });

    } catch (error) {
        console.error('❌ Email sending failed:', error.message);

        if (error.response) {
            console.error('   Response status:', error.response.status);
            console.error('   Response data:', error.response.data);
        }

        res.status(500).json({
            success: false,
            error: error.message,
            details: error.response?.data || null
        });
    }
});

// SMTP relay endpoint (for PHPMailer compatibility)
app.post('/relay', async (req, res) => {
    try {
        // Parse SMTP-like request
        const {
            fromAddress,
            fromName,
            toAddress,
            subject,
            body,
            isHtml = true
        } = req.body;

        const emailData = {
            from: fromName ? `${fromName} <${fromAddress}>` : fromAddress,
            to: [toAddress],
            subject: subject,
            [isHtml ? 'html' : 'text']: body
        };

        const response = await axios.post('https://api.resend.com/emails', emailData, {
            headers: {
                'Authorization': `Bearer ${RESEND_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        console.log(`✅ Relay email sent: ${response.data.id}`);
        res.json({ success: true, messageId: response.data.id });

    } catch (error) {
        console.error('❌ Relay email failed:', error.message);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Test endpoint
app.get('/test', async (req, res) => {
    try {
        const testEmail = {
            from: '<EMAIL>',
            to: ['<EMAIL>'],
            subject: 'Test from Resend API Service',
            html: '<p>This is a test email from the Resend API service.</p>'
        };

        const response = await axios.post('https://api.resend.com/emails', testEmail, {
            headers: {
                'Authorization': `Bearer ${RESEND_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        res.json({
            success: true,
            messageId: response.data.id,
            message: 'Test email sent successfully'
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🌐 Resend API Email Service running on http://0.0.0.0:${PORT}`);
    console.log(`🔧 Health check: http://localhost:${PORT}/health`);
    console.log(`📧 Send endpoint: http://localhost:${PORT}/send`);
    console.log(`🔄 Relay endpoint: http://localhost:${PORT}/relay`);
    console.log(`🧪 Test endpoint: http://localhost:${PORT}/test`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('📪 Shutting down Resend API Email Service...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('📪 Shutting down Resend API Email Service...');
    process.exit(0);
});