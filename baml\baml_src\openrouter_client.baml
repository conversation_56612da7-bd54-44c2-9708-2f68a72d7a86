// OpenRouter Client Configuration for Leantime
// Access to 100+ models including open-source options through unified API

// OpenRouter Base Configuration
client<llm> OpenRouterBase {
  provider openai  // OpenRouter uses OpenAI-compatible API
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    default_headers {
      "HTTP-Referer": "https://leantime.io",  // Your app URL
      "X-Title": "Leantime Project Management"  // App name for OpenRouter dashboard
    }
  }
}

// Fast & Free Open Source Models
client<llm> FastOSSClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "mistralai/mistral-7b-instruct:free"  // Free tier
    max_tokens 2048
    temperature 0.7
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// Production-Ready Open Source Model
client<llm> ProductionOSSClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "meta-llama/llama-3.1-70b-instruct"  // Powerful OSS model
    max_tokens 4096
    temperature 0.7
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// High-Performance Model (Mixtral)
client<llm> MixtralClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "mistralai/mixtral-8x7b-instruct"  // MoE architecture
    max_tokens 4096
    temperature 0.7
    top_p 0.9
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// Code-Optimized Model
client<llm> CodeOSSClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "deepseek/deepseek-coder-33b-instruct"  // Specialized for code
    max_tokens 4096
    temperature 0.5  // Lower temp for code generation
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// Small & Fast Model for Simple Tasks
client<llm> TinyOSSClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "microsoft/phi-3-mini-128k-instruct"  // Tiny but capable
    max_tokens 2048
    temperature 0.7
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// Math & Analysis Optimized
client<llm> AnalysisOSSClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "qwen/qwen-2.5-72b-instruct"  // Strong at analysis
    max_tokens 4096
    temperature 0.6
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// Creative Writing Model
client<llm> CreativeOSSClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "meta-llama/llama-3.1-8b-instruct"  // Good balance
    max_tokens 4096
    temperature 0.9  // Higher for creativity
    top_p 0.95
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
  }
}

// Smart Router - Let OpenRouter Choose Best Model
client<llm> SmartRouterClient {
  provider openai
  options {
    base_url "https://openrouter.ai/api/v1"
    api_key env.OPENROUTER_API_KEY
    model "openrouter/auto"  // Automatic model selection
    max_tokens 4096
    temperature 0.7
    default_headers {
      "HTTP-Referer": "https://leantime.io",
      "X-Title": "Leantime PM"
    }
    route_preferences {
      "prefer_free": true,  // Prefer free models when possible
      "max_cost": 0.01,     // Max cost per request in USD
      "latency": "low"      // Optimize for speed
    }
  }
}

// Fallback Chain with Multiple OSS Models
client<llm> LeantimeSmartClient {
  provider fallback
  options {
    strategy [
      ProductionOSSClient,   // Try Llama 70B first
      MixtralClient,          // Fallback to Mixtral
      FastOSSClient,          // Final fallback to free tier
      TinyOSSClient           // Emergency fallback
    ]
    retry_policy {
      max_retries 3
      retry_on ["RateLimitError", "TimeoutError", "ServiceUnavailable"]
      backoff_multiplier 2
    }
  }
}

// Model Selection by Task Type
enum TaskType {
  PROJECT_ANALYSIS
  TASK_BREAKDOWN  
  EFFORT_ESTIMATION
  CODE_GENERATION
  REPORT_WRITING
  DATA_ANALYSIS
  SIMPLE_QUERY
}

// Function to select appropriate model
function SelectModelForTask(
  taskType: TaskType,
  complexity: "simple" | "medium" | "complex"
) -> string {
  client TinyOSSClient
  
  prompt #"
    Select the best model for:
    Task Type: {{ taskType }}
    Complexity: {{ complexity }}
    
    Available models:
    - FastOSSClient: Free, fast, good for simple tasks
    - TinyOSSClient: Very fast, limited context
    - ProductionOSSClient: Powerful, comprehensive
    - MixtralClient: Balanced performance
    - CodeOSSClient: Code-specific tasks
    - AnalysisOSSClient: Data and math
    - CreativeOSSClient: Reports and writing
    
    Return the model name only.
  "#
}