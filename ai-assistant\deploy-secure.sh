#!/bin/bash
# Secure deployment script for AI Assistant with hardening

set -e

echo "🔒 Deploying Secure AI Assistant..."

# Configuration
CONTAINER_NAME="ai-assistant-secure"
IMAGE_NAME="ai-assistant:secure"
NETWORK_NAME="ai-network"
PORT="8444"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to check if container exists
container_exists() {
    podman container exists "$1" 2>/dev/null
}

# Function to check if network exists
network_exists() {
    podman network exists "$1" 2>/dev/null
}

echo -e "${YELLOW}Step 1: Building secure container image...${NC}"
podman build -f Dockerfile.secure -t ${IMAGE_NAME} .

echo -e "${YELLOW}Step 2: Creating isolated network...${NC}"
if ! network_exists ${NETWORK_NAME}; then
    podman network create ${NETWORK_NAME} \
        --subnet **********/16 \
        --gateway **********
    echo -e "${GREEN}✓ Network created${NC}"
else
    echo -e "${GREEN}✓ Network already exists${NC}"
fi

echo -e "${YELLOW}Step 3: Stopping existing container if running...${NC}"
if container_exists ${CONTAINER_NAME}; then
    podman stop ${CONTAINER_NAME} 2>/dev/null || true
    podman rm ${CONTAINER_NAME} 2>/dev/null || true
    echo -e "${GREEN}✓ Existing container removed${NC}"
fi

echo -e "${YELLOW}Step 4: Starting secure container...${NC}"
podman run -d \
    --name ${CONTAINER_NAME} \
    --network ${NETWORK_NAME} \
    --ip **********0 \
    -p 127.0.0.1:${PORT}:${PORT} \
    --read-only \
    --tmpfs /tmp:rw,noexec,nosuid,size=100m \
    --tmpfs /app/logs:rw,noexec,nosuid,size=50m \
    --cap-drop ALL \
    --cap-add NET_BIND_SERVICE \
    --security-opt no-new-privileges \
    --memory 2g \
    --memory-swap 2g \
    --cpus 2 \
    --restart unless-stopped \
    --env-file .env \
    ${IMAGE_NAME}

echo -e "${YELLOW}Step 5: Waiting for container to be healthy...${NC}"
MAX_ATTEMPTS=30
ATTEMPT=0

while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
    if podman healthcheck run ${CONTAINER_NAME} 2>/dev/null; then
        echo -e "${GREEN}✓ Container is healthy${NC}"
        break
    fi
    echo -n "."
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done

if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
    echo -e "${RED}✗ Container failed to become healthy${NC}"
    echo "Container logs:"
    podman logs ${CONTAINER_NAME}
    exit 1
fi

echo -e "${YELLOW}Step 6: Running security checks...${NC}"

# Check that port is only bound to localhost
PORT_CHECK=$(ss -tlpn | grep ":${PORT}" | grep -c "127.0.0.1:${PORT}" || true)
if [ "$PORT_CHECK" -eq "1" ]; then
    echo -e "${GREEN}✓ Port bound to localhost only${NC}"
else
    echo -e "${RED}✗ Port not properly restricted${NC}"
fi

# Check container capabilities
CAPS=$(podman inspect ${CONTAINER_NAME} | jq -r '.[0].EffectiveCaps[]' | wc -l)
if [ "$CAPS" -le "2" ]; then
    echo -e "${GREEN}✓ Minimal capabilities (${CAPS})${NC}"
else
    echo -e "${YELLOW}⚠ Multiple capabilities (${CAPS})${NC}"
fi

# Check read-only filesystem
READONLY=$(podman inspect ${CONTAINER_NAME} | jq -r '.[0].HostConfig.ReadonlyRootfs')
if [ "$READONLY" == "true" ]; then
    echo -e "${GREEN}✓ Read-only root filesystem${NC}"
else
    echo -e "${RED}✗ Filesystem is writable${NC}"
fi

# Check running as non-root
USER_ID=$(podman exec ${CONTAINER_NAME} id -u)
if [ "$USER_ID" != "0" ]; then
    echo -e "${GREEN}✓ Running as non-root (UID: ${USER_ID})${NC}"
else
    echo -e "${RED}✗ Running as root${NC}"
fi

echo -e "${YELLOW}Step 7: Setting up nginx reverse proxy...${NC}"
cat > /tmp/ai-assistant-nginx.conf << 'EOF'
upstream ai_assistant {
    server 127.0.0.1:8444;
    keepalive 32;
}

server {
    listen 443 ssl http2;
    server_name ai.dxbmeta.com;
    
    # SSL configuration (update paths)
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "default-src 'self'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=ai_limit:10m rate=10r/s;
    limit_req zone=ai_limit burst=20 nodelay;
    
    location / {
        proxy_pass http://ai_assistant;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Block sensitive paths
    location ~ /\. {
        deny all;
    }
}
EOF

echo -e "${GREEN}Nginx configuration saved to /tmp/ai-assistant-nginx.conf${NC}"

echo -e "${YELLOW}Step 8: Container information...${NC}"
podman inspect ${CONTAINER_NAME} | jq '{
    Name: .[0].Name,
    State: .[0].State.Status,
    Network: .[0].NetworkSettings.Networks | keys[0],
    IP: .[0].NetworkSettings.IPAddress,
    Ports: .[0].NetworkSettings.Ports,
    User: .[0].Config.User,
    ReadOnly: .[0].HostConfig.ReadonlyRootfs,
    Memory: .[0].HostConfig.Memory
}'

echo -e "${GREEN}✅ Secure deployment complete!${NC}"
echo ""
echo "Access points:"
echo "  - Local API: http://127.0.0.1:${PORT}"
echo "  - Health: curl http://127.0.0.1:${PORT}/health"
echo "  - Login: curl -X POST http://127.0.0.1:${PORT}/auth/login -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"admin123\"}'"
echo ""
echo "Security features enabled:"
echo "  ✓ JWT Authentication required"
echo "  ✓ Rate limiting active"
echo "  ✓ Bound to localhost only"
echo "  ✓ Network isolation"
echo "  ✓ Read-only filesystem"
echo "  ✓ Dropped capabilities"
echo "  ✓ Non-root user"
echo "  ✓ Memory limits"
echo ""
echo "Monitor logs: podman logs -f ${CONTAINER_NAME}"