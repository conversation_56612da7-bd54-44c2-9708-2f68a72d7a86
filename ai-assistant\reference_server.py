#!/usr/bin/env python3
"""
Enhanced Leantime MCP Server - Complete Business Management Suite
Includes all business modules: Expense Management, Support Tickets, CRM, Invoicing, and more
Uses OpenRouter API key: 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
"""

import json
import os
import logging
import asyncio
import hashlib
import jwt
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from contextlib import asynccontextmanager
import sys

# MCP Server implementation
from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.types import (
    Resource, Tool, Prompt, TextContent, ImageContent,
    ListResourcesRequest, ListToolsRequest, ListPromptsRequest,
    ReadResourceRequest, CallToolRequest, GetPromptRequest,
    ListResourcesResult, ListToolsResult, ListPromptsResult,
    ReadResourceResult, CallToolResult, GetPromptResult,
    McpError, ErrorCode
)

# Database and API
import mysql.connector
from mysql.connector import pooling
import aiohttp
from cryptography.fernet import Fernet
import pytz

# Import business modules
from modules.expense_manager import ExpenseManager
from modules.ticket_system import SupportTicketSystem
from modules.crm_module import CRMModule
from modules.invoice_generator import InvoiceGenerator

# Setup logging (stderr only for MCP compliance)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

class EnhancedLeantimeMCPConfig:
    """Enhanced configuration with business management features"""
    
    # Security
    SECRET_KEY = os.getenv("MCP_SECRET_KEY", Fernet.generate_key().decode())
    JWT_SECRET = os.getenv("MCP_JWT_SECRET", str(uuid.uuid4()))
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRY_HOURS = 24
    
    # OpenRouter AI - Using provided key
    OPENROUTER_API_KEY = "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"
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # Database
    DB_HOST = os.getenv("LEAN_DB_HOST", "mysql_leantime")
    DB_USER = os.getenv("LEAN_DB_USER", "lean")
    DB_PASSWORD = os.getenv("LEAN_DB_PASSWORD")
    DB_DATABASE = os.getenv("LEAN_DB_DATABASE", "leantime")
    DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "15"))
    
    # Server
    MCP_SERVER_NAME = "leantime-business-suite"
    MCP_SERVER_VERSION = "2.0.0"
    
    # Business features
    BUSINESS_MODULES_ENABLED = {
        'expenses': True,
        'support_tickets': True,
        'crm': True,
        'invoicing': True,
        'inventory': False,  # To be implemented
        'accounting': False  # To be implemented
    }
    
    # Dubai/UAE settings
    TIMEZONE = "Asia/Dubai"
    DEFAULT_CURRENCY = "AED"
    UAE_VAT_RATE = 5.0
    
    # Allowed clients
    ALLOWED_CLIENTS = os.getenv("MCP_ALLOWED_CLIENTS", "").split(",")
    
    # Self-organizing features
    AUTO_ORGANIZE = os.getenv("MCP_AUTO_ORGANIZE", "true").lower() == "true"
    AUTO_ASSIGN_TASKS = os.getenv("MCP_AUTO_ASSIGN", "true").lower() == "true"
    AUTO_PRIORITIZE = os.getenv("MCP_AUTO_PRIORITIZE", "true").lower() == "true"

class EnhancedSecurityManager:
    """Enhanced security with financial data protection"""
    
    def __init__(self):
        self.cipher = Fernet(EnhancedLeantimeMCPConfig.SECRET_KEY.encode())
        self.client_sessions = {}
        self.financial_access_log = []
    
    def generate_client_token(self, client_id: str, metadata: Dict) -> str:
        """Generate JWT token with enhanced permissions"""
        payload = {
            "client_id": client_id,
            "metadata": metadata,
            "permissions": self._get_client_permissions(client_id),
            "exp": datetime.utcnow() + timedelta(hours=EnhancedLeantimeMCPConfig.JWT_EXPIRY_HOURS),
            "iat": datetime.utcnow(),
            "jti": str(uuid.uuid4())
        }
        return jwt.encode(payload, EnhancedLeantimeMCPConfig.JWT_SECRET, 
                         algorithm=EnhancedLeantimeMCPConfig.JWT_ALGORITHM)
    
    def _get_client_permissions(self, client_id: str) -> List[str]:
        """Define permissions based on client"""
        # In production, this would be more sophisticated
        return [
            "view_projects", "edit_tasks", "view_expenses", "create_expenses",
            "view_tickets", "create_tickets", "view_customers", "edit_customers",
            "view_invoices", "create_invoices", "process_payments"
        ]
    
    def log_financial_access(self, client_id: str, action: str, data_type: str, record_id: str):
        """Log all access to financial data"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "client_id": client_id,
            "action": action,
            "data_type": data_type,
            "record_id": record_id,
            "ip_address": "local"  # In production, capture real IP
        }
        self.financial_access_log.append(log_entry)
        logger.info(f"FINANCIAL_ACCESS: {json.dumps(log_entry)}")

class EnhancedDatabaseManager:
    """Enhanced database manager with business module support"""
    
    def __init__(self):
        self.pool = None
        self.init_pool()
        self.timezone = pytz.timezone(EnhancedLeantimeMCPConfig.TIMEZONE)
    
    def init_pool(self):
        """Initialize connection pool"""
        try:
            self.pool = pooling.MySQLConnectionPool(
                pool_name="leantime_business_pool",
                pool_size=EnhancedLeantimeMCPConfig.DB_POOL_SIZE,
                host=EnhancedLeantimeMCPConfig.DB_HOST,
                user=EnhancedLeantimeMCPConfig.DB_USER,
                password=EnhancedLeantimeMCPConfig.DB_PASSWORD,
                database=EnhancedLeantimeMCPConfig.DB_DATABASE
            )
            logger.info("Enhanced database pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
    
    def get_connection(self):
        """Get connection from pool"""
        if self.pool:
            return self.pool.get_connection()
        return None
    
    def get_dubai_time(self) -> datetime:
        """Get current Dubai time"""
        return datetime.now(self.timezone)
    
    async def run_migration(self, migration_file: str):
        """Run database migration for business tables"""
        try:
            with open(migration_file, 'r') as f:
                migration_sql = f.read()
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Split and execute each statement
            statements = migration_sql.split(';')
            for statement in statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    cursor.execute(statement)
            
            conn.commit()
            logger.info(f"Migration {migration_file} completed successfully")
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            if conn:
                conn.rollback()
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

class EnhancedLeantimeMCPServer:
    """Enhanced MCP Server with complete business management suite"""
    
    def __init__(self):
        self.server = Server(EnhancedLeantimeMCPConfig.MCP_SERVER_NAME)
        self.security = EnhancedSecurityManager()
        self.db = EnhancedDatabaseManager()
        self.active_clients = {}
        
        # Initialize business modules
        self.expense_manager = ExpenseManager(self.db.pool, EnhancedLeantimeMCPConfig.OPENROUTER_API_KEY)
        self.ticket_system = SupportTicketSystem(self.db.pool, EnhancedLeantimeMCPConfig.OPENROUTER_API_KEY)
        self.crm_module = CRMModule(self.db.pool, EnhancedLeantimeMCPConfig.OPENROUTER_API_KEY)
        self.invoice_generator = InvoiceGenerator(self.db.pool, EnhancedLeantimeMCPConfig.OPENROUTER_API_KEY)
        
        # Register all handlers
        self._register_handlers()
        
        logger.info("Enhanced Leantime MCP Server initialized with business suite")
    
    def _register_handlers(self):
        """Register all MCP handlers including business modules"""
        
        # =====================
        # RESOURCES
        # =====================
        @self.server.list_resources()
        async def handle_list_resources() -> list[Resource]:
            """List all available resources including business data"""
            resources = [
                # Core Leantime resources
                Resource(uri="leantime://projects", name="Projects", description="All projects in Leantime"),
                Resource(uri="leantime://tasks", name="Tasks", description="All tasks across projects"),
                Resource(uri="leantime://team", name="Team Members", description="Team member information"),
                Resource(uri="leantime://reports", name="Reports", description="Project reports and analytics"),
                
                # Business management resources
                Resource(uri="business://expenses", name="Expenses", description="Expense tracking and management"),
                Resource(uri="business://tickets", name="Support Tickets", description="Customer support tickets"),
                Resource(uri="business://customers", name="Customers", description="CRM and customer data"),
                Resource(uri="business://invoices", name="Invoices", description="Invoicing and billing"),
                Resource(uri="business://payments", name="Payments", description="Payment tracking"),
                Resource(uri="business://financials", name="Financial Reports", description="Financial analytics and reports"),
                Resource(uri="business://pipeline", name="Sales Pipeline", description="Sales pipeline and opportunities"),
                Resource(uri="business://metrics", name="Business Metrics", description="KPIs and business metrics")
            ]
            return resources
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Read resource content with business data integration"""
            
            # Core Leantime resources
            if uri == "leantime://projects":
                projects = self.db.execute_query("SELECT id, name, state, start, end FROM zp_projects WHERE active = 1")
                return json.dumps(projects, default=str)
            
            elif uri == "leantime://tasks":
                tasks = self.db.execute_query("""
                    SELECT t.id, t.headline, t.status, t.priority, p.name as project_name 
                    FROM zp_tickets t 
                    JOIN zp_projects p ON t.projectId = p.id 
                    WHERE t.type = 'task'
                    ORDER BY t.priority DESC
                """)
                return json.dumps(tasks, default=str)
            
            # Business resources
            elif uri == "business://expenses":
                expenses = await self.expense_manager.get_expenses({'limit': 100})
                analytics = await self.expense_manager.get_expense_analytics()
                return json.dumps({
                    "recent_expenses": expenses[:20],
                    "analytics": analytics,
                    "currency": "AED",
                    "timezone": "Asia/Dubai"
                }, default=str)
            
            elif uri == "business://tickets":
                tickets = await self.ticket_system.get_tickets({'limit': 50})
                metrics = await self.ticket_system.generate_support_metrics()
                return json.dumps({
                    "active_tickets": tickets,
                    "performance_metrics": metrics
                }, default=str)
            
            elif uri == "business://customers":
                customers = await self.crm_module.get_customers({'limit': 50})
                pipeline = await self.crm_module.get_customer_pipeline()
                return json.dumps({
                    "customers": customers,
                    "pipeline": pipeline
                }, default=str)
            
            elif uri == "business://invoices":
                # Get recent invoices
                conn = self.db.get_connection()
                cursor = conn.cursor(dictionary=True)
                try:
                    query = """
                        SELECT i.*, c.company_name as customer_name
                        FROM mcp_invoices i
                        LEFT JOIN mcp_customers c ON i.customer_id = c.id
                        ORDER BY i.created_at DESC
                        LIMIT 50
                    """
                    cursor.execute(query)
                    invoices = cursor.fetchall()
                    
                    # Get financial report
                    financial_report = await self.invoice_generator.generate_financial_report()
                    
                    return json.dumps({
                        "recent_invoices": invoices,
                        "financial_summary": financial_report
                    }, default=str)
                finally:
                    cursor.close()
                    conn.close()
            
            elif uri == "business://metrics":
                # Comprehensive business metrics
                dubai_now = self.db.get_dubai_time()
                
                metrics = {
                    "generated_at": dubai_now.isoformat(),
                    "timezone": "Asia/Dubai",
                    "currency": "AED",
                    "expense_summary": await self.expense_manager.get_expense_analytics(),
                    "support_metrics": await self.ticket_system.generate_support_metrics(),
                    "financial_summary": await self.invoice_generator.generate_financial_report(),
                    "crm_pipeline": await self.crm_module.get_customer_pipeline()
                }
                
                return json.dumps(metrics, default=str)
            
            else:
                raise ValueError(f"Unknown resource: {uri}")
        
        # =====================
        # TOOLS
        # =====================
        @self.server.list_tools()
        async def handle_list_tools() -> list[Tool]:
            """List all available tools including business operations"""
            tools = [
                # Core project management tools
                Tool(
                    name="create_task",
                    description="Create a new task in a project",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "project_id": {"type": "integer"},
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]},
                            "assignee_id": {"type": "integer", "optional": True}
                        },
                        "required": ["project_id", "title"]
                    }
                ),
                
                # Expense management tools
                Tool(
                    name="track_expense",
                    description="Track a new business expense with AI categorization",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "amount": {"type": "number"},
                            "currency": {"type": "string", "default": "AED"},
                            "category": {"type": "string", "optional": True},
                            "project_id": {"type": "integer", "optional": True},
                            "expense_date": {"type": "string", "optional": True},
                            "receipt_data": {"type": "string", "optional": True}
                        },
                        "required": ["title", "amount"]
                    }
                ),
                
                Tool(
                    name="approve_expense",
                    description="Approve or reject an expense",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "expense_id": {"type": "string"},
                            "approved": {"type": "boolean"},
                            "notes": {"type": "string", "optional": True}
                        },
                        "required": ["expense_id", "approved"]
                    }
                ),
                
                # Support ticket tools
                Tool(
                    name="create_ticket",
                    description="Create a new support ticket with AI triage",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "subject": {"type": "string"},
                            "description": {"type": "string"},
                            "reporter_email": {"type": "string"},
                            "reporter_name": {"type": "string"},
                            "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"], "optional": True},
                            "category": {"type": "string", "optional": True}
                        },
                        "required": ["subject", "description", "reporter_email", "reporter_name"]
                    }
                ),
                
                Tool(
                    name="update_ticket_status",
                    description="Update support ticket status",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "ticket_id": {"type": "string"},
                            "status": {"type": "string", "enum": ["open", "in_progress", "pending_customer", "resolved", "closed"]},
                            "notes": {"type": "string", "optional": True}
                        },
                        "required": ["ticket_id", "status"]
                    }
                ),
                
                # CRM tools
                Tool(
                    name="add_customer",
                    description="Add new customer to CRM with AI insights",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "company_name": {"type": "string"},
                            "primary_contact_name": {"type": "string"},
                            "primary_contact_email": {"type": "string"},
                            "industry": {"type": "string", "optional": True},
                            "company_size": {"type": "string", "enum": ["solo", "small", "medium", "large", "enterprise"], "optional": True},
                            "website": {"type": "string", "optional": True},
                            "notes": {"type": "string", "optional": True}
                        },
                        "required": ["company_name", "primary_contact_name", "primary_contact_email"]
                    }
                ),
                
                Tool(
                    name="log_interaction",
                    description="Log customer interaction",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "customer_id": {"type": "string"},
                            "interaction_type": {"type": "string", "enum": ["email", "call", "meeting", "demo", "support", "sales"]},
                            "subject": {"type": "string"},
                            "description": {"type": "string"},
                            "outcome": {"type": "string", "optional": True},
                            "follow_up_required": {"type": "boolean", "optional": True}
                        },
                        "required": ["customer_id", "interaction_type", "subject"]
                    }
                ),
                
                # Invoicing tools
                Tool(
                    name="create_invoice",
                    description="Create new invoice with UAE VAT calculation",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "customer_id": {"type": "integer"},
                            "title": {"type": "string"},
                            "description": {"type": "string", "optional": True},
                            "line_items": {"type": "array"},
                            "project_id": {"type": "integer", "optional": True},
                            "payment_terms": {"type": "integer", "default": 30}
                        },
                        "required": ["customer_id", "title", "line_items"]
                    }
                ),
                
                Tool(
                    name="process_payment",
                    description="Process payment against invoice",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "invoice_id": {"type": "string"},
                            "amount": {"type": "number"},
                            "payment_method": {"type": "string"},
                            "reference_number": {"type": "string", "optional": True},
                            "notes": {"type": "string", "optional": True}
                        },
                        "required": ["invoice_id", "amount", "payment_method"]
                    }
                ),
                
                # Analytical tools
                Tool(
                    name="generate_business_report",
                    description="Generate comprehensive business analytics report",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "report_type": {"type": "string", "enum": ["financial", "expenses", "support", "crm", "comprehensive"]},
                            "period": {"type": "string", "enum": ["week", "month", "quarter", "year"], "default": "month"},
                            "format": {"type": "string", "enum": ["json", "summary"], "default": "summary"}
                        },
                        "required": ["report_type"]
                    }
                ),
                
                Tool(
                    name="ai_business_insights",
                    description="Get AI-powered business insights and recommendations",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "focus_area": {"type": "string", "enum": ["expenses", "revenue", "customers", "support", "overall"]},
                            "timeframe": {"type": "string", "enum": ["week", "month", "quarter"], "default": "month"}
                        },
                        "required": ["focus_area"]
                    }
                )
            ]
            return tools
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict) -> list:
            """Execute business tools with enhanced capabilities"""
            
            # Log financial operations
            if name in ["track_expense", "process_payment", "create_invoice"]:
                self.security.log_financial_access("mcp_client", f"tool_{name}", name, str(arguments))
            
            try:
                # Expense management tools
                if name == "track_expense":
                    expense_data = {
                        "title": arguments["title"],
                        "description": arguments.get("description", ""),
                        "amount": arguments["amount"],
                        "currency": arguments.get("currency", "AED"),
                        "category": arguments.get("category"),
                        "project_id": arguments.get("project_id"),
                        "expense_date": arguments.get("expense_date"),
                        "user_id": 1,  # In production, get from JWT token
                        "created_by": 1
                    }
                    
                    expense_id = await self.expense_manager.create_expense(expense_data)
                    
                    return [TextContent(
                        type="text",
                        text=f"✅ Expense tracked successfully!\n\n**ID:** {expense_id}\n**Amount:** {arguments['amount']} AED\n**Status:** Ready for approval\n\n*AI categorization applied automatically*"
                    )]
                
                elif name == "approve_expense":
                    if arguments["approved"]:
                        success = await self.expense_manager.approve_expense(
                            arguments["expense_id"], 
                            1,  # approver_id 
                            arguments.get("notes", "")
                        )
                        status = "approved" if success else "failed"
                    else:
                        # In full implementation, add reject functionality
                        status = "rejected"
                    
                    return [TextContent(
                        type="text",
                        text=f"💼 Expense {arguments['expense_id']} has been **{status}**"
                    )]
                
                # Support ticket tools  
                elif name == "create_ticket":
                    ticket_data = {
                        "subject": arguments["subject"],
                        "description": arguments["description"],
                        "reporter_email": arguments["reporter_email"],
                        "reporter_name": arguments["reporter_name"],
                        "priority": arguments.get("priority"),
                        "category": arguments.get("category"),
                        "source": "api"
                    }
                    
                    ticket_id = await self.ticket_system.create_ticket(ticket_data)
                    
                    return [TextContent(
                        type="text",
                        text=f"🎫 Support ticket created successfully!\n\n**ID:** {ticket_id}\n**Subject:** {arguments['subject']}\n**Priority:** {ticket_data.get('priority', 'Medium')}\n\n*AI triage applied - auto-assigned to appropriate team member*"
                    )]
                
                elif name == "update_ticket_status":
                    success = await self.ticket_system.update_ticket_status(
                        arguments["ticket_id"],
                        arguments["status"],
                        1,  # user_id
                        arguments.get("notes", "")
                    )
                    
                    return [TextContent(
                        type="text",
                        text=f"🎫 Ticket {arguments['ticket_id']} status updated to **{arguments['status']}**"
                    )]
                
                # CRM tools
                elif name == "add_customer":
                    customer_data = {
                        "company_name": arguments["company_name"],
                        "primary_contact_name": arguments["primary_contact_name"],
                        "primary_contact_email": arguments["primary_contact_email"],
                        "industry": arguments.get("industry"),
                        "company_size": arguments.get("company_size"),
                        "website": arguments.get("website"),
                        "notes": arguments.get("notes", ""),
                        "customer_type": "prospect"
                    }
                    
                    customer_id = await self.crm_module.create_customer(customer_data)
                    
                    return [TextContent(
                        type="text",
                        text=f"🏢 Customer added successfully!\n\n**ID:** {customer_id}\n**Company:** {arguments['company_name']}\n**Contact:** {arguments['primary_contact_name']}\n\n*AI lead scoring applied automatically*"
                    )]
                
                elif name == "log_interaction":
                    interaction_data = {
                        "customer_id": arguments["customer_id"],
                        "user_id": 1,  # From JWT in production
                        "interaction_type": arguments["interaction_type"],
                        "subject": arguments["subject"],
                        "description": arguments.get("description", ""),
                        "outcome": arguments.get("outcome", ""),
                        "follow_up_required": arguments.get("follow_up_required", False)
                    }
                    
                    interaction_id = await self.crm_module.log_interaction(interaction_data)
                    
                    return [TextContent(
                        type="text",
                        text=f"📝 Customer interaction logged successfully!\n\n**ID:** {interaction_id}\n**Type:** {arguments['interaction_type']}\n**Subject:** {arguments['subject']}"
                    )]
                
                # Invoicing tools
                elif name == "create_invoice":
                    invoice_data = {
                        "customer_id": arguments["customer_id"],
                        "title": arguments["title"],
                        "description": arguments.get("description", ""),
                        "line_items": arguments["line_items"],
                        "project_id": arguments.get("project_id"),
                        "payment_terms": arguments.get("payment_terms", 30)
                    }
                    
                    invoice_id = await self.invoice_generator.create_invoice(invoice_data)
                    
                    return [TextContent(
                        type="text",
                        text=f"📄 Invoice created successfully!\n\n**ID:** {invoice_id}\n**Title:** {arguments['title']}\n**Payment Terms:** {invoice_data['payment_terms']} days\n\n*UAE VAT (5%) calculated automatically*"
                    )]
                
                elif name == "process_payment":
                    payment_data = {
                        "invoice_id": arguments["invoice_id"],
                        "amount": arguments["amount"],
                        "payment_method": arguments["payment_method"],
                        "reference_number": arguments.get("reference_number"),
                        "notes": arguments.get("notes", ""),
                        "status": "completed"
                    }
                    
                    payment_id = await self.invoice_generator.process_payment(payment_data)
                    
                    return [TextContent(
                        type="text",
                        text=f"💰 Payment processed successfully!\n\n**Payment ID:** {payment_id}\n**Amount:** {arguments['amount']} AED\n**Method:** {arguments['payment_method']}\n\n*Invoice status updated automatically*"
                    )]
                
                # Analytical tools
                elif name == "generate_business_report":
                    report_type = arguments["report_type"]
                    period = arguments.get("period", "month")
                    
                    if report_type == "financial":
                        report = await self.invoice_generator.generate_financial_report(period)
                    elif report_type == "expenses":
                        report = await self.expense_manager.generate_expense_report({"period": period})
                    elif report_type == "support":
                        report = await self.ticket_system.generate_support_metrics(period)
                    elif report_type == "crm":
                        report = await self.crm_module.get_customer_pipeline()
                    else:  # comprehensive
                        report = {
                            "financial": await self.invoice_generator.generate_financial_report(period),
                            "expenses": await self.expense_manager.get_expense_analytics(period),
                            "support": await self.ticket_system.generate_support_metrics(period),
                            "crm": await self.crm_module.get_customer_pipeline(),
                            "generated_at": self.db.get_dubai_time().isoformat()
                        }
                    
                    if arguments.get("format") == "json":
                        return [TextContent(type="text", text=json.dumps(report, indent=2, default=str))]
                    else:
                        # Format as readable summary
                        if report_type == "financial":
                            summary = f"""
📊 **Financial Report ({period})**

💰 Total Invoiced: {report['summary']['total_invoiced']:,.2f} AED
💵 Total Collected: {report['summary']['total_collected']:,.2f} AED
📈 Collection Rate: {report['summary']['collection_rate']:.1f}%
⏰ Avg Payment Time: {report['summary']['avg_payment_days']:.1f} days
🔴 Overdue Amount: {report['summary']['overdue_amount']:,.2f} AED

*Generated: {report['generated_at']}*
                            """
                        else:
                            summary = f"📋 **{report_type.title()} Report**\n\n" + json.dumps(report, indent=2, default=str)[:1000] + "..."
                    
                    return [TextContent(type="text", text=summary.strip())]
                
                elif name == "ai_business_insights":
                    focus_area = arguments["focus_area"]
                    timeframe = arguments.get("timeframe", "month")
                    
                    # Get relevant data based on focus area
                    if focus_area == "expenses":
                        data = await self.expense_manager.get_expense_analytics(timeframe)
                        context = f"Expense data for {timeframe}: {json.dumps(data, default=str)}"
                    elif focus_area == "revenue":
                        data = await self.invoice_generator.generate_financial_report(timeframe)
                        context = f"Revenue data for {timeframe}: {json.dumps(data, default=str)}"
                    else:
                        # Get comprehensive data
                        context = f"Business overview for {timeframe}"
                    
                    # Generate AI insights
                    insights_prompt = f"""
                    Analyze this Dubai business data and provide actionable insights:
                    
                    {context}
                    
                    Focus on {focus_area} optimization for a {timeframe} timeframe.
                    Consider UAE business culture and practices.
                    
                    Provide:
                    1. Key insights (3-5 bullet points)
                    2. Recommended actions
                    3. Risk factors to monitor
                    4. Opportunities for improvement
                    
                    Keep response concise and actionable.
                    """
                    
                    # Call AI for insights
                    try:
                        async with aiohttp.ClientSession() as session:
                            headers = {
                                "Authorization": f"Bearer {EnhancedLeantimeMCPConfig.OPENROUTER_API_KEY}",
                                "Content-Type": "application/json"
                            }
                            
                            payload = {
                                "model": "mistralai/mistral-7b-instruct:free",
                                "messages": [{"role": "user", "content": insights_prompt}],
                                "max_tokens": 1024,
                                "temperature": 0.7
                            }
                            
                            async with session.post(
                                f"{EnhancedLeantimeMCPConfig.OPENROUTER_BASE_URL}/chat/completions",
                                headers=headers,
                                json=payload
                            ) as response:
                                if response.status == 200:
                                    result = await response.json()
                                    ai_insights = result["choices"][0]["message"]["content"]
                                else:
                                    ai_insights = "AI insights temporarily unavailable"
                    except:
                        ai_insights = "AI insights temporarily unavailable"
                    
                    return [TextContent(
                        type="text", 
                        text=f"🤖 **AI Business Insights - {focus_area.title()}**\n\n{ai_insights}\n\n*Analysis based on {timeframe} data from Dubai timezone*"
                    )]
                
                else:
                    return [TextContent(type="text", text=f"Unknown tool: {name}")]
            
            except Exception as e:
                logger.error(f"Tool execution failed for {name}: {e}")
                return [TextContent(type="text", text=f"❌ Error executing {name}: {str(e)}")]
        
        # =====================
        # PROMPTS
        # =====================
        @self.server.list_prompts()
        async def handle_list_prompts() -> list[Prompt]:
            """List business-focused prompts"""
            return [
                Prompt(
                    name="business_dashboard",
                    description="Generate comprehensive business dashboard overview",
                    arguments=[
                        {"name": "focus_area", "description": "Business area to focus on", "required": False}
                    ]
                ),
                Prompt(
                    name="expense_analysis",
                    description="Analyze expense patterns and suggest optimizations",
                    arguments=[
                        {"name": "period", "description": "Analysis period", "required": False}
                    ]
                ),
                Prompt(
                    name="customer_health_check",
                    description="Assess customer relationship health and risks",
                    arguments=[
                        {"name": "customer_id", "description": "Specific customer to analyze", "required": False}
                    ]
                ),
                Prompt(
                    name="support_triage",
                    description="Prioritize and organize support tickets",
                    arguments=[]
                ),
                Prompt(
                    name="financial_forecast",
                    description="Generate financial forecasts and cash flow predictions",
                    arguments=[
                        {"name": "horizon", "description": "Forecast horizon in months", "required": False}
                    ]
                )
            ]
        
        @self.server.get_prompt()
        async def handle_get_prompt(name: str, arguments: dict) -> Prompt:
            """Generate business-focused prompts with real data"""
            
            dubai_now = self.db.get_dubai_time()
            
            if name == "business_dashboard":
                # Get latest business metrics
                expense_analytics = await self.expense_manager.get_expense_analytics()
                support_metrics = await self.ticket_system.generate_support_metrics()
                crm_pipeline = await self.crm_module.get_customer_pipeline()
                financial_report = await self.invoice_generator.generate_financial_report()
                
                prompt_text = f"""
# 📊 Business Dashboard - {dubai_now.strftime('%B %d, %Y')} (Dubai Time)

## 💰 Financial Overview
- **Total Invoiced**: {financial_report['summary']['total_invoiced']:,.2f} AED
- **Collection Rate**: {financial_report['summary']['collection_rate']:.1f}%
- **Overdue Amount**: {financial_report['summary']['overdue_amount']:,.2f} AED

## 💸 Expense Summary  
- **Total Expenses**: {expense_analytics['total_amount']:,.2f} AED
- **Categories**: {len(expense_analytics['category_breakdown'])} active categories
- **Pending Approvals**: {expense_analytics['pending_approvals']} items

## 🎫 Support Performance
- **Open Tickets**: Active support requests
- **SLA Compliance**: Meeting response time targets
- **Resolution Rate**: Ticket closure efficiency

## 🏢 CRM Pipeline
- **Active Prospects**: Potential new business
- **Conversion Opportunities**: Ready to close deals
- **Customer Health**: Relationship status overview

---

**Focus Areas for Today:**
1. Review overdue invoices and send payment reminders
2. Approve pending expense reports
3. Follow up on high-priority support tickets
4. Contact warm leads in CRM pipeline

*Dashboard generated at {dubai_now.isoformat()} Dubai time*
                """
                
            elif name == "expense_analysis":
                period = arguments.get("period", "month")
                expense_report = await self.expense_manager.generate_expense_report({"period": period})
                
                prompt_text = f"""
# 💸 Expense Analysis - {period.title()} Review

## Key Metrics
- **Total Spend**: {expense_report['total_amount']:,.2f} AED
- **Transaction Count**: {expense_report['expense_count']} expenses
- **Top Categories**: {', '.join(list(expense_report['categories'].keys())[:3])}

## Category Breakdown
"""
                for category, data in expense_report['categories'].items():
                    prompt_text += f"- **{category.title()}**: {data['amount']:,.2f} AED ({data['count']} items)\n"
                
                prompt_text += f"""

## AI Insights
{expense_report.get('ai_insights', 'Analysis completed')}

## Recommendations
1. Review high-value expense categories for optimization opportunities
2. Ensure all expenses are properly categorized and documented
3. Consider setting spending limits for frequently used categories
4. Implement automated approval workflows for routine expenses

*Analysis period: {expense_report['period']}*
                """
            
            else:
                prompt_text = f"Business prompt '{name}' generated at {dubai_now.isoformat()} Dubai time"
            
            return Prompt(
                name=name,
                description=f"Business analysis prompt for {name}",
                arguments=[],
                messages=[
                    {"role": "user", "content": TextContent(type="text", text=prompt_text)}
                ]
            )
    
    async def run(self):
        """Run the enhanced MCP server"""
        logger.info(f"Starting Enhanced Leantime Business Suite MCP Server v{EnhancedLeantimeMCPConfig.MCP_SERVER_VERSION}")
        logger.info("Business modules: Expenses ✓, Support ✓, CRM ✓, Invoicing ✓")
        
        # Run database migrations
        try:
            await self.db.run_migration("migrations/001_business_tables.sql")
        except Exception as e:
            logger.warning(f"Migration skipped: {e}")
        
        # Initialize server
        async with self.server.run(
            transport="stdio",
            initialization_options=InitializationOptions(
                server_name=EnhancedLeantimeMCPConfig.MCP_SERVER_NAME,
                server_version=EnhancedLeantimeMCPConfig.MCP_SERVER_VERSION,
                capabilities=self.server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={}
                )
            )
        ) as running_server:
            logger.info("🚀 Enhanced Leantime Business Suite ready for connections!")
            logger.info("💼 Available: Project Management + Expenses + Support + CRM + Invoicing")
            
            # Server will run until interrupted
            await running_server.wait_closed()

# Main entry point
async def main():
    """Main entry point"""
    server = EnhancedLeantimeMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())