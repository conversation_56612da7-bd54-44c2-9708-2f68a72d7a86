# BAML Service Dockerfile for Leantime Integration
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install BAML CLI
RUN pip install --no-cache-dir baml-py

# Copy BAML source files
COPY baml_src/ ./baml_src/
COPY baml_project.yml .

# Generate BAML client
RUN baml generate --target python --output-path ./baml_client

# Copy Python integration code
COPY python/ ./

# Create necessary directories
RUN mkdir -p /app/logs /app/cache /app/data

# Set environment variables
ENV PYTHONPATH="/app"
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Create non-root user
RUN useradd --create-home --shell /bin/bash baml
RUN chown -R baml:baml /app
USER baml

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "-m", "uvicorn", "leantime_integration:app", "--host", "0.0.0.0", "--port", "8000"]