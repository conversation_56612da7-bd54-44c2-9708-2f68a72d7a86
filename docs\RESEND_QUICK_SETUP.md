# Resend Email Setup for Leantime - Quick Guide

## 5-Minute Setup Process

### Step 1: Create Resend Account (1 minute)
1. Go to https://resend.com/signup
2. Sign up with your email
3. Verify your email address
4. **No credit card required!**

### Step 2: Add Your Domain (2 minutes)
1. In Resend dashboard, click "Domains"
2. Click "Add Domain"
3. Enter: `kanousai.com`
4. Click "Add"

### Step 3: Configure Cloudflare DNS (2 minutes)
Go to your Cloudflare DNS settings and add these records:

```
Type: TXT
Name: @
Value: v=spf1 include:amazonses.com ~all

Type: TXT
Name: resend._domainkey
Value: [Copy from Resend dashboard - starts with p=MIGfMA0GCS...]

Type: CNAME
Name: cname._domainkey
Value: [Copy from Resend dashboard]
```

### Step 4: Get Your API Key (30 seconds)
1. Click "API Keys" in Resend dashboard
2. Create a new API key
3. Copy the key (starts with `re_`)

### Step 5: Update Leantime Configuration
Edit your `.env` file and update this line:
```env
LEAN_EMAIL_SMTP_PASSWORD = 're_YOUR_API_KEY_HERE'
```

Replace `'re_YOUR_API_KEY_HERE'` with your actual API key.

### Step 6: Test & Deploy
```bash
# Test email configuration
python scripts/test-email.py re_YOUR_API_KEY <EMAIL>

# Restart Leantime
docker-compose restart leantime

# Check logs
docker-compose logs -f leantime | grep -i email
```

## Verification Checklist

- [ ] Resend account created and verified
- [ ] Domain added to Resend
- [ ] DNS records added to Cloudflare
- [ ] Domain verified in Resend (green checkmark)
- [ ] API key generated
- [ ] .env file updated with API key
- [ ] Test email sent successfully
- [ ] Leantime container restarted

## Testing in Leantime

1. Go to Leantime login page
2. Click "Forgot Password"
3. Enter a valid email
4. Check if email is received

## Troubleshooting

### Domain Not Verifying?
- Wait 5-10 minutes for DNS propagation
- Check Cloudflare proxy is OFF for these records
- Verify records are exactly as shown in Resend

### Authentication Failed?
- Verify API key starts with `re_`
- Check no extra spaces in .env file
- Ensure quotes around the API key value

### Connection Refused?
- Check Docker network connectivity
- Verify port 587 is not blocked
- Try restarting Docker

## Free Tier Limits
- **100 emails/day**
- **3,000 emails/month**
- Perfect for development and small teams

## Support Links
- Resend Dashboard: https://resend.com/overview
- API Keys: https://resend.com/api-keys
- Domain Settings: https://resend.com/domains
- Documentation: https://resend.com/docs

## Alternative: Brevo (If Resend doesn't work)

If you have issues with Resend, Brevo offers 300 emails/day forever free:

1. Sign up at https://www.brevo.com/
2. Get SMTP credentials from settings
3. Update .env:
```env
LEAN_EMAIL_SMTP_HOSTS = 'smtp-relay.brevo.com'
LEAN_EMAIL_SMTP_USERNAME = 'YOUR_BREVO_EMAIL'
LEAN_EMAIL_SMTP_PASSWORD = 'YOUR_SMTP_KEY'
```

---

**Need help?** The test script will guide you through any issues!