#!/usr/bin/env python3
"""
Expense Management Module for Leantime MCP Server
Handles expense tracking, categorization, approval workflows, and receipt processing
Configured for Arab Standard Time (Dubai, UTC+4)
"""

import os
import json
import uuid
import base64
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import mysql.connector
from mysql.connector import pooling
import pytz

# OCR and AI processing
from PIL import Image
import io
import re

class ExpenseManager:
    """Manages all expense-related operations with AI-powered features"""
    
    def __init__(self, db_pool, openrouter_key: str):
        self.db_pool = db_pool
        self.openrouter_key = openrouter_key
        self.openrouter_base = "https://openrouter.ai/api/v1"
        
        # Dubai timezone (Arab Standard Time)
        self.timezone = pytz.timezone('Asia/Dubai')
        
        # Expense categories with AI training data
        self.expense_categories = {
            'office_supplies': ['office', 'supplies', 'stationery', 'paper', 'pens', 'printer'],
            'travel': ['travel', 'flight', 'hotel', 'uber', 'taxi', 'rental', 'airbnb', 'booking'],
            'meals': ['restaurant', 'food', 'lunch', 'dinner', 'coffee', 'starbucks', 'meal'],
            'software': ['software', 'subscription', 'saas', 'license', 'adobe', 'microsoft'],
            'marketing': ['marketing', 'advertising', 'facebook', 'google ads', 'promotion'],
            'equipment': ['laptop', 'computer', 'monitor', 'keyboard', 'mouse', 'hardware'],
            'utilities': ['internet', 'phone', 'electricity', 'water', 'utilities'],
            'professional': ['consulting', 'legal', 'accounting', 'professional', 'service'],
            'training': ['course', 'training', 'certification', 'education', 'workshop'],
            'fuel': ['petrol', 'gas', 'fuel', 'diesel', 'adnoc', 'eppco', 'enoc'],
            'telecommunications': ['etisalat', 'du', 'mobile', 'broadband', 'telecom'],
            'entertainment': ['entertainment', 'team', 'event', 'party', 'celebration']
        }
    
    def get_connection(self):
        """Get database connection from pool"""
        return self.db_pool.get_connection()
    
    def get_dubai_time(self) -> datetime:
        """Get current time in Dubai timezone"""
        return datetime.now(self.timezone)
    
    async def categorize_expense_ai(self, title: str, description: str, amount: float) -> Dict[str, Any]:
        """Use AI to categorize expense and extract insights"""
        
        prompt = f"""
        Analyze this business expense for a company in Dubai, UAE:
        
        Title: {title}
        Description: {description}
        Amount: {amount} AED
        
        Please categorize this expense and provide insights:
        
        Available categories:
        - office_supplies: Office materials, stationery, supplies
        - travel: Business travel, flights, hotels, transportation
        - meals: Business meals, client entertainment, food
        - software: Software licenses, subscriptions, SaaS
        - marketing: Advertising, promotion, marketing campaigns
        - equipment: Hardware, computers, office equipment
        - utilities: Internet, phone, electricity, office utilities
        - professional: Legal, consulting, accounting services
        - training: Courses, certifications, professional development
        - fuel: Petrol, diesel, vehicle fuel (common in Dubai)
        - telecommunications: Etisalat, du, mobile, internet services
        - entertainment: Team events, client entertainment
        
        Respond in JSON format:
        {{
            "category": "most_likely_category",
            "confidence": 0.85,
            "reasoning": "why this category was chosen",
            "subcategory": "specific subcategory if applicable",
            "is_business_expense": true,
            "red_flags": ["any suspicious patterns"],
            "suggested_tags": ["relevant", "tags"],
            "currency_detected": "AED or other",
            "location_context": "Dubai/UAE specific context if any"
        }}
        """
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://leantime.io",
                    "X-Title": "Leantime Expense Manager"
                }
                
                payload = {
                    "model": "mistralai/mistral-7b-instruct:free",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1024,
                    "temperature": 0.3  # Lower temperature for more consistent categorization
                }
                
                async with session.post(
                    f"{self.openrouter_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        # Extract JSON from response
                        try:
                            if "```json" in content:
                                content = content.split("```json")[1].split("```")[0]
                            elif "```" in content:
                                content = content.split("```")[1].split("```")[0]
                            
                            ai_analysis = json.loads(content.strip())
                            return ai_analysis
                        except json.JSONDecodeError:
                            # Fallback to simple categorization
                            return self._simple_categorize(title, description)
                    else:
                        return self._simple_categorize(title, description)
        except Exception as e:
            print(f"AI categorization failed: {e}")
            return self._simple_categorize(title, description)
    
    def _simple_categorize(self, title: str, description: str) -> Dict[str, Any]:
        """Fallback categorization using keyword matching"""
        text = f"{title} {description}".lower()
        
        for category, keywords in self.expense_categories.items():
            if any(keyword in text for keyword in keywords):
                return {
                    "category": category,
                    "confidence": 0.6,
                    "reasoning": f"Keyword match from: {keywords}",
                    "subcategory": None,
                    "is_business_expense": True,
                    "red_flags": [],
                    "suggested_tags": [category],
                    "currency_detected": "AED",
                    "location_context": "Dubai/UAE"
                }
        
        return {
            "category": "office_supplies",  # Default
            "confidence": 0.3,
            "reasoning": "Default category - requires manual review",
            "subcategory": None,
            "is_business_expense": True,
            "red_flags": ["uncategorized"],
            "suggested_tags": ["needs_review"],
            "currency_detected": "AED",
            "location_context": "Dubai/UAE"
        }
    
    async def process_receipt_ocr(self, image_data: bytes) -> Dict[str, Any]:
        """Process receipt using AI vision (simplified version)"""
        
        # For a full implementation, you'd use GPT-4 Vision or similar
        # This is a simplified version that extracts basic info
        
        prompt = """
        This appears to be a receipt image. Extract the following information:
        
        - Vendor/merchant name
        - Date of transaction
        - Total amount
        - Currency
        - Location/address if visible
        - Individual line items if clear
        
        Return as JSON format:
        {
            "vendor": "Merchant Name",
            "date": "YYYY-MM-DD",
            "amount": 123.45,
            "currency": "AED",
            "location": "Dubai, UAE",
            "line_items": [{"description": "Item", "amount": 10.00}],
            "confidence": 0.85
        }
        """
        
        # For now, return a placeholder - in production, integrate with OCR service
        return {
            "vendor": "Extracted via OCR",
            "date": self.get_dubai_time().strftime("%Y-%m-%d"),
            "amount": 0.00,
            "currency": "AED",
            "location": "Dubai, UAE",
            "line_items": [],
            "confidence": 0.5,
            "note": "OCR processing placeholder - integrate with vision API"
        }
    
    async def create_expense(self, expense_data: Dict[str, Any]) -> str:
        """Create a new expense entry"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Generate unique expense ID
            expense_id = f"EXP-{uuid.uuid4().hex[:8].upper()}"
            
            # AI categorization if not provided
            if not expense_data.get('category'):
                ai_analysis = await self.categorize_expense_ai(
                    expense_data['title'],
                    expense_data.get('description', ''),
                    expense_data['amount']
                )
                expense_data['category'] = ai_analysis['category']
                expense_data['ai_category_confidence'] = ai_analysis['confidence']
                expense_data['ai_flags'] = json.dumps({
                    'red_flags': ai_analysis.get('red_flags', []),
                    'suggested_tags': ai_analysis.get('suggested_tags', []),
                    'reasoning': ai_analysis.get('reasoning', '')
                })
            
            # Process receipt if provided
            receipt_ocr_data = None
            if expense_data.get('receipt_data'):
                ocr_result = await self.process_receipt_ocr(expense_data['receipt_data'])
                receipt_ocr_data = json.dumps(ocr_result)
            
            # Current Dubai time
            dubai_now = self.get_dubai_time()
            
            # Insert expense
            query = """
                INSERT INTO mcp_expenses (
                    expense_id, project_id, user_id, client_id,
                    title, description, category, subcategory,
                    amount, currency, tax_amount, expense_date,
                    status, receipt_path, receipt_ocr_data,
                    is_mileage, mileage_start, mileage_end, mileage_distance, mileage_rate,
                    ai_category_confidence, ai_flags, notes, created_by
                ) VALUES (
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s
                )
            """
            
            values = (
                expense_id,
                expense_data.get('project_id'),
                expense_data['user_id'],
                expense_data.get('client_id'),
                expense_data['title'],
                expense_data.get('description', ''),
                expense_data.get('category', 'office_supplies'),
                expense_data.get('subcategory'),
                expense_data['amount'],
                expense_data.get('currency', 'AED'),
                expense_data.get('tax_amount', 0),
                expense_data.get('expense_date', dubai_now.date()),
                expense_data.get('status', 'draft'),
                expense_data.get('receipt_path'),
                receipt_ocr_data,
                expense_data.get('is_mileage', False),
                expense_data.get('mileage_start'),
                expense_data.get('mileage_end'),
                expense_data.get('mileage_distance'),
                expense_data.get('mileage_rate'),
                expense_data.get('ai_category_confidence'),
                expense_data.get('ai_flags'),
                expense_data.get('notes', ''),
                expense_data['user_id']  # created_by
            )
            
            cursor.execute(query, values)
            conn.commit()
            
            return expense_id
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to create expense: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def get_expenses(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Retrieve expenses with optional filters"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Base query
            query = """
                SELECT 
                    e.*,
                    p.name as project_name,
                    c.name as client_name,
                    u.firstname as user_firstname,
                    u.lastname as user_lastname
                FROM mcp_expenses e
                LEFT JOIN zp_projects p ON e.project_id = p.id
                LEFT JOIN zp_clients c ON e.client_id = c.id
                LEFT JOIN zp_users u ON e.user_id = u.id
                WHERE 1=1
            """
            
            params = []
            
            # Apply filters
            if filters:
                if filters.get('project_id'):
                    query += " AND e.project_id = %s"
                    params.append(filters['project_id'])
                
                if filters.get('user_id'):
                    query += " AND e.user_id = %s"
                    params.append(filters['user_id'])
                
                if filters.get('status'):
                    query += " AND e.status = %s"
                    params.append(filters['status'])
                
                if filters.get('category'):
                    query += " AND e.category = %s"
                    params.append(filters['category'])
                
                if filters.get('date_from'):
                    query += " AND e.expense_date >= %s"
                    params.append(filters['date_from'])
                
                if filters.get('date_to'):
                    query += " AND e.expense_date <= %s"
                    params.append(filters['date_to'])
            
            query += " ORDER BY e.expense_date DESC, e.created_at DESC"
            
            if filters and filters.get('limit'):
                query += f" LIMIT {int(filters['limit'])}"
            
            cursor.execute(query, params)
            expenses = cursor.fetchall()
            
            # Convert decimals to float for JSON serialization
            for expense in expenses:
                if expense['amount']:
                    expense['amount'] = float(expense['amount'])
                if expense['tax_amount']:
                    expense['tax_amount'] = float(expense['tax_amount'])
                if expense['mileage_distance']:
                    expense['mileage_distance'] = float(expense['mileage_distance'])
                if expense['mileage_rate']:
                    expense['mileage_rate'] = float(expense['mileage_rate'])
                if expense['ai_category_confidence']:
                    expense['ai_category_confidence'] = float(expense['ai_category_confidence'])
                
                # Parse JSON fields
                if expense['receipt_ocr_data']:
                    try:
                        expense['receipt_ocr_data'] = json.loads(expense['receipt_ocr_data'])
                    except:
                        expense['receipt_ocr_data'] = None
                
                if expense['ai_flags']:
                    try:
                        expense['ai_flags'] = json.loads(expense['ai_flags'])
                    except:
                        expense['ai_flags'] = {}
            
            return expenses
            
        finally:
            cursor.close()
            conn.close()
    
    async def approve_expense(self, expense_id: str, approver_id: int, notes: str = "") -> bool:
        """Approve an expense"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            dubai_now = self.get_dubai_time()
            
            query = """
                UPDATE mcp_expenses 
                SET status = 'approved',
                    approved_by = %s,
                    approved_at = %s,
                    notes = CONCAT(IFNULL(notes, ''), %s)
                WHERE expense_id = %s AND status IN ('submitted', 'draft')
            """
            
            approval_note = f"\n[APPROVED {dubai_now.strftime('%Y-%m-%d %H:%M')}]: {notes}"
            
            cursor.execute(query, (approver_id, dubai_now, approval_note, expense_id))
            
            if cursor.rowcount > 0:
                conn.commit()
                return True
            else:
                return False
                
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to approve expense: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def generate_expense_report(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate expense report with AI insights"""
        
        expenses = await self.get_expenses(filters)
        
        if not expenses:
            return {
                "total_expenses": 0,
                "total_amount": 0,
                "expense_count": 0,
                "categories": {},
                "insights": "No expenses found for the specified criteria"
            }
        
        # Calculate totals
        total_amount = sum(exp['amount'] for exp in expenses)
        expense_count = len(expenses)
        
        # Category breakdown
        categories = {}
        for expense in expenses:
            cat = expense['category']
            if cat not in categories:
                categories[cat] = {"count": 0, "amount": 0}
            categories[cat]["count"] += 1
            categories[cat]["amount"] += expense['amount']
        
        # AI insights
        insights_prompt = f"""
        Analyze these expense patterns for a Dubai-based business:
        
        Total Expenses: {expense_count} items
        Total Amount: {total_amount} AED
        Categories: {json.dumps(categories, indent=2)}
        
        Provide insights on:
        1. Spending patterns
        2. Areas for cost optimization
        3. Unusual patterns or trends
        4. Dubai/UAE specific considerations
        5. Recommendations
        
        Keep response concise and actionable.
        """
        
        try:
            ai_insights = await self._get_ai_insights(insights_prompt)
        except:
            ai_insights = "AI analysis unavailable"
        
        return {
            "period": filters.get('date_from', 'All time') + " to " + filters.get('date_to', 'Present'),
            "total_amount": total_amount,
            "expense_count": expense_count,
            "categories": categories,
            "currency": "AED",
            "timezone": "Asia/Dubai",
            "ai_insights": ai_insights,
            "generated_at": self.get_dubai_time().isoformat()
        }
    
    async def _get_ai_insights(self, prompt: str) -> str:
        """Get AI insights for reporting"""
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_key}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "model": "mistralai/mistral-7b-instruct:free",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 512,
                    "temperature": 0.7
                }
                
                async with session.post(
                    f"{self.openrouter_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        return "AI insights unavailable"
        except:
            return "AI insights unavailable"
    
    async def smart_mileage_calculation(self, start_location: str, end_location: str) -> Dict[str, Any]:
        """Calculate mileage using Dubai road network knowledge"""
        
        # In production, integrate with Google Maps or UAE road network API
        # For now, provide intelligent estimates based on Dubai locations
        
        dubai_distances = {
            ("dubai marina", "downtown"): 25.0,
            ("deira", "jlt"): 30.0,
            ("sharjah", "dubai"): 45.0,
            ("abu dhabi", "dubai"): 150.0,
            ("ajman", "dubai"): 55.0,
            # Add more common routes
        }
        
        start_key = start_location.lower()
        end_key = end_location.lower()
        
        # Try to find exact match
        distance = dubai_distances.get((start_key, end_key)) or dubai_distances.get((end_key, start_key))
        
        if not distance:
            # AI estimation
            distance_prompt = f"""
            Estimate the driving distance between these locations in Dubai, UAE:
            From: {start_location}
            To: {end_location}
            
            Consider:
            - Dubai road network
            - Typical traffic routes
            - Highway vs city roads
            
            Respond with just the estimated distance in kilometers as a number.
            """
            
            try:
                ai_distance = await self._get_ai_insights(distance_prompt)
                # Extract number from response
                import re
                numbers = re.findall(r'\d+\.?\d*', ai_distance)
                if numbers:
                    distance = float(numbers[0])
                else:
                    distance = 25.0  # Default
            except:
                distance = 25.0  # Default for Dubai area
        
        # UAE mileage rate (typical business rate)
        uae_mileage_rate = 0.50  # AED per KM (adjust as needed)
        
        return {
            "distance_km": distance,
            "rate_per_km": uae_mileage_rate,
            "total_amount": distance * uae_mileage_rate,
            "currency": "AED",
            "estimated": True if not dubai_distances.get((start_key, end_key)) else False
        }
    
    async def get_expense_analytics(self, timeframe: str = "month") -> Dict[str, Any]:
        """Get expense analytics dashboard data"""
        
        dubai_now = self.get_dubai_time()
        
        # Calculate date range
        if timeframe == "week":
            start_date = dubai_now - timedelta(days=7)
        elif timeframe == "month":
            start_date = dubai_now - timedelta(days=30)
        elif timeframe == "quarter":
            start_date = dubai_now - timedelta(days=90)
        else:  # year
            start_date = dubai_now - timedelta(days=365)
        
        filters = {
            'date_from': start_date.date(),
            'date_to': dubai_now.date()
        }
        
        expenses = await self.get_expenses(filters)
        
        # Analytics calculations
        analytics = {
            "timeframe": timeframe,
            "period_start": start_date.date().isoformat(),
            "period_end": dubai_now.date().isoformat(),
            "total_amount": sum(exp['amount'] for exp in expenses),
            "total_count": len(expenses),
            "average_expense": sum(exp['amount'] for exp in expenses) / len(expenses) if expenses else 0,
            "status_breakdown": {},
            "category_breakdown": {},
            "monthly_trend": [],
            "top_expenses": sorted(expenses, key=lambda x: x['amount'], reverse=True)[:10],
            "pending_approvals": len([e for e in expenses if e['status'] == 'submitted']),
            "ai_flagged": len([e for e in expenses if e.get('ai_flags') and json.loads(e['ai_flags'] or '{}').get('red_flags')]),
            "currency": "AED",
            "timezone": "Asia/Dubai"
        }
        
        # Status breakdown
        for expense in expenses:
            status = expense['status']
            analytics['status_breakdown'][status] = analytics['status_breakdown'].get(status, 0) + 1
        
        # Category breakdown
        for expense in expenses:
            category = expense['category']
            analytics['category_breakdown'][category] = analytics['category_breakdown'].get(category, 0) + expense['amount']
        
        return analytics