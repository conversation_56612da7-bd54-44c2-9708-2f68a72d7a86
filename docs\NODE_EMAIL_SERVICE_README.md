# Node.js Email Service for Leantime

A multi-provider email relay service with automatic failover, queuing, and template support.

## 🚀 Quick Start

### 1. Start the Email Service

```bash
# Make script executable
chmod +x scripts/start-email-service.sh

# Start the service
./scripts/start-email-service.sh
```

### 2. Configure Email Providers

Edit `.env` file and add at least one provider:

```env
# Recommended: Resend (Cloudflare compatible)
RESEND_API_KEY=re_your_api_key_here

# Alternative: Brevo (300/day free forever)
BREVO_USERNAME=<EMAIL>
BREVO_SMTP_KEY=your_smtp_key_here
```

### 3. Restart and Test

```bash
# Restart after configuration
docker-compose -f docker-compose.email.yml restart

# Test the service
cd email-service
<NAME_EMAIL>
```

## 📊 Dashboard

Access the web dashboard at: http://localhost:3001

⚠️ **Security Note**: All API endpoints now require authentication with a secure API key.

Features:
- Service health monitoring
- Email queue statistics
- Provider status and failover
- Send test emails
- Real-time logs

## 🏗️ Architecture

```
Leantime → Email Service → [Resend|Brevo|Gmail] → Recipients
             ↓
           Redis Queue (reliable delivery)
```

### Components

- **SMTP Relay**: Internal SMTP server on port 2525
- **REST API**: HTTP API on port 3000 for direct email sending
- **Redis Queue**: Reliable email delivery with retry logic
- **Multi-Provider**: Automatic failover between email services
- **Templates**: Handlebars-based email templates

## 📧 Email Providers

### Primary: Resend (Recommended)
- ✅ Works with Cloudflare
- ✅ 100 emails/day, 3000/month free
- ✅ No credit card required
- ✅ Fast DNS verification

Setup:
1. Sign up at https://resend.com
2. Add domain and verify DNS
3. Get API key
4. Add to `.env`: `RESEND_API_KEY=re_your_key`

### Fallback 1: Brevo
- ✅ 300 emails/day forever free
- ✅ No credit card required
- ✅ SMTP authentication

Setup:
1. Sign up at https://www.brevo.com
2. Get SMTP credentials
3. Add to `.env`:
   ```
   BREVO_USERNAME=<EMAIL>
   BREVO_SMTP_KEY=your_smtp_key
   ```

### Fallback 2: Gmail OAuth2
- ✅ Free with Google account
- ⚠️ Requires OAuth2 setup
- ⚠️ Daily sending limits

## 🔧 Configuration

### Environment Variables

```env
# Email Service
EMAIL_DASHBOARD_PORT=3000
EMAIL_REDIS_PASSWORD=secure_password

# Providers (configure at least one)
RESEND_API_KEY=re_your_key
BREVO_USERNAME=<EMAIL>
BREVO_SMTP_KEY=your_smtp_key

# Leantime Integration (already configured)
LEAN_EMAIL_SMTP_HOSTS=email-service
LEAN_EMAIL_SMTP_PORT=2525
LEAN_EMAIL_SMTP_USERNAME=leantime
LEAN_EMAIL_SMTP_PASSWORD=leantime-email-service
```

### Docker Services

```yaml
# Start all services
docker-compose -f docker-compose.yml -f docker-compose.email.yml up -d

# Or just email service
docker-compose -f docker-compose.email.yml up -d
```

## 🧪 Testing

### 1. Direct SMTP Test
```bash
cd email-service
node test-email.js <EMAIL>
```

### 2. API Test
```bash
curl -X POST http://localhost:3001/api/send \
  -H "x-api-key: YOUR_API_KEY_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "html": "<h1>Hello from Email Service</h1>"
  }'
```

### 3. Template Test
```bash
curl -X POST http://localhost:3001/api/send \
  -H "x-api-key: YOUR_API_KEY_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Password Reset",
    "template": "password-reset",
    "templateData": {
      "siteName": "Leantime",
      "userName": "Test User",
      "resetLink": "https://example.com/reset"
    }
  }'
```

## 📄 Templates

Built-in templates:
- `password-reset` - Password reset emails
- `user-invitation` - User invitation emails  
- `notification` - General notifications

### Custom Templates

Add `.hbs` files to `email-service/templates/templates/`:

```handlebars
<!-- custom-template.hbs -->
<html>
<body>
  <h1>Hello {{userName}}!</h1>
  <p>{{message}}</p>
</body>
</html>
```

Use with API:
```javascript
{
  "template": "custom-template",
  "templateData": {
    "userName": "John",
    "message": "Welcome!"
  }
}
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Queue Status
```bash
curl http://localhost:3001/api/queue/status
```

### Provider Status
```bash
curl http://localhost:3001/api/providers/status
```

### Logs
```bash
# Email service logs
docker-compose -f docker-compose.email.yml logs -f email-service

# Redis logs
docker-compose -f docker-compose.email.yml logs -f redis_email
```

## 🔧 Troubleshooting

### Service Won't Start
```bash
# Check if network exists
docker network ls | grep leantime-net

# Create network if missing
docker network create leantime-net

# Rebuild containers
docker-compose -f docker-compose.email.yml build --no-cache
```

### Emails Not Sending
1. Check provider configuration in `.env`
2. Verify API keys are correct
3. Check provider daily limits
4. Review service logs

### Dashboard Not Accessible
```bash
# Check if port 3000 is available
netstat -tulpn | grep :3000

# Use different port
EMAIL_DASHBOARD_PORT=3001 docker-compose -f docker-compose.email.yml up -d
```

## 🚀 Production Deployment

### 1. Security
- Change default Redis password
- Use environment-specific API keys
- Enable firewall rules for port 3000
- Use SSL/TLS for dashboard access

### 2. Scaling
- Increase Redis memory limit
- Add more email provider fallbacks
- Monitor queue performance
- Set up log rotation

### 3. Backup
- Backup Redis data volume
- Export email templates
- Document API key rotation

## 🔗 Integration

### Leantime Configuration
The service is already configured to work with Leantime. No additional setup needed.

### Other Applications
Use the SMTP relay or REST API:

```javascript
// SMTP (any application)
const transporter = nodemailer.createTransporter({
  host: 'localhost',  // or email-service container
  port: 2525,
  auth: {
    user: 'leantime',
    pass: 'leantime-email-service'
  }
});

// REST API (modern applications)
fetch('http://localhost:3001/api/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    to: '<EMAIL>',
    subject: 'Hello',
    html: '<h1>Hello World</h1>'
  })
});
```

## 📚 API Reference

### POST /api/send
Send an email

**Request:**
```json
{
  "to": "<EMAIL>",
  "from": "<EMAIL>",  // optional
  "subject": "Email Subject",
  "html": "<h1>HTML Content</h1>",  // optional if using template
  "text": "Text Content",  // optional
  "template": "template-name",  // optional
  "templateData": {}  // required if using template
}
```

**Response:**
```json
{
  "success": true,
  "jobId": "12345",
  "message": "Email queued for delivery"
}
```

### GET /health
Service health check

**Response:**
```json
{
  "status": "healthy",
  "smtp": true,
  "queue": true,
  "providers": [...],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

**Need help?** Check the dashboard at http://localhost:3001 or review the logs!