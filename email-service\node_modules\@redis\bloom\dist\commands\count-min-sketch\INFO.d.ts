export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export declare function transformArguments(key: string): Array<string>;
export type InfoRawReply = [
    _: string,
    width: number,
    _: string,
    depth: number,
    _: string,
    count: number
];
export interface InfoReply {
    width: number;
    depth: number;
    count: number;
}
export declare function transformReply(reply: InfoRawReply): InfoReply;
