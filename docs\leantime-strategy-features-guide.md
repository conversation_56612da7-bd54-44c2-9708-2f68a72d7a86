# 🎯 Leantime Strategy Features Guide
## "Set Direction" and "Map Your Steps" Alternatives

Based on Leantime 3.5.12 analysis, here are the equivalent features for your strategic planning needs:

## 🚨 **Key Finding**: 
"Set Direction" and "Map Your Steps" are **NOT standard Leantime feature names**. However, Leantime provides equivalent functionality through different tools.

---

## 🎯 **"SET DIRECTION" ALTERNATIVES**

### 1. **Project Blueprints** 
- **Location**: Main Menu → "Blueprints" 
- **URL**: `https://admin.dxbmeta.com/blueprints`
- **Function**: Strategic project templates and planning frameworks
- **Use Case**: Set overall project direction and strategic goals

### 2. **Lean Canvas Tool**
- **Location**: Blueprints → "Lean Canvas"
- **URL**: `https://admin.dxbmeta.com/blueprints/canvas/lean`
- **Function**: 12-section business planning tool
- **Sections**: Problem, Solution, Key Metrics, Unique Value Proposition, Unfair Advantage, Channels, Customer Segments, Cost Structure, Revenue Streams
- **Use Case**: Strategic business direction setting

### 3. **Business Model Canvas**
- **Location**: Blueprints → "Business Model Canvas"  
- **URL**: `https://admin.dxbmeta.com/blueprints/canvas/business`
- **Function**: 9-section strategic visualization
- **Use Case**: Define business strategy and direction

### 4. **SWOT Analysis Canvas**
- **Location**: Blueprints → "SWOT Analysis"
- **URL**: `https://admin.dxbmeta.com/blueprints/swot`
- **Function**: Strategic analysis framework
- **Use Case**: Analyze strengths, weaknesses, opportunities, threats

### 5. **Project Goals & Objectives**
- **Location**: Projects → "Goals" tab
- **URL**: `https://admin.dxbmeta.com/projects/[project-id]/goals`
- **Function**: Set strategic objectives and KPIs
- **Use Case**: Define measurable direction and outcomes

---

## 📋 **"MAP YOUR STEPS" ALTERNATIVES**

### 1. **Timeline View**
- **Location**: Projects → "Timeline" tab
- **URL**: `https://admin.dxbmeta.com/projects/[project-id]/timeline`
- **Function**: Visual step-by-step planning
- **Use Case**: Map out sequential steps and dependencies

### 2. **Gantt Chart**
- **Location**: Projects → "Gantt" view
- **URL**: `https://admin.dxbmeta.com/projects/[project-id]/gantt`
- **Function**: Advanced timeline with dependencies
- **Use Case**: Detailed step mapping with relationships

### 3. **Milestones Management**
- **Location**: Projects → "Milestones" tab
- **URL**: `https://admin.dxbmeta.com/projects/[project-id]/milestones`  
- **Function**: Key checkpoint planning
- **Use Case**: Mark major steps and deliverables

### 4. **Task Dependencies**
- **Location**: Projects → Tasks → "Dependencies" 
- **URL**: Within task management interface
- **Function**: Link related tasks in sequence
- **Use Case**: Create step-by-step workflow

### 5. **Project Roadmap**
- **Location**: Projects → "Roadmap" view
- **URL**: `https://admin.dxbmeta.com/projects/[project-id]/roadmap`
- **Function**: High-level step visualization  
- **Use Case**: Strategic step mapping over time

---

## 🔄 **COMBINED WORKFLOW: "SET DIRECTION → MAP STEPS"**

### **Phase 1: Set Direction (Strategic Planning)**
1. **Start with Lean Canvas**:
   - Access: Main Menu → Blueprints → Lean Canvas
   - Define: Problem, Solution, Value Proposition
   - Set: Strategic direction and focus

2. **Create Project Goals**:
   - Access: Projects → Goals tab
   - Set: Measurable objectives and KPIs
   - Define: Success criteria

3. **SWOT Analysis** (Optional):
   - Access: Blueprints → SWOT Analysis
   - Analyze: Strategic position
   - Identify: Key focus areas

### **Phase 2: Map Your Steps (Execution Planning)**
1. **Create Timeline**:
   - Access: Projects → Timeline tab
   - Plan: Sequential phases and stages
   - Set: Start and end dates

2. **Define Milestones**:
   - Access: Projects → Milestones tab
   - Mark: Key deliverables and checkpoints
   - Set: Review and approval points

3. **Build Task Dependencies**:
   - Access: Projects → Tasks
   - Link: Related tasks in logical sequence
   - Create: Step-by-step workflow

4. **Generate Gantt Chart**:
   - Access: Projects → Gantt view
   - Visualize: Complete step mapping
   - Track: Progress and dependencies

---

## 🎯 **HOW TO ACCESS THESE FEATURES**

### **Login Instructions**:
1. Navigate to: `https://admin.dxbmeta.com`
2. Username: `<EMAIL>`
3. Password: `admin123`

### **Navigation Paths**:

#### **For "Set Direction" Features**:
```
Dashboard → Blueprints → [Canvas Tool]
Dashboard → Projects → [Project Name] → Goals
Dashboard → Strategy (if available)
```

#### **For "Map Your Steps" Features**:
```
Dashboard → Projects → [Project Name] → Timeline
Dashboard → Projects → [Project Name] → Gantt
Dashboard → Projects → [Project Name] → Milestones
Dashboard → Projects → [Project Name] → Tasks → Dependencies
```

---

## 🔧 **TROUBLESHOOTING**

### **If Features Are Missing**:

1. **Check User Permissions**:
   - Go to: Profile → Settings → Permissions
   - Verify: Access to Projects, Blueprints, Strategy modules

2. **Check Plugin Status**:
   - Go to: Admin → Plugins (if accessible)
   - Look for: Strategy Plugin, Canvas Plugin, Blueprint Plugin
   - Status: Should be "Enabled" or "Active"

3. **Check Project Setup**:
   - Ensure: You're in a project context for project-specific features
   - Create: New project if none exist
   - Assign: Yourself to the project

### **If URLs Don't Work**:

1. **Alternative Access Methods**:
   - Use main navigation menu
   - Look for sidebar links
   - Check project-specific tabs

2. **Version-Specific Locations**:
   - Some features may be in different locations
   - Check: Settings → Modules for feature toggles
   - Look for: "Strategy", "Planning", "Canvas" sections

---

## 💡 **BEST PRACTICES**

### **For Strategic Planning ("Set Direction")**:
1. Start with **Lean Canvas** for overall strategy
2. Set **Project Goals** for measurable outcomes  
3. Use **SWOT Analysis** for strategic positioning
4. Document decisions in **Project Wiki**

### **For Step Planning ("Map Your Steps")**:
1. Create **Timeline** first for overall flow
2. Add **Milestones** for key checkpoints
3. Build **Task Dependencies** for detailed steps
4. Use **Gantt Chart** for visual management
5. Track progress with **Status Updates**

### **Integration Tips**:
1. Link Canvas insights to Project Goals
2. Align Milestones with Strategic Objectives
3. Use Timeline to validate strategic timing
4. Regular review and adjustment cycles

---

## 📞 **NEXT STEPS**

1. **Login** to your Leantime system
2. **Navigate** to Blueprints section first
3. **Explore** available Canvas tools
4. **Create** a test project to access Timeline/Gantt features
5. **Document** which specific features work in your installation

## 🎉 **CONCLUSION**

While Leantime doesn't have features specifically named "Set Direction" and "Map Your Steps", it provides comprehensive alternatives:

- **Canvas Tools** = Strategic Direction Setting
- **Timeline/Gantt/Milestones** = Step Mapping
- **Combined Usage** = Complete strategic planning workflow

These tools provide equivalent functionality for strategic planning and execution management.