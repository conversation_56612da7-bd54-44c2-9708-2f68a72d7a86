version: '3.8'

# Production Docker Compose for DigitalOcean
# Optimized for shared hosting environment

services:
  # Nginx Reverse Proxy (handles SSL and routing)
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./certbot/www:/var/www/certbot:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - nginx_cache:/var/cache/nginx
    networks:
      - leantime-net
    depends_on:
      - leantime
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Certbot for SSL certificates
  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes:
      - ./certbot/www:/var/www/certbot:rw
      - ./certbot/conf:/etc/letsencrypt:rw
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"

  # MySQL Database
  mysql_leantime:
    image: mysql:8.4
    container_name: mysql_leantime
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
    networks:
      - leantime-net
    command: >
      --character-set-server=UTF8MB4
      --collation-server=UTF8MB4_unicode_ci
      --max_connections=200
      --innodb_buffer_pool_size=256M
      --innodb_log_file_size=64M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Leantime Application
  leantime:
    image: leantime-enterprise/leantime:latest
    container_name: leantime
    restart: always
    environment:
      LEAN_DB_HOST: mysql_leantime
      LEAN_DB_USER: ${MYSQL_USER}
      LEAN_DB_PASSWORD: ${MYSQL_PASSWORD}
      LEAN_DB_DATABASE: ${MYSQL_DATABASE}
      LEAN_SESSION_PASSWORD: ${LEAN_SESSION_PASSWORD}
      LEAN_SITENAME: ${LEAN_SITENAME}
      LEAN_LANGUAGE: ${LEAN_LANGUAGE:-en-US}
      LEAN_DEFAULT_TIMEZONE: ${LEAN_DEFAULT_TIMEZONE:-UTC}
      LEAN_ENABLE_MENU_TYPE: ${LEAN_ENABLE_MENU_TYPE:-both}
      LEAN_SESSION_EXPIRATION: ${LEAN_SESSION_EXPIRATION:-28800}
      # Email settings
      LEAN_EMAIL_RETURN: ${LEAN_EMAIL_RETURN}
      LEAN_EMAIL_USE_SMTP: ${LEAN_EMAIL_USE_SMTP:-true}
      LEAN_EMAIL_SMTP_HOSTS: ${LEAN_EMAIL_SMTP_HOSTS}
      LEAN_EMAIL_SMTP_USERNAME: ${LEAN_EMAIL_SMTP_USERNAME}
      LEAN_EMAIL_SMTP_PASSWORD: ${LEAN_EMAIL_SMTP_PASSWORD}
      LEAN_EMAIL_SMTP_PORT: ${LEAN_EMAIL_SMTP_PORT:-587}
      LEAN_EMAIL_SMTP_SECURE: ${LEAN_EMAIL_SMTP_SECURE:-tls}
      # S3 settings (optional)
      LEAN_USE_S3: ${LEAN_USE_S3:-false}
      LEAN_S3_BUCKET: ${LEAN_S3_BUCKET}
      LEAN_S3_REGION: ${LEAN_S3_REGION}
      LEAN_S3_KEY: ${LEAN_S3_KEY}
      LEAN_S3_SECRET: ${LEAN_S3_SECRET}
    volumes:
      - leantime_public_userfiles:/var/www/html/public/userfiles
      - leantime_userfiles:/var/www/html/userfiles
      - leantime_plugins:/var/www/html/app/Plugins
    networks:
      - leantime-net
    depends_on:
      mysql_leantime:
        condition: service_healthy
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - leantime-net
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Email Service (optional)
  email-service:
    image: leantime-enterprise/email-service:latest
    container_name: email-service
    restart: always
    environment:
      NODE_ENV: production
      PORT: 3001
      REDIS_HOST: redis
      REDIS_PORT: 6379
      EMAIL_PROVIDER: ${EMAIL_PROVIDER:-smtp}
      EMAIL_FROM: ${EMAIL_FROM}
      RESEND_API_KEY: ${RESEND_API_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
    networks:
      - leantime-net
    depends_on:
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backup Service
  backup:
    image: alpine:latest
    container_name: backup-service
    volumes:
      - mysql_data:/mysql-data:ro
      - leantime_userfiles:/leantime-data:ro
      - ./backups:/backups
    entrypoint: >
      /bin/sh -c "
      while true; do
        echo 'Running backup at' $$(date);
        tar -czf /backups/backup-$$(date +%Y%m%d-%H%M%S).tar.gz /mysql-data /leantime-data;
        find /backups -name 'backup-*.tar.gz' -mtime +7 -delete;
        sleep 86400;
      done
      "
    restart: always
    networks:
      - leantime-net

volumes:
  mysql_data:
    driver: local
  leantime_public_userfiles:
    driver: local
  leantime_userfiles:
    driver: local
  leantime_plugins:
    driver: local
  redis_data:
    driver: local
  nginx_cache:
    driver: local

networks:
  leantime-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16