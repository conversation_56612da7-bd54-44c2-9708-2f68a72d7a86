#!/usr/bin/env python3
"""
Configuration loader for AI Assistant System
Loads environment variables and provides centralized config access
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv
import json
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    host: str
    user: str
    password: str
    database: str
    pool_size: int
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        return cls(
            host=os.getenv('LEAN_DB_HOST', 'mysql_leantime_podman'),
            user=os.getenv('LEAN_DB_USER', 'lean'),
            password=os.getenv('LEAN_DB_PASSWORD', ''),
            database=os.getenv('LEAN_DB_DATABASE', 'leantime'),
            pool_size=int(os.getenv('DB_POOL_SIZE', '10'))
        )

@dataclass
class RedisConfig:
    """Redis configuration settings"""
    host: str
    port: int
    password: str
    db: int
    
    @classmethod
    def from_env(cls) -> 'RedisConfig':
        return cls(
            host=os.getenv('REDIS_HOST', 'redis_podman'),
            port=int(os.getenv('REDIS_PORT', '6379')),
            password=os.getenv('REDIS_PASSWORD', ''),
            db=int(os.getenv('REDIS_DB', '1'))
        )

@dataclass
class AIConfig:
    """AI/OpenRouter configuration settings"""
    api_key: str
    base_url: str
    model: str
    
    @classmethod
    def from_env(cls) -> 'AIConfig':
        return cls(
            api_key=os.getenv('OPENROUTER_API_KEY', ''),
            base_url=os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1'),
            model=os.getenv('AI_MODEL', 'anthropic/claude-3-opus')
        )

@dataclass
class SecurityConfig:
    """Security configuration settings"""
    secret_key: str
    jwt_secret: str
    jwt_algorithm: str
    jwt_expiry_hours: int
    allowed_clients: list
    
    @classmethod
    def from_env(cls) -> 'SecurityConfig':
        clients = os.getenv('ALLOWED_CLIENTS', '').split(',')
        return cls(
            secret_key=os.getenv('MCP_SECRET_KEY', 'default_secret_key'),
            jwt_secret=os.getenv('MCP_JWT_SECRET', 'default_jwt_secret'),
            jwt_algorithm=os.getenv('JWT_ALGORITHM', 'HS256'),
            jwt_expiry_hours=int(os.getenv('JWT_EXPIRY_HOURS', '24')),
            allowed_clients=[c.strip() for c in clients if c.strip()]
        )

@dataclass
class BusinessConfig:
    """Business configuration settings"""
    timezone: str
    currency: str
    vat_rate: float
    
    @classmethod
    def from_env(cls) -> 'BusinessConfig':
        return cls(
            timezone=os.getenv('TIMEZONE', 'Asia/Dubai'),
            currency=os.getenv('DEFAULT_CURRENCY', 'AED'),
            vat_rate=float(os.getenv('UAE_VAT_RATE', '5.0'))
        )

@dataclass
class FeatureFlags:
    """Feature flags for enabling/disabling modules"""
    expense_automation: bool
    ticket_automation: bool
    knowledge_base: bool
    project_automation: bool
    analytics: bool
    
    @classmethod
    def from_env(cls) -> 'FeatureFlags':
        return cls(
            expense_automation=os.getenv('ENABLE_EXPENSE_AUTOMATION', 'true').lower() == 'true',
            ticket_automation=os.getenv('ENABLE_TICKET_AUTOMATION', 'true').lower() == 'true',
            knowledge_base=os.getenv('ENABLE_KNOWLEDGE_BASE', 'true').lower() == 'true',
            project_automation=os.getenv('ENABLE_PROJECT_AUTOMATION', 'true').lower() == 'true',
            analytics=os.getenv('ENABLE_ANALYTICS', 'true').lower() == 'true'
        )

@dataclass
class AutomationSettings:
    """Automation behavior settings"""
    auto_organize: bool
    auto_assign: bool
    auto_prioritize: bool
    auto_learn: bool
    
    @classmethod
    def from_env(cls) -> 'AutomationSettings':
        return cls(
            auto_organize=os.getenv('AUTO_ORGANIZE', 'true').lower() == 'true',
            auto_assign=os.getenv('AUTO_ASSIGN_TASKS', 'true').lower() == 'true',
            auto_prioritize=os.getenv('AUTO_PRIORITIZE', 'true').lower() == 'true',
            auto_learn=os.getenv('AUTO_LEARN_FROM_TICKETS', 'true').lower() == 'true'
        )

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.database = DatabaseConfig.from_env()
        self.redis = RedisConfig.from_env()
        self.ai = AIConfig.from_env()
        self.security = SecurityConfig.from_env()
        self.business = BusinessConfig.from_env()
        self.features = FeatureFlags.from_env()
        self.automation = AutomationSettings.from_env()
        
        # Server settings
        self.server_host = os.getenv('AI_SERVER_HOST', '0.0.0.0')
        self.server_port = int(os.getenv('AI_SERVER_PORT', '8444'))
        
        # Logging
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.log_file = os.getenv('LOG_FILE', '/app/logs/ai_assistant.log')
        
        # Rate limiting
        self.rate_limit_per_minute = int(os.getenv('RATE_LIMIT_PER_MINUTE', '60'))
        self.rate_limit_per_hour = int(os.getenv('RATE_LIMIT_PER_HOUR', '1000'))
        
        # Storage paths
        self.upload_path = os.getenv('UPLOAD_PATH', '/app/uploads')
        self.knowledge_path = os.getenv('KNOWLEDGE_PATH', '/app/knowledge')
        self.temp_path = os.getenv('TEMP_PATH', '/tmp')
        
        # Validate configuration
        self._validate()
    
    def _validate(self):
        """Validate configuration settings"""
        errors = []
        
        if not self.ai.api_key:
            errors.append("OPENROUTER_API_KEY is required")
        
        if not self.database.password:
            errors.append("LEAN_DB_PASSWORD is required")
        
        if not self.redis.password:
            logger.warning("REDIS_PASSWORD not set - using unsecured Redis")
        
        if self.security.secret_key == 'default_secret_key':
            logger.warning("Using default SECRET_KEY - not secure for production")
        
        if errors:
            raise ValueError(f"Configuration errors: {'; '.join(errors)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            'database': self.database.__dict__,
            'redis': self.redis.__dict__,
            'ai': {k: v for k, v in self.ai.__dict__.items() if k != 'api_key'},
            'security': {k: v for k, v in self.security.__dict__.items() if k != 'jwt_secret'},
            'business': self.business.__dict__,
            'features': self.features.__dict__,
            'automation': self.automation.__dict__,
            'server': {
                'host': self.server_host,
                'port': self.server_port
            },
            'logging': {
                'level': self.log_level,
                'file': self.log_file
            },
            'rate_limits': {
                'per_minute': self.rate_limit_per_minute,
                'per_hour': self.rate_limit_per_hour
            }
        }
    
    def __str__(self) -> str:
        """String representation (safe for logging)"""
        return json.dumps(self.to_dict(), indent=2)

# Global config instance
config = Config()

if __name__ == "__main__":
    # Test configuration loading
    print("AI Assistant Configuration:")
    print(config)