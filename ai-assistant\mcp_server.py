#!/usr/bin/env python3
"""
Minimal MCP Server for AI Assistant System
Provides core server functionality with health checks and basic routing
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from typing import Dict, Any

from aiohttp import web
import aiohttp_cors

# Import configuration
from config.config import config

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stderr),
        logging.FileHandler(config.log_file) if config.log_file else logging.NullHandler()
    ]
)
logger = logging.getLogger(__name__)

class MCPServer:
    """Main MCP Server class"""
    
    def __init__(self):
        self.app = web.Application()
        self.start_time = datetime.utcnow()
        self.request_count = 0
        self.setup_routes()
        self.setup_cors()
        
    def setup_routes(self):
        """Setup server routes"""
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/status', self.status)
        self.app.router.add_post('/api/v1/process', self.process_request)
        
    def setup_cors(self):
        """Setup CORS for cross-origin requests"""
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def health_check(self, request: web.Request) -> web.Response:
        """Health check endpoint"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    async def status(self, request: web.Request) -> web.Response:
        """Server status endpoint"""
        uptime = (datetime.utcnow() - self.start_time).total_seconds()
        
        return web.json_response({
            'status': 'running',
            'version': '1.0.0',
            'uptime_seconds': uptime,
            'request_count': self.request_count,
            'config': {
                'features': {
                    'expense_automation': config.features.expense_automation,
                    'ticket_automation': config.features.ticket_automation,
                    'knowledge_base': config.features.knowledge_base,
                    'project_automation': config.features.project_automation,
                    'analytics': config.features.analytics
                },
                'automation': {
                    'auto_organize': config.automation.auto_organize,
                    'auto_assign': config.automation.auto_assign,
                    'auto_prioritize': config.automation.auto_prioritize,
                    'auto_learn': config.automation.auto_learn
                }
            }
        })
    
    async def process_request(self, request: web.Request) -> web.Response:
        """Main request processing endpoint"""
        self.request_count += 1
        
        try:
            data = await request.json()
            
            # Basic request validation
            if 'action' not in data:
                return web.json_response(
                    {'error': 'Missing required field: action'},
                    status=400
                )
            
            # Route to appropriate handler
            action = data['action']
            result = await self.route_action(action, data)
            
            return web.json_response({
                'success': True,
                'action': action,
                'result': result
            })
            
        except json.JSONDecodeError:
            return web.json_response(
                {'error': 'Invalid JSON'},
                status=400
            )
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def route_action(self, action: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Route action to appropriate handler"""
        # This will be expanded with actual agent routing
        handlers = {
            'expense.process': self.handle_expense,
            'ticket.process': self.handle_ticket,
            'knowledge.search': self.handle_knowledge_search,
            'workflow.execute': self.handle_workflow
        }
        
        handler = handlers.get(action)
        if not handler:
            raise ValueError(f"Unknown action: {action}")
        
        return await handler(data)
    
    async def handle_expense(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle expense processing request"""
        # Placeholder for expense agent integration
        return {
            'message': 'Expense processing not yet implemented',
            'data': data
        }
    
    async def handle_ticket(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle ticket processing request"""
        # Placeholder for ticket agent integration
        return {
            'message': 'Ticket processing not yet implemented',
            'data': data
        }
    
    async def handle_knowledge_search(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle knowledge base search request"""
        # Placeholder for knowledge base integration
        return {
            'message': 'Knowledge search not yet implemented',
            'query': data.get('query', '')
        }
    
    async def handle_workflow(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle workflow execution request"""
        # Placeholder for workflow engine integration
        return {
            'message': 'Workflow execution not yet implemented',
            'workflow_id': data.get('workflow_id', '')
        }
    
    def run(self):
        """Run the server"""
        logger.info(f"Starting MCP Server on {config.server_host}:{config.server_port}")
        web.run_app(
            self.app,
            host=config.server_host,
            port=config.server_port
        )

def main():
    """Main entry point"""
    try:
        server = MCPServer()
        server.run()
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()