#!/bin/bash
# Integration testing with Leantime database

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

BASE_URL="http://127.0.0.1:8444"

echo "🔗 Integration Testing Round 3 - AI Assistant with Leantime"
echo "=========================================================="

# Get authentication token
echo -e "\n${YELLOW}Getting authentication token...${NC}"
AUTH_RESPONSE=$(curl -s -X POST ${BASE_URL}/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "admin123"}')

if [[ $AUTH_RESPONSE == *"token"* ]]; then
    TOKEN=$(echo $AUTH_RESPONSE | jq -r .token)
    echo -e "${GREEN}✓ Authentication token obtained${NC}"
else
    echo -e "${RED}✗ Failed to get authentication token${NC}"
    exit 1
fi

# Test 1: Database connectivity check
echo -e "\n${YELLOW}Test 1: Database connectivity check${NC}"
DB_TEST=$(podman exec ai-assistant-secure python -c "
import os
import mysql.connector
try:
    conn = mysql.connector.connect(
        host=os.getenv('LEAN_DB_HOST'),
        user=os.getenv('LEAN_DB_USER'), 
        password=os.getenv('LEAN_DB_PASSWORD'),
        database=os.getenv('LEAN_DB_DATABASE')
    )
    cursor = conn.cursor()
    cursor.execute('SHOW TABLES LIMIT 5')
    tables = cursor.fetchall()
    conn.close()
    print(f'SUCCESS: Found {len(tables)} tables')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1)

if [[ $DB_TEST == *"SUCCESS"* ]]; then
    echo -e "${GREEN}✓ Database connection successful${NC}"
    echo -e "  ${DB_TEST}"
else
    echo -e "${RED}✗ Database connection failed${NC}"
    echo -e "  ${DB_TEST}"
fi

# Test 2: Check AI tables exist
echo -e "\n${YELLOW}Test 2: Verify AI tables existence${NC}"
TABLE_CHECK=$(podman exec ai-assistant-secure python -c "
import os
import mysql.connector
try:
    conn = mysql.connector.connect(
        host=os.getenv('LEAN_DB_HOST'),
        user=os.getenv('LEAN_DB_USER'),
        password=os.getenv('LEAN_DB_PASSWORD'), 
        database=os.getenv('LEAN_DB_DATABASE')
    )
    cursor = conn.cursor()
    cursor.execute(\"SHOW TABLES LIKE '%knowledge_base%'\")
    kb_table = cursor.fetchone()
    cursor.execute(\"SHOW TABLES LIKE '%ai_agent_logs%'\")  
    log_table = cursor.fetchone()
    conn.close()
    if kb_table and log_table:
        print('SUCCESS: AI tables exist')
    else:
        print('WARNING: AI tables missing')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1)

if [[ $TABLE_CHECK == *"SUCCESS"* ]]; then
    echo -e "${GREEN}✓ AI tables verified${NC}"
elif [[ $TABLE_CHECK == *"WARNING"* ]]; then
    echo -e "${YELLOW}⚠ AI tables missing - running migration${NC}"
    # Apply migration
    ssh root@************* "podman exec mysql_leantime_podman mysql -u root -e 'CREATE DATABASE IF NOT EXISTS leantime;' 2>/dev/null || true"
else
    echo -e "${RED}✗ Database table check failed${NC}"
fi

# Test 3: Leantime API simulation
echo -e "\n${YELLOW}Test 3: Leantime API simulation${NC}"
LEANTIME_RESPONSE=$(curl -s -X POST ${BASE_URL}/api/v1/process \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
        "action": "knowledge.search",
        "data": {
            "query": "How to create a project in Leantime",
            "category": "project_management"
        }
    }')

if [[ $LEANTIME_RESPONSE == *"success"* ]]; then
    echo -e "${GREEN}✓ Leantime API simulation successful${NC}"
else
    echo -e "${YELLOW}⚠ Leantime API simulation returned: ${LEANTIME_RESPONSE}${NC}"
fi

# Test 4: Container network connectivity
echo -e "\n${YELLOW}Test 4: Container network connectivity${NC}"
NETWORK_TEST=$(podman exec ai-assistant-secure python -c "
import socket
import os

# Test MySQL connection
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex((os.getenv('LEAN_DB_HOST'), 3306))
    sock.close()
    if result == 0:
        print('MySQL: CONNECTED')
    else:
        print('MySQL: FAILED')
except Exception as e:
    print(f'MySQL: ERROR - {e}')

# Test Redis connection  
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex((os.getenv('REDIS_HOST', 'redis_podman'), 6379))
    sock.close()
    if result == 0:
        print('Redis: CONNECTED')
    else:
        print('Redis: FAILED') 
except Exception as e:
    print(f'Redis: ERROR - {e}')
" 2>&1)

echo -e "${GREEN}Network connectivity:${NC}"
echo "$NETWORK_TEST" | while read line; do
    if [[ $line == *"CONNECTED"* ]]; then
        echo -e "  ${GREEN}✓ $line${NC}"
    elif [[ $line == *"FAILED"* ]]; then
        echo -e "  ${RED}✗ $line${NC}"
    else
        echo -e "  ${YELLOW}⚠ $line${NC}"
    fi
done

# Test 5: JWT token validation
echo -e "\n${YELLOW}Test 5: JWT token validation and refresh${NC}"
TOKEN_VALIDATION=$(curl -s -X POST ${BASE_URL}/auth/refresh \
    -H "Authorization: Bearer $TOKEN")

if [[ $TOKEN_VALIDATION == *"token"* ]]; then
    echo -e "${GREEN}✓ Token refresh successful${NC}"
    NEW_TOKEN=$(echo $TOKEN_VALIDATION | jq -r .token)
    
    # Test with new token
    NEW_TOKEN_TEST=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/api/v1/process \
        -H "Authorization: Bearer $NEW_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"action": "knowledge.search", "data": {"query": "test"}}' -o /dev/null)
    
    if [[ $NEW_TOKEN_TEST == "200" ]]; then
        echo -e "${GREEN}✓ New token works correctly${NC}"
    else
        echo -e "${RED}✗ New token failed${NC}"
    fi
else
    echo -e "${RED}✗ Token refresh failed${NC}"
fi

# Test 6: Error handling
echo -e "\n${YELLOW}Test 6: Error handling validation${NC}"

# Test with malformed JSON
MALFORMED_TEST=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/api/v1/process \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"action": "test", malformed}' -o /dev/null)

if [[ $MALFORMED_TEST == "400" ]]; then
    echo -e "${GREEN}✓ Malformed JSON rejected (400)${NC}"
else
    echo -e "${RED}✗ Malformed JSON not handled properly (got ${MALFORMED_TEST})${NC}"
fi

# Test with unauthorized action
UNAUTH_ACTION=$(curl -s -X POST ${BASE_URL}/api/v1/process \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"action": "system.shutdown"}')

if [[ $UNAUTH_ACTION == *"Unauthorized action"* ]]; then
    echo -e "${GREEN}✓ Unauthorized action blocked${NC}"
else
    echo -e "${YELLOW}⚠ Unauthorized action response: ${UNAUTH_ACTION}${NC}"
fi

# Test 7: Resource monitoring during load
echo -e "\n${YELLOW}Test 7: Resource monitoring during load${NC}"
CONTAINER_NAME="ai-assistant-secure"

# Generate some load
echo "Generating load..."
for i in {1..20}; do
    curl -s -X POST ${BASE_URL}/api/v1/process \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"action": "knowledge.search", "data": {"query": "load test '$i'"}}' > /dev/null &
done

sleep 3

# Check resources
LOAD_STATS=$(podman stats --no-stream --format "json" $CONTAINER_NAME | jq '.[0]')
LOAD_MEMORY=$(echo $LOAD_STATS | jq -r '.MemPerc' | sed 's/%//')
LOAD_CPU=$(echo $LOAD_STATS | jq -r '.CPU' | sed 's/%//')

wait  # Wait for background jobs

echo -e "${GREEN}Under load - Memory: ${LOAD_MEMORY}%, CPU: ${LOAD_CPU}%${NC}"

if (( $(echo "$LOAD_MEMORY < 80" | bc -l) )); then
    echo -e "${GREEN}✓ Memory usage under control during load${NC}"
else
    echo -e "${RED}✗ High memory usage during load${NC}"
fi

# Test 8: Security header validation
echo -e "\n${YELLOW}Test 8: Security headers (detailed check)${NC}"
HEADERS_RESPONSE=$(curl -sI ${BASE_URL}/health)

SECURITY_HEADERS=(
    "X-Content-Type-Options: nosniff"
    "X-Frame-Options: DENY" 
    "X-XSS-Protection: 1; mode=block"
    "Content-Security-Policy: default-src 'self'"
    "Strict-Transport-Security"
)

for header in "${SECURITY_HEADERS[@]}"; do
    if echo "$HEADERS_RESPONSE" | grep -q "$header"; then
        echo -e "${GREEN}✓ $header${NC}"
    else
        echo -e "${RED}✗ Missing: $header${NC}"
    fi
done

echo -e "\n${GREEN}Integration Testing Round 3 Complete!${NC}"
echo -e "\n${YELLOW}Integration Summary:${NC}"
echo -e "  Database Connectivity: $(if [[ $DB_TEST == *"SUCCESS"* ]]; then echo '✓'; else echo '✗'; fi)"
echo -e "  Network Connectivity: $(if echo "$NETWORK_TEST" | grep -q "CONNECTED"; then echo '✓'; else echo '✗'; fi)" 
echo -e "  Token Management: $(if [[ $TOKEN_VALIDATION == *"token"* ]]; then echo '✓'; else echo '✗'; fi)"
echo -e "  Error Handling: $(if [[ $MALFORMED_TEST == "400" ]]; then echo '✓'; else echo '✗'; fi)"
echo -e "  Resource Usage: Memory ${LOAD_MEMORY}%, CPU ${LOAD_CPU}%"