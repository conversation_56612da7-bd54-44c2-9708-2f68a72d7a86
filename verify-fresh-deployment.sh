#!/bin/bash

# Verify Fresh Leantime Deployment
# Run this after fresh-start-deploy.sh to verify everything is working

echo "🔍 Verifying Fresh Leantime Deployment"
echo "======================================"
echo "📅 $(date)"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

TESTS_PASSED=0
TOTAL_TESTS=8

echo -e "${BLUE}🧪 Test 1: Container Status${NC}"
echo "============================"

CONTAINERS=("mysql_leantime_podman" "redis_email" "leantime-email-service" "leantime_podman")
for container in "${CONTAINERS[@]}"; do
    STATUS=$(podman ps --format "{{.Names}}\t{{.Status}}" | grep "$container" || echo "NOT_FOUND")
    if [[ "$STATUS" == "NOT_FOUND" ]]; then
        echo -e "${RED}❌ $container: Not running${NC}"
    else
        echo -e "${GREEN}✅ $container: Running${NC}"
    fi
done

if podman ps | grep -q "leantime_podman.*Up"; then
    ((TESTS_PASSED++))
    echo -e "${GREEN}✅ Test 1 PASSED: All containers running${NC}"
else
    echo -e "${RED}❌ Test 1 FAILED: Some containers not running${NC}"
fi
echo ""

echo -e "${BLUE}🧪 Test 2: Database Connectivity${NC}"
echo "==============================="

DB_TEST=$(podman exec mysql_leantime_podman mysql -u lean -pSecureDBPass2024! leantime -e "SELECT 1;" 2>/dev/null || echo "FAILED")
if [[ "$DB_TEST" == "FAILED" ]]; then
    echo -e "${RED}❌ Database connection failed${NC}"
else
    echo -e "${GREEN}✅ Database connection successful${NC}"
    ((TESTS_PASSED++))
fi
echo ""

echo -e "${BLUE}🧪 Test 3: Email Service Health${NC}"
echo "=============================="

EMAIL_HEALTH=$(curl -s http://localhost:3001/health 2>/dev/null | grep "healthy" || echo "FAILED")
if [[ "$EMAIL_HEALTH" == "FAILED" ]]; then
    echo -e "${RED}❌ Email service health check failed${NC}"
else
    echo -e "${GREEN}✅ Email service is healthy${NC}"
    ((TESTS_PASSED++))
fi
echo ""

echo -e "${BLUE}🧪 Test 4: Leantime Web Response${NC}"
echo "==============================="

WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8090/ 2>/dev/null || echo "FAILED")
if [[ "$WEB_STATUS" == "200" ]] || [[ "$WEB_STATUS" == "302" ]]; then
    echo -e "${GREEN}✅ Leantime web interface responding (HTTP $WEB_STATUS)${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ Leantime web interface not responding (HTTP $WEB_STATUS)${NC}"
fi
echo ""

echo -e "${BLUE}🧪 Test 5: Email API Test${NC}"
echo "========================="

EMAIL_API_TEST=$(curl -s -X POST http://localhost:3001/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Deployment Verification Test",
    "html": "<h2>✅ Verification Test</h2><p>Email service is working correctly!</p>"
  }' 2>/dev/null | grep "success.*true" || echo "FAILED")

if [[ "$EMAIL_API_TEST" == "FAILED" ]]; then
    echo -e "${RED}❌ Email API test failed${NC}"
else
    echo -e "${GREEN}✅ Email API test successful${NC}"
    echo "📧 Verification email <NAME_EMAIL>"
    ((TESTS_PASSED++))
fi
echo ""

echo -e "${BLUE}🧪 Test 6: Network Connectivity${NC}"
echo "==============================="

# Test Leantime to MySQL
LEANTIME_TO_DB=$(podman exec leantime_podman nc -z mysql_leantime_podman 3306 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "Leantime → MySQL: $LEANTIME_TO_DB"

# Test Leantime to Email Service
LEANTIME_TO_EMAIL=$(podman exec leantime_podman nc -z leantime-email-service 2525 2>/dev/null && echo "SUCCESS" || echo "FAILED")
echo "Leantime → Email Service: $LEANTIME_TO_EMAIL"

if [[ "$LEANTIME_TO_DB" == "SUCCESS" ]] && [[ "$LEANTIME_TO_EMAIL" == "SUCCESS" ]]; then
    echo -e "${GREEN}✅ Network connectivity test passed${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ Network connectivity issues detected${NC}"
fi
echo ""

echo -e "${BLUE}🧪 Test 7: External Domain Access${NC}"
echo "==============================="

EXTERNAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 --max-time 30 https://admin.dxbmeta.com/ 2>/dev/null || echo "FAILED")
if [[ "$EXTERNAL_STATUS" == "200" ]] || [[ "$EXTERNAL_STATUS" == "302" ]] || [[ "$EXTERNAL_STATUS" == "303" ]]; then
    echo -e "${GREEN}✅ External domain accessible (HTTP $EXTERNAL_STATUS)${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${YELLOW}⚠️  External domain test: $EXTERNAL_STATUS${NC}"
    echo "   This may be normal if nginx/proxy needs configuration"
fi
echo ""

echo -e "${BLUE}🧪 Test 8: Installation Status${NC}"
echo "=============================="

# Check if Leantime shows install page or login page
LOCAL_RESPONSE=$(curl -s -L http://localhost:8090/ 2>/dev/null | head -20)
if echo "$LOCAL_RESPONSE" | grep -qi "install"; then
    echo -e "${GREEN}✅ Leantime ready for fresh installation${NC}"
    echo "🔧 Next: Complete setup at https://admin.dxbmeta.com"
    ((TESTS_PASSED++))
elif echo "$LOCAL_RESPONSE" | grep -qi "login"; then
    echo -e "${GREEN}✅ Leantime already configured - ready to use${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${YELLOW}⚠️  Leantime status unclear - manual check needed${NC}"
fi
echo ""

echo -e "${BLUE}📊 Verification Summary${NC}"
echo "======================="

echo -e "${BLUE}Tests Passed: $TESTS_PASSED/$TOTAL_TESTS${NC}"

if [[ $TESTS_PASSED -ge 7 ]]; then
    echo -e "${GREEN}🎉 EXCELLENT: Deployment is working perfectly!${NC}"
    echo ""
    echo -e "${GREEN}✅ All systems operational${NC}"
    echo -e "${GREEN}✅ Email service ready${NC}"
    echo -e "${GREEN}✅ Database connected${NC}"
    echo -e "${GREEN}✅ Web interface accessible${NC}"
    echo ""
    echo -e "${BLUE}🚀 Ready for Production Use!${NC}"
    
elif [[ $TESTS_PASSED -ge 5 ]]; then
    echo -e "${YELLOW}⚠️  GOOD: Deployment mostly working${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Minor issues detected - review failed tests above${NC}"
    echo -e "${GREEN}✅ Core functionality operational${NC}"
    echo ""
    echo -e "${BLUE}📧 Email testing can proceed${NC}"
    
else
    echo -e "${RED}❌ ISSUES: Deployment needs attention${NC}"
    echo ""
    echo -e "${RED}🚨 Multiple failures detected${NC}"
    echo -e "${YELLOW}🔧 Review logs and fix issues before proceeding${NC}"
fi

echo ""
echo -e "${BLUE}🔗 Access Information${NC}"
echo "===================="
echo "🌐 Web Interface:"
echo "   • Primary: https://admin.dxbmeta.com"
echo "   • Direct: http://$(hostname -I | awk '{print $1}'):8090"
echo ""
echo "📧 Email Service:"
echo "   • Health: http://$(hostname -I | awk '{print $1}'):3001/health"
echo "   • API: http://$(hostname -I | awk '{print $1}'):3001/send"
echo ""
echo "🔧 Management:"
echo "   • Container status: podman ps"
echo "   • Leantime logs: podman logs leantime_podman"
echo "   • Email logs: podman logs leantime-email-service"
echo "   • Database logs: podman logs mysql_leantime_podman"

echo ""
echo -e "${BLUE}📋 Next Steps${NC}"
echo "============="

if [[ $TESTS_PASSED -ge 6 ]]; then
    echo "1. 🌐 Access https://admin.dxbmeta.com"
    echo "2. 👤 Complete Leantime installation/setup"
    echo "3. 📧 Create admin user: <EMAIL>"
    echo "4. 🔐 Test password reset functionality"
    echo "5. ✅ Verify email delivery"
    echo ""
    echo -e "${GREEN}🎯 Your fresh Leantime installation is ready!${NC}"
else
    echo "1. 📋 Review failed tests above"
    echo "2. 🔍 Check container logs for errors"
    echo "3. 🔧 Fix identified issues"
    echo "4. 🔄 Re-run verification"
    echo ""
    echo -e "${YELLOW}🔧 Address issues before proceeding${NC}"
fi

echo ""
echo "✅ Verification completed at $(date)"
