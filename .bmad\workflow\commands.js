#!/usr/bin/env node

/**
 * BMAD Workflow Commands
 * CLI commands for BMAD-Claude integrated framework
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const agentFactory = require('../agents/factory');

class BMADCommands {
  constructor() {
    this.configPath = path.join(process.cwd(), '.bmad', 'config.json');
    this.storiesPath = path.join(process.cwd(), '.bmad', 'stories');
    this.docsPath = path.join(process.cwd(), '.bmad', 'docs');
  }

  /**
   * Initialize BMAD project structure
   */
  async init(options = {}) {
    console.log('🚀 Initializing BMAD project structure...');
    
    const directories = [
      '.bmad',
      '.bmad/agents',
      '.bmad/agents/planning',
      '.bmad/agents/development',
      '.bmad/agents/orchestration',
      '.bmad/templates',
      '.bmad/stories',
      '.bmad/docs'
    ];

    for (const dir of directories) {
      await fs.mkdir(path.join(process.cwd(), dir), { recursive: true });
    }

    // Copy default config if not exists
    if (!await this.fileExists(this.configPath)) {
      await this.copyDefaultConfig();
    }

    console.log('✅ BMAD project initialized successfully');
    return { success: true, message: 'BMAD project initialized' };
  }

  /**
   * Run planning phase with AI agents
   */
  async plan(options = {}) {
    console.log('📋 Starting BMAD Planning Phase...');
    
    const config = await this.loadConfig();
    if (!config.phases.planning.enabled) {
      throw new Error('Planning phase is disabled in config');
    }

    const results = {
      requirements: null,
      prd: null,
      architecture: null
    };

    // Step 1: Requirements Analysis
    console.log('1️⃣ Spawning Requirements Analyst...');
    const analyst = await agentFactory.createAgent('analyst', {
      task: options.task || 'Analyze project requirements',
      projectContext: options.context,
      useClaudeFlow: true
    });
    results.requirements = await this.saveOutput('requirements', analyst);

    // Step 2: Product Requirements Document
    console.log('2️⃣ Spawning Product Manager...');
    const pm = await agentFactory.createAgent('product-manager', {
      task: 'Create PRD based on requirements',
      projectContext: {
        requirements: results.requirements,
        ...options.context
      },
      useClaudeFlow: true
    });
    results.prd = await this.saveOutput('prd', pm);

    // Step 3: Architecture Design
    console.log('3️⃣ Spawning System Architect...');
    const architect = await agentFactory.createAgent('architect', {
      task: 'Design system architecture',
      projectContext: {
        requirements: results.requirements,
        prd: results.prd,
        ...options.context
      },
      useClaudeFlow: true
    });
    results.architecture = await this.saveOutput('architecture', architect);

    console.log('✅ Planning phase completed successfully');
    return results;
  }

  /**
   * Execute context-engineered development
   */
  async develop(options = {}) {
    console.log('💻 Starting BMAD Development Phase...');
    
    const config = await this.loadConfig();
    if (!config.phases.development.enabled) {
      throw new Error('Development phase is disabled in config');
    }

    // Load planning documents
    const requirements = await this.loadDocument('requirements');
    const prd = await this.loadDocument('prd');
    const architecture = await this.loadDocument('architecture');

    // Step 1: Generate Context-Engineered Stories
    console.log('1️⃣ Spawning Scrum Master for story generation...');
    const scrumMaster = await agentFactory.createAgent('scrum-master', {
      task: 'Generate context-engineered development stories',
      projectContext: {
        requirements,
        prd,
        architecture,
        ...options.context
      },
      useClaudeFlow: true
    });

    const stories = await agentFactory.generateStory(requirements, architecture, {
      title: options.storyTitle || 'Development Story',
      implementation: options.implementation,
      criteria: options.criteria
    });

    await this.saveStories(stories);

    // Step 2: Create Development Swarm
    if (options.swarm) {
      console.log('2️⃣ Creating development swarm...');
      const swarm = await agentFactory.createSwarm([
        { type: 'coder', options: { task: 'Implement features' }},
        { type: 'code-reviewer', options: { task: 'Review code' }},
        { type: 'tester', options: { task: 'Test implementation' }}
      ], 'mesh');

      console.log(`✅ Development swarm created with ${swarm.agents.length} agents`);
    }

    console.log('✅ Development phase initiated');
    return { stories, scrumMaster };
  }

  /**
   * Create a specific type of agent
   */
  async agent(action, type, options = {}) {
    console.log(`🤖 Agent command: ${action} ${type}`);

    switch(action) {
      case 'create':
      case 'spawn':
        const agent = await agentFactory.createAgent(type, options);
        console.log(`✅ Agent created: ${agent.id}`);
        return agent;

      case 'list':
        const agents = type === 'active' 
          ? agentFactory.getActiveAgents()
          : agentFactory.getAvailableAgents();
        console.log(`📋 ${type === 'active' ? 'Active' : 'Available'} agents:`);
        agents.forEach(a => console.log(`  - ${a.name || a.type}: ${a.role || a.status}`));
        return agents;

      case 'terminate':
        const terminated = await agentFactory.terminateAgent(type);
        console.log(`✅ Agent terminated: ${terminated.id}`);
        return terminated;

      default:
        throw new Error(`Unknown agent action: ${action}`);
    }
  }

  /**
   * Validate the current phase outputs
   */
  async validate(phase) {
    console.log(`🔍 Validating ${phase} phase outputs...`);

    const validators = {
      planning: ['requirements', 'prd', 'architecture'],
      development: ['stories', 'code', 'tests'],
      validation: ['test-results', 'coverage', 'performance']
    };

    const required = validators[phase];
    if (!required) {
      throw new Error(`Unknown phase: ${phase}`);
    }

    const results = {};
    for (const doc of required) {
      const exists = await this.documentExists(doc);
      results[doc] = exists;
      console.log(`  ${exists ? '✅' : '❌'} ${doc}`);
    }

    const allValid = Object.values(results).every(v => v);
    console.log(allValid ? '✅ All validations passed' : '❌ Some validations failed');
    
    return results;
  }

  // Helper methods
  async loadConfig() {
    try {
      const content = await fs.readFile(this.configPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Failed to load BMAD config: ${error.message}`);
    }
  }

  async saveOutput(type, agent) {
    const outputPath = path.join(this.docsPath, `${type}_${Date.now()}.md`);
    const content = `# ${type.toUpperCase()} Document\n\nGenerated by: ${agent.id}\n\nDate: ${new Date().toISOString()}\n\n## Content\n\n[Agent output would be captured here]`;
    
    await fs.writeFile(outputPath, content);
    return outputPath;
  }

  async saveStories(stories) {
    const storyPath = path.join(this.storiesPath, `story_${stories.id}.json`);
    await fs.writeFile(storyPath, JSON.stringify(stories, null, 2));
    return storyPath;
  }

  async loadDocument(type) {
    const files = await fs.readdir(this.docsPath);
    const docFile = files.find(f => f.startsWith(type));
    if (docFile) {
      return await fs.readFile(path.join(this.docsPath, docFile), 'utf-8');
    }
    return null;
  }

  async documentExists(type) {
    const files = await fs.readdir(this.docsPath).catch(() => []);
    return files.some(f => f.startsWith(type));
  }

  async fileExists(filepath) {
    try {
      await fs.access(filepath);
      return true;
    } catch {
      return false;
    }
  }

  async copyDefaultConfig() {
    const defaultConfig = require('../config.json');
    await fs.writeFile(this.configPath, JSON.stringify(defaultConfig, null, 2));
  }
}

// CLI handling
if (require.main === module) {
  const bmad = new BMADCommands();
  const [,, command, ...args] = process.argv;

  const commands = {
    init: () => bmad.init(),
    plan: () => bmad.plan({ task: args[0] }),
    develop: () => bmad.develop({ swarm: args.includes('--swarm') }),
    agent: () => bmad.agent(args[0], args[1], { task: args[2] }),
    validate: () => bmad.validate(args[0] || 'planning')
  };

  if (commands[command]) {
    commands[command]()
      .then(result => {
        if (result && typeof result === 'object') {
          console.log('\nResult:', JSON.stringify(result, null, 2));
        }
      })
      .catch(error => {
        console.error('❌ Error:', error.message);
        process.exit(1);
      });
  } else {
    console.log(`
BMAD-Claude Framework Commands:

  bmad init                    Initialize BMAD project structure
  bmad plan [task]            Run planning phase with AI agents
  bmad develop [--swarm]      Execute context-engineered development
  bmad agent <action> <type>  Manage AI agents
  bmad validate [phase]       Validate phase outputs

Agent Actions:
  bmad agent create <type>    Create a new agent
  bmad agent list active      List active agents
  bmad agent list available   List available agent types
  bmad agent terminate <id>   Terminate an agent

Examples:
  bmad init
  bmad plan "Build e-commerce platform"
  bmad develop --swarm
  bmad agent create analyst
  bmad validate planning
    `);
  }
}

module.exports = BMADCommands;