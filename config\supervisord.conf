[supervisord]
nodaemon=true
logfile=/dev/stdout
loglevel=info
logfile_maxbytes=0
pidfile=/run/supervisord.pid

[program:php-fpm]
command=php-fpm -F
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
priority=5
startretries=10

[program:nginx]
command=nginx -g 'daemon off;'
autostart=true
autorestart=true
priority=10

[program:scheduler]
command=php /var/www/html/bin/leantime schedule:work
autostart=true
autorestart=true
user=www-data
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
redirect_stderr=true
