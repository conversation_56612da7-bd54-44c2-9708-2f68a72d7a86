#!/usr/bin/env python3
"""
Leantime BAML Integration Layer
Provides Python interface for AI-powered project management features
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

import mysql.connector
import redis
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Import generated BAML client
from baml_client import baml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
class Config:
    MYSQL_HOST = os.getenv('LEAN_DB_HOST', 'mysql_leantime')
    MYSQL_USER = os.getenv('LEAN_DB_USER', 'lean')
    MYSQL_PASSWORD = os.getenv('LEAN_DB_PASSWORD', '')
    MYSQL_DATABASE = os.getenv('LEAN_DB_DATABASE', 'leantime')
    REDIS_URL = os.getenv('REDIS_URL', 'redis://redis:6379')
    BAML_CACHE_TTL = int(os.getenv('BAML_CACHE_TTL', '3600'))
    LOG_LEVEL = os.getenv('BAML_LOG_LEVEL', 'INFO')

# Pydantic Models for API
class ProjectAnalysisRequest(BaseModel):
    project_id: int
    include_predictions: bool = True
    analysis_types: List[str] = Field(default=['health', 'risks', 'performance'])
    time_range_days: int = 30

class TaskPrioritizationRequest(BaseModel):
    project_id: int
    task_ids: List[int] = None
    prioritization_criteria: Dict[str, float] = Field(default={
        'business_value': 0.4,
        'technical_risk': 0.3,
        'effort_efficiency': 0.3
    })

class SprintPlanningRequest(BaseModel):
    project_id: int
    sprint_goal: str
    sprint_duration_days: int = 14
    team_capacity_hours: float
    constraints: Dict[str, Any] = {}

# Database Connection Manager
class DatabaseManager:
    def __init__(self):
        self.pool = None
        
    async def initialize(self):
        """Initialize database connection pool"""
        try:
            self.pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name="leantime_baml",
                pool_size=10,
                pool_reset_session=True,
                host=Config.MYSQL_HOST,
                user=Config.MYSQL_USER,
                password=Config.MYSQL_PASSWORD,
                database=Config.MYSQL_DATABASE,
                autocommit=True
            )
            logger.info("Database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def get_connection(self):
        """Get database connection from pool"""
        if not self.pool:
            raise RuntimeError("Database not initialized")
        return self.pool.get_connection()

# Cache Manager
class CacheManager:
    def __init__(self):
        self.redis_client = None
        
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(Config.REDIS_URL, decode_responses=True)
            await self.redis_client.ping()
            logger.info("Redis connection initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            raise
    
    async def get(self, key: str) -> Optional[Dict]:
        """Get cached value"""
        try:
            cached = await self.redis_client.get(key)
            return json.loads(cached) if cached else None
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
            return None
    
    async def set(self, key: str, value: Dict, ttl: int = Config.BAML_CACHE_TTL):
        """Set cached value"""
        try:
            await self.redis_client.setex(key, ttl, json.dumps(value))
        except Exception as e:
            logger.warning(f"Cache set error: {e}")

# Leantime Data Access Layer
class LeantimeDAO:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_project(self, project_id: int) -> Optional[Dict]:
        """Retrieve project data from Leantime"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute("""
                    SELECT p.*, 
                           COUNT(DISTINCT t.id) as task_count,
                           COUNT(DISTINCT CASE WHEN t.status = 3 THEN t.id END) as completed_tasks,
                           SUM(t.planHours) as planned_hours,
                           SUM(t.realHours) as actual_hours
                    FROM zp_projects p
                    LEFT JOIN zp_tickets t ON p.id = t.projectId
                    WHERE p.id = %s AND p.status != 'archived'
                    GROUP BY p.id
                """, (project_id,))
                
                project = cursor.fetchone()
                if not project:
                    return None
                
                # Get project milestones
                cursor.execute("""
                    SELECT * FROM zp_tickets 
                    WHERE projectId = %s AND type = 'milestone'
                    ORDER BY sortindex, dateToFinish
                """, (project_id,))
                project['milestones'] = cursor.fetchall()
                
                # Get project tasks
                cursor.execute("""
                    SELECT t.*, u.firstname, u.lastname, u.user as assignee_name
                    FROM zp_tickets t
                    LEFT JOIN zp_user u ON t.editorId = u.id
                    WHERE t.projectId = %s AND t.type != 'milestone'
                    ORDER BY t.sortindex, t.date
                """, (project_id,))
                project['tasks'] = cursor.fetchall()
                
                # Get team members
                cursor.execute("""
                    SELECT DISTINCT u.id, u.firstname, u.lastname, u.user, u.role
                    FROM zp_user u
                    JOIN zp_projectassignment pa ON u.id = pa.userId
                    WHERE pa.projectId = %s
                """, (project_id,))
                project['team'] = cursor.fetchall()
                
                return project
                
        except Exception as e:
            logger.error(f"Error fetching project {project_id}: {e}")
            return None
    
    def get_team_workload(self, project_id: int) -> Dict[int, Dict]:
        """Get current workload for team members"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute("""
                    SELECT 
                        u.id as user_id,
                        u.firstname,
                        u.lastname,
                        COUNT(t.id) as active_tasks,
                        SUM(CASE WHEN t.status IN (1,2) THEN t.planHours ELSE 0 END) as planned_hours,
                        SUM(t.realHours) as actual_hours,
                        AVG(CASE WHEN t.status = 3 THEN 
                            DATEDIFF(t.dateToFinish, t.date) 
                            ELSE NULL END) as avg_completion_days
                    FROM zp_user u
                    JOIN zp_projectassignment pa ON u.id = pa.userId
                    LEFT JOIN zp_tickets t ON u.id = t.editorId AND t.projectId = %s
                    WHERE pa.projectId = %s
                    GROUP BY u.id
                """, (project_id, project_id))
                
                workload_data = {}
                for row in cursor.fetchall():
                    workload_data[row['user_id']] = {
                        'name': f"{row['firstname']} {row['lastname']}",
                        'active_tasks': row['active_tasks'],
                        'planned_hours': float(row['planned_hours'] or 0),
                        'actual_hours': float(row['actual_hours'] or 0),
                        'avg_completion_days': float(row['avg_completion_days'] or 0)
                    }
                
                return workload_data
                
        except Exception as e:
            logger.error(f"Error fetching team workload: {e}")
            return {}
    
    def get_historical_tasks(self, project_id: int, limit: int = 50) -> List[Dict]:
        """Get historical task data for analysis"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute("""
                    SELECT 
                        t.*,
                        DATEDIFF(t.dateToFinish, t.date) as duration_days,
                        (t.realHours / NULLIF(t.planHours, 0)) as estimation_accuracy
                    FROM zp_tickets t
                    WHERE t.projectId = %s 
                    AND t.status = 3 
                    AND t.type != 'milestone'
                    AND t.planHours > 0
                    ORDER BY t.dateToFinish DESC
                    LIMIT %s
                """, (project_id, limit))
                
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Error fetching historical tasks: {e}")
            return []
    
    def store_baml_insight(self, project_id: int, insight_type: str, data: Dict, confidence: float = 1.0):
        """Store BAML-generated insight in database"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO baml_insights 
                    (project_id, insight_type, data, confidence_score, created_at, expires_at)
                    VALUES (%s, %s, %s, %s, NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR))
                """, (project_id, insight_type, json.dumps(data), confidence))
                
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"Error storing BAML insight: {e}")
            return None

# BAML Service Layer
class BAMLService:
    def __init__(self, dao: LeantimeDAO, cache: CacheManager):
        self.dao = dao
        self.cache = cache
    
    async def analyze_project_health(self, project_id: int, options: Dict = None) -> Dict:
        """Analyze project health using BAML"""
        cache_key = f"project_health:{project_id}:{hash(str(options))}"
        
        # Check cache first
        cached = await self.cache.get(cache_key)
        if cached:
            return cached
        
        try:
            # Gather project data
            project = self.dao.get_project(project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            team_workload = self.dao.get_team_workload(project_id)
            historical_tasks = self.dao.get_historical_tasks(project_id)
            
            # Prepare data for BAML
            project_data = {
                'id': project['id'],
                'name': project['name'],
                'status': project['state'],
                'planned_hours': float(project['planned_hours'] or 0),
                'actual_hours': float(project['actual_hours'] or 0),
                'task_count': project['task_count'],
                'completed_tasks': project['completed_tasks'],
                'completion_rate': (project['completed_tasks'] / max(project['task_count'], 1)),
                'milestones': project['milestones'],
                'tasks': project['tasks']
            }
            
            team_metrics = {
                'workload_distribution': team_workload,
                'team_size': len(project['team']),
                'avg_task_completion': sum(w.get('avg_completion_days', 0) for w in team_workload.values()) / max(len(team_workload), 1)
            }
            
            timeline_data = {
                'start_date': str(project.get('created', datetime.now())),
                'planned_end': str(project.get('dateToFinish', datetime.now() + timedelta(days=30))),
                'historical_performance': historical_tasks
            }
            
            # Call BAML function
            result = await baml.AnalyzeProjectHealth(
                project_data=project_data,
                team_metrics=team_metrics,
                timeline_data=timeline_data
            )
            
            # Store insight in database
            self.dao.store_baml_insight(
                project_id, 
                'project_health', 
                asdict(result) if hasattr(result, '__dataclass_fields__') else dict(result)
            )
            
            # Cache result
            result_dict = asdict(result) if hasattr(result, '__dataclass_fields__') else dict(result)
            await self.cache.set(cache_key, result_dict)
            
            return result_dict
            
        except Exception as e:
            logger.error(f"Error analyzing project health: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def prioritize_tasks(self, project_id: int, task_ids: List[int] = None, criteria: Dict = None) -> Dict:
        """Prioritize tasks using AI analysis"""
        cache_key = f"task_prioritization:{project_id}:{hash(str(task_ids))}:{hash(str(criteria))}"
        
        cached = await self.cache.get(cache_key)
        if cached:
            return cached
        
        try:
            project = self.dao.get_project(project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Filter tasks if task_ids provided
            tasks = project['tasks']
            if task_ids:
                tasks = [t for t in tasks if t['id'] in task_ids]
            
            # Get business value estimates (simplified for demo)
            business_value = {str(task['id']): self._estimate_business_value(task) for task in tasks}
            
            # Get technical dependencies
            dependencies = {str(task['id']): self._get_task_dependencies(task) for task in tasks}
            
            # Resource constraints
            team_capacity = self.dao.get_team_workload(project_id)
            resource_constraints = {
                'total_capacity_hours': sum(w.get('planned_hours', 40) for w in team_capacity.values()),
                'available_skills': list(set(member.get('role', '') for member in project['team']))
            }
            
            # Call BAML function
            result = await baml.PrioritizeTaskBacklog(
                backlog_tasks=tasks,
                project_goals=[project.get('details', '').split('\n')[0] if project.get('details') else 'Project delivery'],
                business_value=business_value,
                technical_dependencies=dependencies,
                resource_constraints=resource_constraints,
                stakeholder_input={'client_priority': 'high', 'timeline_pressure': 'medium'}
            )
            
            result_dict = asdict(result) if hasattr(result, '__dataclass_fields__') else dict(result)
            await self.cache.set(cache_key, result_dict)
            
            return result_dict
            
        except Exception as e:
            logger.error(f"Error prioritizing tasks: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def plan_sprint(self, project_id: int, sprint_data: Dict) -> Dict:
        """Plan sprint using AI optimization"""
        try:
            project = self.dao.get_project(project_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            
            # Get available tasks for sprint
            available_tasks = [t for t in project['tasks'] if t['status'] in [0, 1]]  # New or In Progress
            
            # Calculate team velocity from historical data
            historical_tasks = self.dao.get_historical_tasks(project_id, 20)
            avg_completion_time = sum(t.get('duration_days', 0) for t in historical_tasks) / max(len(historical_tasks), 1)
            
            velocity_data = {
                'avg_tasks_per_sprint': len(historical_tasks) / 4,  # Assuming 4 sprints
                'avg_completion_days': avg_completion_time,
                'estimation_accuracy': sum(t.get('estimation_accuracy', 1.0) for t in historical_tasks) / max(len(historical_tasks), 1)
            }
            
            team_capacity = {
                str(member['id']): 40.0  # Assuming 40 hours per sprint per person
                for member in project['team']
            }
            
            constraints = {
                'sprint_duration': sprint_data.get('sprint_duration_days', 14),
                'team_availability': 0.8,  # 80% availability
                'quality_gates': True
            }
            
            # Call BAML function
            result = await baml.PlanOptimalSprint(
                backlog_tasks=available_tasks,
                team_velocity=velocity_data,
                sprint_goals=[sprint_data.get('sprint_goal', 'Sprint delivery')],
                team_capacity=team_capacity,
                constraints=constraints
            )
            
            return asdict(result) if hasattr(result, '__dataclass_fields__') else dict(result)
            
        except Exception as e:
            logger.error(f"Error planning sprint: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _estimate_business_value(self, task: Dict) -> float:
        """Simple business value estimation based on task attributes"""
        value = 0.5  # Base value
        
        # Priority weighting
        priority_weights = {'low': 0.3, 'medium': 0.6, 'high': 0.8, 'urgent': 1.0}
        priority = task.get('priority', 'medium')
        value += priority_weights.get(priority, 0.5)
        
        # Type weighting  
        type_weights = {'bug': 0.8, 'feature': 1.0, 'improvement': 0.7, 'task': 0.6}
        task_type = task.get('type', 'task')
        value += type_weights.get(task_type, 0.6)
        
        return min(value, 1.0)
    
    def _get_task_dependencies(self, task: Dict) -> List[int]:
        """Extract task dependencies (simplified)"""
        # In a real implementation, this would parse dependency relationships
        return []

# Global instances
db_manager = DatabaseManager()
cache_manager = CacheManager()
baml_service = None

# FastAPI Application
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    global baml_service
    
    # Startup
    await db_manager.initialize()
    await cache_manager.initialize()
    baml_service = BAMLService(LeantimeDAO(db_manager), cache_manager)
    logger.info("BAML Service initialized")
    
    yield
    
    # Shutdown
    logger.info("BAML Service shutting down")

app = FastAPI(
    title="Leantime BAML Integration API",
    description="AI-powered project management enhancements for Leantime",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.post("/analyze/project/{project_id}")
async def analyze_project(project_id: int, request: ProjectAnalysisRequest):
    """Analyze project health and performance"""
    if not baml_service:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return await baml_service.analyze_project_health(
        project_id,
        {
            'include_predictions': request.include_predictions,
            'analysis_types': request.analysis_types,
            'time_range_days': request.time_range_days
        }
    )

@app.post("/tasks/prioritize")
async def prioritize_tasks(request: TaskPrioritizationRequest):
    """Prioritize project tasks using AI"""
    if not baml_service:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return await baml_service.prioritize_tasks(
        request.project_id,
        request.task_ids,
        request.prioritization_criteria
    )

@app.post("/sprint/plan")
async def plan_sprint(request: SprintPlanningRequest):
    """Plan optimal sprint using AI"""
    if not baml_service:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return await baml_service.plan_sprint(
        request.project_id,
        {
            'sprint_goal': request.sprint_goal,
            'sprint_duration_days': request.sprint_duration_days,
            'team_capacity_hours': request.team_capacity_hours,
            'constraints': request.constraints
        }
    )

@app.get("/insights/{project_id}")
async def get_project_insights(project_id: int, insight_type: str = None):
    """Retrieve stored AI insights for project"""
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            
            query = "SELECT * FROM baml_insights WHERE project_id = %s"
            params = [project_id]
            
            if insight_type:
                query += " AND insight_type = %s"
                params.append(insight_type)
            
            query += " ORDER BY created_at DESC LIMIT 10"
            
            cursor.execute(query, params)
            insights = cursor.fetchall()
            
            # Parse JSON data
            for insight in insights:
                insight['data'] = json.loads(insight['data']) if insight['data'] else {}
                insight['created_at'] = insight['created_at'].isoformat()
                if insight['expires_at']:
                    insight['expires_at'] = insight['expires_at'].isoformat()
            
            return {'insights': insights}
            
    except Exception as e:
        logger.error(f"Error fetching insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "leantime_integration:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=Config.LOG_LEVEL.lower()
    )