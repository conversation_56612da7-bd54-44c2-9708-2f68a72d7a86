#!/usr/bin/env python3
"""
Code Analyzer Agent - Odoo-Leantime MCP Integration
Code quality analysis and technical debt assessment
"""

import json
import os
import ast
import re
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import structlog

logger = structlog.get_logger()

class CodeAnalyzerAgent:
    """Code Analyzer specialized agent for code quality and technical debt analysis"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.analysis_results = {
            "code_quality": {},
            "technical_debt": {},
            "performance_issues": {},
            "maintainability": {},
            "documentation": {}
        }

    async def analyze_code_complexity(self):
        """Analyze code complexity metrics"""
        logger.info("Starting code complexity analysis")

        complexity_results = {
            "files_analyzed": 0,
            "total_lines": 0,
            "cyclomatic_complexity": [],
            "function_lengths": [],
            "class_sizes": [],
            "complexity_hotspots": []
        }

        # Analyze Python files
        python_files = list(self.project_root.glob("**/*.py"))

        for py_file in python_files:
            if ".swarm" in str(py_file) or "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.splitlines()

                complexity_results["files_analyzed"] += 1
                complexity_results["total_lines"] += len(lines)

                # Parse AST for detailed analysis
                try:
                    tree = ast.parse(content)
                    file_analysis = self._analyze_ast(tree, py_file)

                    complexity_results["cyclomatic_complexity"].extend(file_analysis["functions"])
                    complexity_results["class_sizes"].extend(file_analysis["classes"])

                    # Identify complexity hotspots
                    for func in file_analysis["functions"]:
                        if func["complexity"] > 10:  # High complexity threshold
                            complexity_results["complexity_hotspots"].append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "function": func["name"],
                                "complexity": func["complexity"],
                                "lines": func["lines"]
                            })

                except SyntaxError as e:
                    logger.warning("Syntax error in file", file=str(py_file), error=str(e))

            except Exception as e:
                logger.warning("Could not analyze file", file=str(py_file), error=str(e))

        # Calculate averages and statistics
        if complexity_results["cyclomatic_complexity"]:
            complexities = [f["complexity"] for f in complexity_results["cyclomatic_complexity"]]
            complexity_results["average_complexity"] = sum(complexities) / len(complexities)
            complexity_results["max_complexity"] = max(complexities)
            complexity_results["high_complexity_count"] = len([c for c in complexities if c > 10])

        self.analysis_results["code_quality"]["complexity"] = complexity_results
        logger.info("Code complexity analysis completed", results=complexity_results)
        return complexity_results

    def _analyze_ast(self, tree: ast.AST, file_path: Path) -> Dict:
        """Analyze AST for complexity metrics"""
        result = {
            "functions": [],
            "classes": []
        }

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_cyclomatic_complexity(node)
                lines = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 0

                result["functions"].append({
                    "name": node.name,
                    "complexity": complexity,
                    "lines": lines,
                    "file": str(file_path.relative_to(self.project_root))
                })

            elif isinstance(node, ast.ClassDef):
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                lines = node.end_lineno - node.lineno if hasattr(node, 'end_lineno') else 0

                result["classes"].append({
                    "name": node.name,
                    "methods": len(methods),
                    "lines": lines,
                    "file": str(file_path.relative_to(self.project_root))
                })

        return result

    def _calculate_cyclomatic_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity for a function"""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            # Decision points that increase complexity
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1

        return complexity

    async def analyze_code_quality_metrics(self):
        """Analyze code quality metrics"""
        logger.info("Starting code quality metrics analysis")

        quality_metrics = {
            "documentation_coverage": 0,
            "naming_conventions": {"violations": [], "score": 0},
            "code_duplication": {"duplicates": [], "percentage": 0},
            "error_handling": {"try_blocks": 0, "bare_except": 0},
            "import_analysis": {"unused": [], "circular": [], "external_deps": []}
        }

        python_files = list(self.project_root.glob("**/*.py"))
        total_functions = 0
        documented_functions = 0

        for py_file in python_files:
            if ".swarm" in str(py_file) or "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Documentation coverage
                func_matches = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
                total_functions += len(func_matches)

                # Count documented functions (with docstrings)
                for match in re.finditer(r'def\s+\w+\s*\([^)]*\):\s*"""', content):
                    documented_functions += 1

                # Naming convention analysis
                self._analyze_naming_conventions(content, py_file, quality_metrics["naming_conventions"])

                # Error handling analysis
                try_blocks = len(re.findall(r'\btry\s*:', content))
                bare_except = len(re.findall(r'except\s*:', content))
                quality_metrics["error_handling"]["try_blocks"] += try_blocks
                quality_metrics["error_handling"]["bare_except"] += bare_except

                # Import analysis
                self._analyze_imports(content, py_file, quality_metrics["import_analysis"])

            except Exception as e:
                logger.warning("Could not analyze file for quality metrics", file=str(py_file), error=str(e))

        # Calculate documentation coverage
        if total_functions > 0:
            quality_metrics["documentation_coverage"] = (documented_functions / total_functions) * 100

        # Calculate naming convention score
        total_violations = len(quality_metrics["naming_conventions"]["violations"])
        quality_metrics["naming_conventions"]["score"] = max(0, 100 - (total_violations * 5))

        self.analysis_results["code_quality"]["metrics"] = quality_metrics
        logger.info("Code quality metrics analysis completed", metrics=quality_metrics)
        return quality_metrics

    def _analyze_naming_conventions(self, content: str, file_path: Path, naming_data: Dict):
        """Analyze naming convention violations"""

        # Check function names (should be snake_case)
        func_pattern = r'def\s+([A-Z][a-zA-Z0-9]*)\s*\('
        for match in re.finditer(func_pattern, content):
            naming_data["violations"].append({
                "type": "function_camelCase",
                "name": match.group(1),
                "file": str(file_path.relative_to(self.project_root)),
                "recommendation": "Use snake_case for function names"
            })

        # Check class names (should be PascalCase)
        class_pattern = r'class\s+([a-z][a-zA-Z0-9]*)\s*[\(:]'
        for match in re.finditer(class_pattern, content):
            naming_data["violations"].append({
                "type": "class_snake_case",
                "name": match.group(1),
                "file": str(file_path.relative_to(self.project_root)),
                "recommendation": "Use PascalCase for class names"
            })

        # Check constants (should be UPPER_CASE)
        const_pattern = r'^([a-z][a-zA-Z0-9_]*)\s*=\s*["\'\d]'
        for match in re.finditer(const_pattern, content, re.MULTILINE):
            if match.group(1) not in ['self', 'cls']:
                naming_data["violations"].append({
                    "type": "constant_lowercase",
                    "name": match.group(1),
                    "file": str(file_path.relative_to(self.project_root)),
                    "recommendation": "Use UPPER_CASE for constants"
                })

    def _analyze_imports(self, content: str, file_path: Path, import_data: Dict):
        """Analyze import statements"""

        # Extract all imports
        import_pattern = r'^(?:from\s+(\S+)\s+)?import\s+(.+)$'
        imports = []

        for match in re.finditer(import_pattern, content, re.MULTILINE):
            module = match.group(1) if match.group(1) else match.group(2).split()[0]
            imports.append(module)

        # Check for external dependencies
        external_deps = ['requests', 'aiohttp', 'fastapi', 'uvicorn', 'mysql', 'structlog',
                        'cryptography', 'pydantic', 'pytz', 'xmlrpc']

        for imp in imports:
            if any(ext in imp for ext in external_deps):
                if imp not in import_data["external_deps"]:
                    import_data["external_deps"].append(imp)

    async def analyze_performance_issues(self):
        """Analyze potential performance issues"""
        logger.info("Starting performance analysis")

        performance_issues = {
            "database_queries": [],
            "loops": [],
            "memory_usage": [],
            "async_issues": []
        }

        python_files = list(self.project_root.glob("**/*.py"))

        for py_file in python_files:
            if ".swarm" in str(py_file) or "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_rel_path = str(py_file.relative_to(self.project_root))

                # Database query issues
                if "execute(" in content:
                    # Check for N+1 query patterns
                    if "for" in content and "execute(" in content:
                        performance_issues["database_queries"].append({
                            "type": "potential_n_plus_1",
                            "file": file_rel_path,
                            "description": "Potential N+1 query pattern in loop"
                        })

                    # Check for non-parameterized queries
                    if "execute(" in content and "%" in content and "%s" not in content:
                        performance_issues["database_queries"].append({
                            "type": "string_formatting",
                            "file": file_rel_path,
                            "description": "Query uses string formatting instead of parameters"
                        })

                # Loop performance issues
                nested_loops = len(re.findall(r'for\s+.*:\s*\n(?:\s*.*\n)*?\s*for\s+', content))
                if nested_loops > 0:
                    performance_issues["loops"].append({
                        "type": "nested_loops",
                        "file": file_rel_path,
                        "count": nested_loops,
                        "description": "Nested loops may cause performance issues"
                    })

                # Memory usage issues
                if "json.loads" in content and "large" in content.lower():
                    performance_issues["memory_usage"].append({
                        "type": "large_json_parsing",
                        "file": file_rel_path,
                        "description": "Large JSON parsing may consume excessive memory"
                    })

                # Async/await issues
                if "async def" in content:
                    if "requests.get" in content or "requests.post" in content:
                        performance_issues["async_issues"].append({
                            "type": "blocking_in_async",
                            "file": file_rel_path,
                            "description": "Using blocking requests in async function"
                        })

            except Exception as e:
                logger.warning("Could not analyze file for performance", file=str(py_file), error=str(e))

        self.analysis_results["performance_issues"] = performance_issues
        logger.info("Performance analysis completed", issues=performance_issues)
        return performance_issues

    async def analyze_technical_debt(self):
        """Analyze technical debt indicators"""
        logger.info("Starting technical debt analysis")

        debt_indicators = {
            "todo_comments": [],
            "hack_comments": [],
            "large_files": [],
            "long_functions": [],
            "high_coupling": [],
            "code_smells": []
        }

        python_files = list(self.project_root.glob("**/*.py"))

        for py_file in python_files:
            if ".swarm" in str(py_file) or "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.splitlines()

                file_rel_path = str(py_file.relative_to(self.project_root))

                # Check file size
                if len(lines) > 500:
                    debt_indicators["large_files"].append({
                        "file": file_rel_path,
                        "lines": len(lines),
                        "recommendation": "Consider breaking into smaller modules"
                    })

                # Check for TODO/FIXME/HACK comments
                for i, line in enumerate(lines, 1):
                    if "TODO" in line.upper():
                        debt_indicators["todo_comments"].append({
                            "file": file_rel_path,
                            "line": i,
                            "comment": line.strip()
                        })

                    if any(keyword in line.upper() for keyword in ["HACK", "FIXME", "WORKAROUND"]):
                        debt_indicators["hack_comments"].append({
                            "file": file_rel_path,
                            "line": i,
                            "comment": line.strip()
                        })

                # Check for code smells
                if "global " in content:
                    debt_indicators["code_smells"].append({
                        "type": "global_variables",
                        "file": file_rel_path,
                        "description": "Global variables detected"
                    })

                if content.count("except:") > content.count("except "):
                    debt_indicators["code_smells"].append({
                        "type": "bare_except",
                        "file": file_rel_path,
                        "description": "Bare except clauses detected"
                    })

            except Exception as e:
                logger.warning("Could not analyze file for technical debt", file=str(py_file), error=str(e))

        self.analysis_results["technical_debt"] = debt_indicators
        logger.info("Technical debt analysis completed", debt=debt_indicators)
        return debt_indicators

    async def analyze_maintainability(self):
        """Analyze code maintainability factors"""
        logger.info("Starting maintainability analysis")

        maintainability = {
            "test_coverage": {"estimated": 0, "test_files": 0},
            "documentation": {"readme": False, "api_docs": False, "inline_docs": 0},
            "dependency_health": {"outdated": [], "security": []},
            "code_organization": {"modules": 0, "packages": 0}
        }

        # Check for test files
        test_files = list(self.project_root.glob("**/test*.py")) + list(self.project_root.glob("**/*test.py"))
        maintainability["test_coverage"]["test_files"] = len(test_files)

        # Check for documentation
        readme_files = list(self.project_root.glob("README*")) + list(self.project_root.glob("readme*"))
        maintainability["documentation"]["readme"] = len(readme_files) > 0

        # Count Python modules and packages
        python_files = list(self.project_root.glob("**/*.py"))
        maintainability["code_organization"]["modules"] = len(python_files)

        init_files = list(self.project_root.glob("**/__init__.py"))
        maintainability["code_organization"]["packages"] = len(init_files)

        # Estimate test coverage (rough estimate based on test files vs source files)
        source_files = len([f for f in python_files if "test" not in str(f).lower()])
        if source_files > 0:
            maintainability["test_coverage"]["estimated"] = min(100, (len(test_files) / source_files) * 100)

        self.analysis_results["maintainability"] = maintainability
        logger.info("Maintainability analysis completed", maintainability=maintainability)
        return maintainability

    async def generate_improvement_recommendations(self):
        """Generate improvement recommendations based on analysis"""
        logger.info("Generating improvement recommendations")

        recommendations = {
            "immediate_actions": [],
            "short_term_goals": [],
            "long_term_goals": [],
            "priority_ranking": []
        }

        # Analyze findings and generate recommendations

        # Code complexity recommendations
        complexity_data = self.analysis_results.get("code_quality", {}).get("complexity", {})
        if complexity_data.get("high_complexity_count", 0) > 0:
            recommendations["immediate_actions"].append({
                "action": "Refactor high complexity functions",
                "priority": "HIGH",
                "impact": "Improved maintainability and reduced bugs",
                "effort": "Medium"
            })

        # Performance recommendations
        perf_issues = self.analysis_results.get("performance_issues", {})
        if perf_issues.get("database_queries"):
            recommendations["immediate_actions"].append({
                "action": "Optimize database queries",
                "priority": "HIGH",
                "impact": "Improved response times",
                "effort": "Medium"
            })

        # Technical debt recommendations
        debt_data = self.analysis_results.get("technical_debt", {})
        if debt_data.get("large_files"):
            recommendations["short_term_goals"].append({
                "goal": "Break down large files into smaller modules",
                "priority": "MEDIUM",
                "impact": "Better code organization",
                "effort": "High"
            })

        # Maintainability recommendations
        maint_data = self.analysis_results.get("maintainability", {})
        if maint_data.get("test_coverage", {}).get("estimated", 0) < 50:
            recommendations["long_term_goals"].append({
                "goal": "Increase test coverage to >80%",
                "priority": "MEDIUM",
                "impact": "Reduced bugs and improved confidence",
                "effort": "High"
            })

        # Priority ranking
        all_actions = (recommendations["immediate_actions"] +
                      recommendations["short_term_goals"] +
                      recommendations["long_term_goals"])

        recommendations["priority_ranking"] = sorted(
            all_actions,
            key=lambda x: {"HIGH": 3, "MEDIUM": 2, "LOW": 1}.get(x["priority"], 0),
            reverse=True
        )

        self.analysis_results["recommendations"] = recommendations
        logger.info("Improvement recommendations generated", recommendations=recommendations)
        return recommendations

    async def save_analysis_results(self):
        """Save code analysis results to file"""
        output_dir = self.project_root / ".swarm" / "analysis"
        output_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().isoformat()

        # Save code analysis
        analysis_file = output_dir / f"code_analysis_{timestamp.replace(':', '-')}.json"
        with open(analysis_file, 'w') as f:
            json.dump({
                "timestamp": timestamp,
                "agent": "CodeAnalyzer",
                "analysis_results": self.analysis_results
            }, f, indent=2)

        logger.info("Code analysis results saved", file=str(analysis_file))
        return str(analysis_file)

    async def execute_code_analysis(self):
        """Execute complete code analysis"""
        logger.info("Starting Code Analyzer Agent execution")

        try:
            # Run analysis phases
            await self.analyze_code_complexity()
            await self.analyze_code_quality_metrics()
            await self.analyze_performance_issues()
            await self.analyze_technical_debt()
            await self.analyze_maintainability()
            await self.generate_improvement_recommendations()

            # Save results
            output_file = await self.save_analysis_results()

            recommendations = self.analysis_results.get("recommendations", {})
            priority_actions = recommendations.get("immediate_actions", [])

            logger.info("Code Analyzer Agent completed successfully",
                       output=output_file,
                       immediate_actions=len(priority_actions))

            return {
                "status": "completed",
                "agent": "CodeAnalyzer",
                "output_file": output_file,
                "summary": {
                    "files_analyzed": self.analysis_results["code_quality"]["complexity"]["files_analyzed"],
                    "high_complexity_functions": self.analysis_results["code_quality"]["complexity"].get("high_complexity_count", 0),
                    "technical_debt_items": len(self.analysis_results["technical_debt"].get("todo_comments", [])),
                    "immediate_actions": len(priority_actions)
                },
                "next_actions": [
                    "Review and prioritize improvement recommendations",
                    "Refactor high complexity functions",
                    "Implement performance optimizations",
                    "Address technical debt items"
                ]
            }

        except Exception as e:
            logger.error("Code Analyzer Agent failed", error=str(e))
            return {
                "status": "failed",
                "agent": "CodeAnalyzer",
                "error": str(e)
            }

async def main():
    """Main execution for Code Analyzer Agent"""
    project_root = r"C:\Users\<USER>\projects\docker-leantime"
    agent = CodeAnalyzerAgent(project_root)
    result = await agent.execute_code_analysis()
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())