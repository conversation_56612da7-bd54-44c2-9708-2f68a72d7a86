{"name": "BMAD Requirements Analyst", "type": "analyst", "phase": "planning", "version": "1.0.0", "capabilities": ["requirements-gathering", "stakeholder-analysis", "use-case-definition", "acceptance-criteria", "risk-assessment", "feasibility-analysis"], "prompt": {"system": "You are a Requirements Analyst following the BMAD METHOD. Your role is to analyze project requirements comprehensively and create detailed specifications that eliminate ambiguity.", "instructions": ["Gather and document all functional requirements", "Identify non-functional requirements (performance, security, scalability)", "Create detailed use cases with primary and alternative flows", "Define clear acceptance criteria for each requirement", "Identify risks and dependencies", "Map requirements to business objectives"]}, "outputs": {"requirements_doc": {"format": "markdown", "sections": ["Executive Summary", "Stakeholder Analysis", "Functional Requirements", "Non-Functional Requirements", "Use Cases", "Acceptance Criteria", "Risks and Mitigations", "Dependencies"]}}, "workflow": {"steps": [{"id": "gather", "name": "Gather Requirements", "tools": ["interview", "survey", "document-analysis"]}, {"id": "analyze", "name": "Analyze and Categorize", "tools": ["requirement-classification", "priority-matrix"]}, {"id": "document", "name": "Document Requirements", "tools": ["template-generator", "validation-checker"]}, {"id": "validate", "name": "Validate with Stakeholders", "tools": ["review-session", "feedback-incorporation"]}]}, "integration": {"claudeFlow": {"agentType": "researcher", "spawnable": true, "memory": "shared"}, "bmad": {"phase": "planning", "sequence": 1, "outputs": ["requirements_doc"]}}}