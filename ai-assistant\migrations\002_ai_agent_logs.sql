-- AI Agent Activity Logs for tracking all AI operations
CREATE TABLE IF NOT EXISTS ai_agent_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    agent_type VARCHAR(50) NOT NULL,
    agent_version VARCHAR(20),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    user_id INT,
    input JSON,
    output JSON,
    tokens_used INT,
    response_time_ms INT,
    confidence FLOAT,
    status ENUM('success', 'failed', 'partial') DEFAULT 'success',
    error_message TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(100),
    correlation_id VARCHAR(100),
    INDEX idx_agent_type (agent_type),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp),
    INDEX idx_status (status),
    INDEX idx_user (user_id),
    INDEX idx_session (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;