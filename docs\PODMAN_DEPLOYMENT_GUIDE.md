# Comprehensive Podman Deployment Guide for Leantime\n\nThis guide covers the complete migration from <PERSON><PERSON> to Podman for Leantime deployment, including all scripts, workflows, and best practices.\n\n## Table of Contents\n\n- [Overview](#overview)\n- [Prerequisites](#prerequisites)\n- [Migration Strategy](#migration-strategy)\n- [Script Reference](#script-reference)\n- [Deployment Workflows](#deployment-workflows)\n- [Security Considerations](#security-considerations)\n- [Troubleshooting](#troubleshooting)\n- [Best Practices](#best-practices)\n\n## Overview\n\nThis project provides a complete set of Podman deployment scripts to replace the existing Docker infrastructure. The scripts maintain compatibility with existing workflows while adding Podman-specific features like auto-updates, enhanced security, and better resource management.\n\n### Key Benefits of Podman\n\n- **Rootless Containers**: Enhanced security with non-root operation\n- **Systemd Integration**: Native systemd service support\n- **Auto-Updates**: Built-in container update automation\n- **SELinux Support**: Better security with mandatory access controls\n- **Resource Efficiency**: Lower overhead than <PERSON><PERSON> daemon\n- **OCI Compatibility**: Full Docker image compatibility\n\n## Prerequisites\n\n### System Requirements\n\n- **Linux**: Ubuntu 20.04+, CentOS 8+, RHEL 8+, or compatible\n- **Windows**: Windows 10/11 with WSL 2 or Podman Machine\n- **Memory**: Minimum 2GB RAM (4GB recommended)\n- **Storage**: 20GB available disk space\n- **Network**: Internet access for image downloads\n\n### Software Dependencies\n\n#### Linux\n```bash\n# Ubuntu/Debian\nsudo apt update\nsudo apt install -y podman podman-compose curl jq\n\n# CentOS/RHEL\nsudo dnf install -y podman podman-compose curl jq\n\n# Alternative: Install podman-compose via pip\npip3 install podman-compose\n```\n\n#### Windows\n```powershell\n# Option 1: winget (recommended)\nwinget install RedHat.Podman\n\n# Option 2: Chocolatey\nchoco install podman-cli\n\n# Option 3: Manual download from GitHub\n# https://github.com/containers/podman/releases\n```\n\n### Optional Dependencies\n\n- **systemd**: For service management (Linux)\n- **jq**: For JSON processing in health checks\n- **openssl**: For SSL certificate validation\n- **mail**: For email notifications\n- **curl/wget**: For HTTP health checks\n\n## Migration Strategy\n\n### Phase 1: Preparation\n\n1. **Backup Current Deployment**\n   ```bash\n   # Docker backup\n   ./scripts/backup-docker-deployment.sh\n   \n   # Or use existing backup scripts\n   docker-compose exec mysql mysqldump -u root -p leantime > backup.sql\n   ```\n\n2. **Install Podman**\n   ```bash\n   # Use the migration script\n   ./scripts/migrate-to-podman.sh\n   \n   # Or manual installation per platform\n   ```\n\n3. **Verify Installation**\n   ```bash\n   # Linux\n   podman --version\n   podman info\n   \n   # Windows\n   .\\scripts\\podman-windows-manager.ps1 -Action status\n   ```\n\n### Phase 2: Migration\n\n1. **Stop Docker Services**\n   ```bash\n   docker-compose down\n   ```\n\n2. **Run Migration Script**\n   ```bash\n   ./scripts/migrate-to-podman.sh\n   ```\n\n3. **Verify Migration**\n   ```bash\n   ./scripts/podman-health-check.sh\n   ```\n\n### Phase 3: Production Setup\n\n1. **Configure Systemd Services**\n   ```bash\n   ./scripts/podman-systemd-manager.sh install user\n   ./scripts/podman-systemd-manager.sh start user\n   ```\n\n2. **Enable Auto-Updates**\n   ```bash\n   ./scripts/setup-podman-autoupdate.sh\n   ```\n\n3. **Setup Monitoring**\n   ```bash\n   # Enable health check timer\n   systemctl --user enable podman-health-check.timer\n   systemctl --user start podman-health-check.timer\n   ```\n\n## Script Reference\n\n### Core Deployment Scripts\n\n#### `scripts/deploy-direct-podman.sh`\n**Purpose**: Direct deployment to server using Podman  \n**Usage**:\n```bash\n./scripts/deploy-direct-podman.sh\n```\n**Features**:\n- Rootless Podman support\n- SELinux label handling\n- Health checks and validation\n- Systemd service creation\n- Automatic backup before deployment\n\n#### `scripts/build-and-deploy-podman.ps1`\n**Purpose**: Complete build and deploy pipeline for Windows  \n**Usage**:\n```powershell\n.\\scripts\\build-and-deploy-podman.ps1 -ServerIP ************* -ServerUser admin -Domain example.com\n```\n**Features**:\n- Multi-stage build process\n- Registry support (Docker Hub, private registries)\n- Automated testing and validation\n- Windows/WSL integration\n\n#### `scripts/zero-downtime-deploy-podman.sh`\n**Purpose**: Blue-green deployment with zero downtime  \n**Usage**:\n```bash\n./scripts/zero-downtime-deploy-podman.sh\n```\n**Features**:\n- Pod-based blue-green deployment\n- Automatic rollback on failure\n- Load balancer integration\n- Health verification\n\n### Management Scripts\n\n#### `scripts/podman-health-check.sh`\n**Purpose**: Comprehensive health monitoring  \n**Usage**:\n```bash\n# Full health check\n./scripts/podman-health-check.sh\n\n# Specific service check\n./scripts/podman-health-check.sh -s database\n\n# Quiet mode (for automation)\n./scripts/podman-health-check.sh -q\n```\n\n#### `scripts/rollback-podman.sh`\n**Purpose**: Comprehensive rollback capabilities  \n**Usage**:\n```bash\n# Full system rollback\n./scripts/rollback-podman.sh full\n\n# Database-only rollback\n./scripts/rollback-podman.sh database\n\n# Emergency recovery\n./scripts/rollback-podman.sh emergency\n```\n\n#### `scripts/podman-systemd-manager.sh`\n**Purpose**: Systemd service management  \n**Usage**:\n```bash\n# Install user services\n./scripts/podman-systemd-manager.sh install user\n\n# Install system services (requires root)\nsudo ./scripts/podman-systemd-manager.sh install system\n\n# Check status\n./scripts/podman-systemd-manager.sh status both\n```\n\n#### `scripts/podman-windows-manager.ps1`\n**Purpose**: Windows-specific Podman management  \n**Usage**:\n```powershell\n# Start services\n.\\scripts\\podman-windows-manager.ps1 -Action start -Backend machine\n\n# Health check\n.\\scripts\\podman-windows-manager.ps1 -Action health\n\n# Backup\n.\\scripts\\podman-windows-manager.ps1 -Action backup -BackupPath \"C:\\Backups\"\n```\n\n### Orchestration Scripts\n\n#### `scripts/orchestrate-deployment-podman.ps1`\n**Purpose**: Complete deployment orchestration  \n**Usage**:\n```powershell\n.\\scripts\\orchestrate-deployment-podman.ps1 `\n    -ServerIP ************* `\n    -ServerUser admin `\n    -Domain example.com `\n    -Strategy blue-green `\n    -Environment production\n```\n\n## Deployment Workflows\n\n### Development Workflow\n\n1. **Local Development**\n   ```bash\n   # Start development environment\n   podman-compose -f podman-compose.local.yml up -d\n   \n   # View logs\n   podman-compose logs -f\n   \n   # Stop when done\n   podman-compose down\n   ```\n\n2. **Testing**\n   ```bash\n   # Run health checks\n   ./scripts/podman-health-check.sh\n   \n   # Run security scan\n   podman run --rm -v /var/run/podman/podman.sock:/var/run/docker.sock \\\n     aquasec/trivy image leantime/leantime:latest\n   ```\n\n### Staging Deployment\n\n1. **Deploy to Staging**\n   ```bash\n   ./scripts/deploy-direct-podman.sh\n   ```\n\n2. **Validation**\n   ```bash\n   # Automated validation\n   ./scripts/podman-health-check.sh\n   \n   # Manual testing\n   curl -f https://staging.example.com\n   ```\n\n### Production Deployment\n\n#### Option 1: Direct Deployment\n```bash\n./scripts/deploy-direct-podman.sh\n```\n\n#### Option 2: Zero-Downtime Deployment\n```bash\n./scripts/zero-downtime-deploy-podman.sh\n```\n\n#### Option 3: Full Orchestration (Windows)\n```powershell\n.\\scripts\\orchestrate-deployment-podman.ps1 `\n    -ServerIP $SERVER_IP `\n    -ServerUser $SERVER_USER `\n    -Domain $DOMAIN `\n    -Strategy blue-green\n```\n\n### Rollback Procedures\n\n#### Quick Rollback (Deployment Color)\n```bash\n./scripts/rollback-podman.sh deployment blue\n```\n\n#### Database Rollback\n```bash\n./scripts/rollback-podman.sh database /path/to/backup.sql\n```\n\n#### Full System Rollback\n```bash\n./scripts/rollback-podman.sh full 20241201-143022\n```\n\n#### Emergency Recovery\n```bash\n./scripts/rollback-podman.sh emergency\n```\n\n## Security Considerations\n\n### Rootless Operation\n\n**Benefits**:\n- Containers run as non-root user\n- Reduced attack surface\n- Better isolation\n\n**Setup**:\n```bash\n# Enable user namespaces\necho 'user.max_user_namespaces=15000' | sudo tee -a /etc/sysctl.conf\nsudo sysctl -p\n\n# Configure subuid/subgid\nsudo usermod --add-subuids 100000-165535 $(whoami)\nsudo usermod --add-subgids 100000-165535 $(whoami)\n```\n\n### SELinux Integration\n\n**Enable SELinux labels**:\n```bash\n# All volume mounts use :Z suffix\n-v data:/var/lib/mysql:Z\n```\n\n**Check SELinux status**:\n```bash\n./scripts/podman-health-check.sh -s security\n```\n\n### Network Security\n\n**Isolated Networks**:\n```yaml\nnetworks:\n  leantime_net:\n    driver: bridge\n    internal: false  # Set to true for complete isolation\n```\n\n**Port Management**:\n```bash\n# Only expose necessary ports\nports:\n  - \"127.0.0.1:8090:8080\"  # Bind to localhost only\n```\n\n### Container Security\n\n**Security Options**:\n```yaml\nsecurity_opt:\n  - no-new-privileges:true\n  - label=disable  # For rootless mode\n```\n\n**Resource Limits**:\n```yaml\ndeploy:\n  resources:\n    limits:\n      cpus: '2.0'\n      memory: 2G\n    reservations:\n      memory: 1G\n```\n\n## Troubleshooting\n\n### Common Issues\n\n#### 1. Permission Denied Errors\n\n**Symptom**: `permission denied` when starting containers\n\n**Solution**:\n```bash\n# Check user namespaces\ngrep $(whoami) /etc/subuid /etc/subgid\n\n# Reset if necessary\npodman system migrate\npodman system reset --force\n```\n\n#### 2. Port Binding Issues\n\n**Symptom**: `bind: permission denied` for ports < 1024\n\n**Solution**:\n```bash\n# Use higher ports for rootless mode\nports:\n  - \"8090:8080\"  # Instead of \"80:8080\"\n  \n# Or enable port binding for non-root (not recommended)\necho 'net.ipv4.ip_unprivileged_port_start=80' | sudo tee -a /etc/sysctl.conf\nsudo sysctl -p\n```\n\n#### 3. Volume Mount Issues\n\n**Symptom**: Files not accessible in containers\n\n**Solution**:\n```bash\n# Use proper SELinux labels\n-v /host/path:/container/path:Z\n\n# Check permissions\nls -laZ /host/path\n```\n\n#### 4. Service Discovery Problems\n\n**Symptom**: Containers can't communicate\n\n**Solution**:\n```bash\n# Ensure all containers are in the same network\n# Use container names for internal communication\nLEAN_DB_HOST: mysql_leantime  # Not localhost\n```\n\n### Debugging Commands\n\n```bash\n# Check Podman info\npodman info\n\n# List all resources\npodman ps -a\npodman pod ls\npodman volume ls\npodman network ls\n\n# Inspect specific resources\npodman inspect container_name\npodman logs container_name\n\n# Check system events\npodman events --since=\"1h\" --until=\"now\"\n\n# Resource usage\npodman stats\n```\n\n### Log Locations\n\n```bash\n# Application logs\n/var/log/podman-health-check.log\n/var/log/podman-systemd.log\n\n# System logs\njournalctl -u leantime-podman.service\njournalctl --user -u leantime-podman.service\n\n# Container logs\npodman logs container_name\n```\n\n## Best Practices\n\n### Development\n\n1. **Use Consistent Naming**\n   ```bash\n   # Project prefix for all resources\n   container_name: leantime_mysql\n   volume_name: leantime_data\n   network_name: leantime_net\n   ```\n\n2. **Environment Management**\n   ```bash\n   # Separate compose files for different environments\n   podman-compose.yml          # Base configuration\n   podman-compose.local.yml    # Development overrides\n   podman-compose.prod.yml     # Production settings\n   ```\n\n3. **Version Pinning**\n   ```yaml\n   # Pin specific versions for production\n   image: docker.io/mysql:8.4  # Not 'latest'\n   ```\n\n### Security\n\n1. **Least Privilege**\n   ```bash\n   # Run as non-root user\n   user: 1001:1001\n   \n   # Drop capabilities\n   cap_drop:\n     - ALL\n   cap_add:\n     - CAP_CHOWN  # Only what's needed\n   ```\n\n2. **Network Isolation**\n   ```yaml\n   # Use internal networks where possible\n   networks:\n     backend:\n       internal: true\n     frontend:\n       internal: false\n   ```\n\n3. **Secret Management**\n   ```bash\n   # Use environment files\n   env_file: .env\n   \n   # Never commit secrets to version control\n   echo '.env' >> .gitignore\n   ```\n\n### Performance\n\n1. **Resource Limits**\n   ```yaml\n   deploy:\n     resources:\n       limits:\n         memory: 2G\n         cpus: '1.5'\n   ```\n\n2. **Volume Optimization**\n   ```bash\n   # Use named volumes for persistent data\n   volumes:\n     - mysql_data:/var/lib/mysql:Z\n   \n   # Use tmpfs for temporary data\n   tmpfs:\n     - /tmp\n     - /var/tmp\n   ```\n\n3. **Image Optimization**\n   ```dockerfile\n   # Multi-stage builds\n   FROM node:18-alpine AS builder\n   # ... build steps ...\n   \n   FROM node:18-alpine AS runtime\n   COPY --from=builder /app /app\n   ```\n\n### Monitoring\n\n1. **Health Checks**\n   ```yaml\n   healthcheck:\n     test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080\"]\n     interval: 30s\n     timeout: 10s\n     retries: 3\n   ```\n\n2. **Auto-Updates**\n   ```yaml\n   labels:\n     - \"io.containers.autoupdate=registry\"\n     - \"io.containers.autoupdate.strategy=rolling\"\n   ```\n\n3. **Logging**\n   ```yaml\n   logging:\n     driver: journald\n     options:\n       tag: \"{{.Name}}\"\n   ```\n\n### Backup and Recovery\n\n1. **Automated Backups**\n   ```bash\n   # Schedule regular backups\n   0 2 * * * /path/to/scripts/backup-podman.sh\n   ```\n\n2. **Test Restores**\n   ```bash\n   # Regular restore testing\n   ./scripts/rollback-podman.sh verify\n   ```\n\n3. **Multiple Backup Locations**\n   ```bash\n   # Local and remote backups\n   rsync -av backups/ user@backup-server:/backups/\n   ```\n\n## Conclusion\n\nThis comprehensive Podman deployment solution provides enterprise-grade container orchestration with enhanced security, better resource management, and improved operational capabilities. The migration from Docker to Podman offers significant benefits while maintaining compatibility with existing workflows.\n\nFor additional support or questions, refer to the troubleshooting section or check the individual script documentation.