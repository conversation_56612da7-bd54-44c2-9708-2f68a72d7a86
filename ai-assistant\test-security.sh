#!/bin/bash
# Comprehensive security testing script

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

BASE_URL="http://127.0.0.1:8444"
CONTAINER_NAME="ai-assistant-secure"

echo "🔒 Security Testing Round 1 - AI Assistant"
echo "=========================================="

# Test 1: Health endpoint (should work without auth)
echo -e "\n${YELLOW}Test 1: Health endpoint accessibility${NC}"
HEALTH_RESPONSE=$(curl -s ${BASE_URL}/health || echo "FAILED")
if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo -e "${GREEN}✓ Health endpoint working${NC}"
else
    echo -e "${RED}✗ Health endpoint failed${NC}"
fi

# Test 2: Unauthenticated API access (should fail)
echo -e "\n${YELLOW}Test 2: Unauthenticated API access${NC}"
API_RESPONSE=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/api/v1/process \
    -H "Content-Type: application/json" \
    -d '{"action": "test"}' -o /dev/null)

if [[ $API_RESPONSE == "401" ]]; then
    echo -e "${GREEN}✓ API properly protected (401)${NC}"
else
    echo -e "${RED}✗ API not protected (got ${API_RESPONSE})${NC}"
fi

# Test 3: Authentication with correct credentials
echo -e "\n${YELLOW}Test 3: Authentication with correct credentials${NC}"
AUTH_RESPONSE=$(curl -s -X POST ${BASE_URL}/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "admin123"}')

if [[ $AUTH_RESPONSE == *"token"* ]]; then
    echo -e "${GREEN}✓ Authentication successful${NC}"
    TOKEN=$(echo $AUTH_RESPONSE | jq -r .token)
else
    echo -e "${RED}✗ Authentication failed${NC}"
    TOKEN=""
fi

# Test 4: Authentication with wrong credentials
echo -e "\n${YELLOW}Test 4: Authentication with wrong credentials${NC}"
WRONG_AUTH=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "wrong"}' -o /dev/null)

if [[ $WRONG_AUTH == "401" ]]; then
    echo -e "${GREEN}✓ Wrong credentials rejected (401)${NC}"
else
    echo -e "${RED}✗ Wrong credentials accepted (got ${WRONG_AUTH})${NC}"
fi

# Test 5: Authenticated API access
if [[ -n $TOKEN ]]; then
    echo -e "\n${YELLOW}Test 5: Authenticated API access${NC}"
    API_AUTH_RESPONSE=$(curl -s -w "%{http_code}" -X POST ${BASE_URL}/api/v1/process \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"action": "knowledge.search", "data": {"query": "test"}}' -o /dev/null)
    
    if [[ $API_AUTH_RESPONSE == "200" ]]; then
        echo -e "${GREEN}✓ Authenticated API access works${NC}"
    else
        echo -e "${RED}✗ Authenticated API failed (got ${API_AUTH_RESPONSE})${NC}"
    fi
fi

# Test 6: Rate limiting
echo -e "\n${YELLOW}Test 6: Rate limiting test${NC}"
RATE_LIMIT_FAILED=0
for i in {1..65}; do
    RATE_RESPONSE=$(curl -s -w "%{http_code}" ${BASE_URL}/health -o /dev/null)
    if [[ $RATE_RESPONSE == "429" ]]; then
        echo -e "${GREEN}✓ Rate limiting active (hit limit at request $i)${NC}"
        break
    fi
    if [[ $i == 65 ]]; then
        echo -e "${YELLOW}⚠ Rate limiting not triggered in 65 requests${NC}"
    fi
done

# Test 7: Path traversal attack
echo -e "\n${YELLOW}Test 7: Path traversal attack prevention${NC}"
if [[ -n $TOKEN ]]; then
    TRAVERSAL_RESPONSE=$(curl -s -X POST ${BASE_URL}/api/v1/process \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"action": "../../etc/passwd"}')
    
    if [[ $TRAVERSAL_RESPONSE == *"Unauthorized action"* ]] || [[ $TRAVERSAL_RESPONSE == *"error"* ]]; then
        echo -e "${GREEN}✓ Path traversal blocked${NC}"
    else
        echo -e "${RED}✗ Path traversal not blocked${NC}"
    fi
fi

# Test 8: XSS injection
echo -e "\n${YELLOW}Test 8: XSS injection prevention${NC}"
if [[ -n $TOKEN ]]; then
    XSS_RESPONSE=$(curl -s -X POST ${BASE_URL}/api/v1/process \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"action": "<script>alert(1)</script>"}')
    
    if [[ $XSS_RESPONSE == *"Unauthorized action"* ]] || [[ $XSS_RESPONSE == *"error"* ]]; then
        echo -e "${GREEN}✓ XSS injection blocked${NC}"
    else
        echo -e "${RED}✗ XSS injection not blocked${NC}"
    fi
fi

# Test 9: Security headers
echo -e "\n${YELLOW}Test 9: Security headers${NC}"
HEADERS=$(curl -s -I ${BASE_URL}/health)
SECURITY_HEADERS=("X-Content-Type-Options" "X-Frame-Options" "X-XSS-Protection" "Content-Security-Policy")
HEADERS_FOUND=0

for header in "${SECURITY_HEADERS[@]}"; do
    if echo "$HEADERS" | grep -q "$header"; then
        HEADERS_FOUND=$((HEADERS_FOUND + 1))
    fi
done

if [[ $HEADERS_FOUND -eq 4 ]]; then
    echo -e "${GREEN}✓ All security headers present${NC}"
elif [[ $HEADERS_FOUND -gt 0 ]]; then
    echo -e "${YELLOW}⚠ Some security headers present (${HEADERS_FOUND}/4)${NC}"
else
    echo -e "${RED}✗ No security headers found${NC}"
fi

# Test 10: Container security
echo -e "\n${YELLOW}Test 10: Container security checks${NC}"

# Check if running as non-root
USER_ID=$(podman exec $CONTAINER_NAME id -u)
if [[ $USER_ID != "0" ]]; then
    echo -e "${GREEN}✓ Container running as non-root (UID: $USER_ID)${NC}"
else
    echo -e "${RED}✗ Container running as root${NC}"
fi

# Check read-only filesystem
READONLY=$(podman inspect $CONTAINER_NAME | jq -r '.[0].HostConfig.ReadonlyRootfs')
if [[ $READONLY == "true" ]]; then
    echo -e "${GREEN}✓ Read-only root filesystem${NC}"
else
    echo -e "${RED}✗ Filesystem is writable${NC}"
fi

# Check capabilities
CAPS=$(podman inspect $CONTAINER_NAME | jq -r '.[0].EffectiveCaps[]' | wc -l)
if [[ $CAPS -le 2 ]]; then
    echo -e "${GREEN}✓ Minimal capabilities ($CAPS)${NC}"
else
    echo -e "${YELLOW}⚠ Multiple capabilities ($CAPS)${NC}"
fi

# Check network binding
PORT_BIND=$(ss -tlpn | grep ":8444" | grep -c "127.0.0.1:8444" || true)
if [[ $PORT_BIND == "1" ]]; then
    echo -e "${GREEN}✓ Port bound to localhost only${NC}"
else
    echo -e "${RED}✗ Port not properly restricted${NC}"
fi

echo -e "\n${GREEN}Security Testing Round 1 Complete!${NC}"