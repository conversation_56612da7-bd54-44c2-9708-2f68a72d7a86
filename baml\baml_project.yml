# BAML Project Configuration
name: gulf-euro-drive-leantime
version: 1.0.0
default_client: SmartClient

# Generator configurations
generators:
  - name: python
    output_path: ./python/baml_client
    options:
      async_support: true
      pydantic_v2: true
      
  - name: typescript
    output_path: ./ts/baml_client
    options:
      runtime: node
      module_type: esm
      
  - name: openapi
    output_path: ./docs/api-spec.json
    options:
      version: 3.1.0
      include_schemas: true

# Test configurations
test:
  timeout: 30000
  parallel: true
  coverage: true
  
# Deployment configurations  
deployment:
  environments:
    development:
      client_override: LocalClient
      log_level: debug
      
    staging:
      client_override: FastClient
      log_level: info
      base_url: https://gulfdrive.kanousai.com
      
    production:
      client_override: SmartClient
      log_level: error
      base_url: https://gulfeurodrive.com
      
# Cache settings
cache:
  enabled: true
  ttl: 3600
  max_size: 100MB
  
# Monitoring
monitoring:
  metrics: true
  tracing: true
  endpoint: http://localhost:9090/metrics