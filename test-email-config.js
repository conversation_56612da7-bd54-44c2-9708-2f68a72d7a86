#!/usr/bin/env node

/**
 * Email Configuration Test Script
 * Tests the email setup for password reset functionality
 */

const axios = require('axios').default;
require('dotenv').config();

// Configuration from .env
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const FROM_EMAIL = process.env.LEAN_EMAIL_RETURN || '<EMAIL>';
const SMTP_HOST = process.env.LEAN_EMAIL_SMTP_HOSTS || 'email-service';
const SMTP_PORT = process.env.LEAN_EMAIL_SMTP_PORT || '2525';

console.log('🔍 Email Configuration Test');
console.log('============================');
console.log(`📧 From Email: ${FROM_EMAIL}`);
console.log(`🏠 SMTP Host: ${SMTP_HOST}`);
console.log(`🔌 SMTP Port: ${SMTP_PORT}`);
console.log(`🔑 API Key: ${RESEND_API_KEY ? RESEND_API_KEY.substring(0, 8) + '...' : 'NOT SET'}`);
console.log('');

async function testResendAPI() {
    console.log('🧪 Testing Resend API directly...');
    
    if (!RESEND_API_KEY) {
        console.error('❌ RESEND_API_KEY not found in environment');
        return false;
    }

    try {
        const testEmail = {
            from: FROM_EMAIL,
            to: ['<EMAIL>'], // Test email
            subject: 'Email Configuration Test - ' + new Date().toISOString(),
            html: `
                <h2>Email Configuration Test</h2>
                <p>This is a test email to verify the email configuration is working correctly.</p>
                <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
                <p><strong>From:</strong> ${FROM_EMAIL}</p>
                <p><strong>API Key:</strong> ${RESEND_API_KEY.substring(0, 8)}...</p>
                <hr>
                <p><em>If you receive this email, the configuration is working correctly!</em></p>
            `
        };

        const response = await axios.post('https://api.resend.com/emails', testEmail, {
            headers: {
                'Authorization': `Bearer ${RESEND_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        console.log('✅ Resend API test successful!');
        console.log(`📧 Email ID: ${response.data.id}`);
        console.log(`📬 Sent to: <EMAIL>`);
        return true;

    } catch (error) {
        console.error('❌ Resend API test failed:');
        console.error(`   Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status}`);
            console.error(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
        }
        return false;
    }
}

async function testEmailServiceHealth() {
    console.log('🏥 Testing email service health...');
    
    try {
        // Test if running on DigitalOcean
        const healthUrl = 'https://admin.dxbmeta.com:2525/health';
        console.log(`🔗 Checking: ${healthUrl}`);
        
        const response = await axios.get(healthUrl, {
            timeout: 5000,
            validateStatus: () => true // Accept any status code
        });

        if (response.status === 200) {
            console.log('✅ Email service is healthy!');
            console.log(`📊 Service info:`, response.data);
            return true;
        } else {
            console.log(`⚠️  Email service responded with status: ${response.status}`);
            return false;
        }

    } catch (error) {
        console.log('ℹ️  Could not reach email service health endpoint');
        console.log(`   This is normal if the service is not running or not exposed`);
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

async function validateConfiguration() {
    console.log('📋 Validating configuration...');
    
    const issues = [];
    
    // Check domain consistency
    if (!FROM_EMAIL.includes('dxbmeta.com')) {
        issues.push(`❌ FROM_EMAIL should use dxbmeta.com domain, got: ${FROM_EMAIL}`);
    } else {
        console.log('✅ Domain consistency: dxbmeta.com');
    }
    
    // Check SMTP port
    if (SMTP_PORT !== '2525') {
        issues.push(`❌ SMTP_PORT should be 2525, got: ${SMTP_PORT}`);
    } else {
        console.log('✅ SMTP port: 2525');
    }
    
    // Check API key format
    if (!RESEND_API_KEY || !RESEND_API_KEY.startsWith('re_')) {
        issues.push(`❌ RESEND_API_KEY should start with 're_', got: ${RESEND_API_KEY ? RESEND_API_KEY.substring(0, 8) + '...' : 'NOT SET'}`);
    } else {
        console.log('✅ API key format: Valid');
    }
    
    if (issues.length > 0) {
        console.log('\n🚨 Configuration Issues Found:');
        issues.forEach(issue => console.log(`   ${issue}`));
        return false;
    } else {
        console.log('✅ Configuration validation passed!');
        return true;
    }
}

async function main() {
    console.log('🚀 Starting email configuration test...\n');
    
    const configValid = await validateConfiguration();
    console.log('');
    
    const healthOk = await testEmailServiceHealth();
    console.log('');
    
    const apiOk = await testResendAPI();
    console.log('');
    
    console.log('📊 Test Results Summary:');
    console.log('========================');
    console.log(`📋 Configuration: ${configValid ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🏥 Service Health: ${healthOk ? '✅ PASS' : '⚠️  UNKNOWN'}`);
    console.log(`🔑 Resend API: ${apiOk ? '✅ PASS' : '❌ FAIL'}`);
    
    if (configValid && apiOk) {
        console.log('\n🎉 Email configuration is working correctly!');
        console.log('📧 Password reset emails should now be sent successfully.');
        console.log('\n💡 Next steps:');
        console.log('   1. Test password reset in Leantime');
        console.log('   2. Check email logs if issues persist');
        console.log('   3. Verify email service is running on DigitalOcean');
    } else {
        console.log('\n🔧 Email configuration needs attention.');
        console.log('📝 Please fix the issues above and run this test again.');
    }
}

// Handle command line execution
if (require.main === module) {
    main().catch(error => {
        console.error('\n💥 Test script failed:', error.message);
        process.exit(1);
    });
}

module.exports = { testResendAPI, testEmailServiceHealth, validateConfiguration };
