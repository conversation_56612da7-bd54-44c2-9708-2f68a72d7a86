#!/usr/bin/env python3
"""
CRM Module for Leantime MCP Server
Handles customer relationship management with AI-powered insights and lead scoring
Configured for Arab Standard Time (Dubai, UTC+4)
"""

import os
import json
import uuid
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import mysql.connector
import pytz
import re

class CRMModule:
    """Comprehensive Customer Relationship Management with AI insights"""
    
    def __init__(self, db_pool, openrouter_key: str):
        self.db_pool = db_pool
        self.openrouter_key = openrouter_key
        self.openrouter_base = "https://openrouter.ai/api/v1"
        
        # Dubai timezone
        self.timezone = pytz.timezone('Asia/Dubai')
        
        # Lead scoring weights
        self.lead_scoring = {
            'company_size': {'solo': 10, 'small': 20, 'medium': 40, 'large': 60, 'enterprise': 80},
            'industry': {
                'technology': 80, 'finance': 70, 'healthcare': 60, 'education': 50,
                'retail': 40, 'manufacturing': 45, 'consulting': 65, 'government': 55
            },
            'interaction_frequency': {'high': 30, 'medium': 20, 'low': 10},
            'engagement_level': {'hot': 50, 'warm': 30, 'cold': 10},
            'budget_range': {'high': 40, 'medium': 25, 'low': 15, 'unknown': 0}
        }
        
        # Customer lifecycle stages
        self.lifecycle_stages = {
            'prospect': {'score_min': 0, 'score_max': 30},
            'qualified_lead': {'score_min': 31, 'score_max': 60},
            'opportunity': {'score_min': 61, 'score_max': 80},
            'customer': {'score_min': 81, 'score_max': 100},
            'champion': {'score_min': 100, 'score_max': 120}
        }
        
        # Dubai/UAE business context
        self.uae_business_info = {
            'common_industries': [
                'Oil & Gas', 'Real Estate', 'Tourism', 'Finance', 'Technology',
                'Construction', 'Healthcare', 'Education', 'Retail', 'Logistics'
            ],
            'business_culture': {
                'relationship_focused': True,
                'hierarchy_important': True,
                'face_to_face_preferred': True,
                'ramadan_considerations': True
            },
            'common_languages': ['English', 'Arabic', 'Hindi', 'Urdu'],
            'business_hours': {'start': 8, 'end': 18, 'days': [0, 1, 2, 3, 4]}
        }
    
    def get_connection(self):
        """Get database connection from pool"""
        return self.db_pool.get_connection()
    
    def get_dubai_time(self) -> datetime:
        """Get current time in Dubai timezone"""
        return datetime.now(self.timezone)
    
    async def analyze_customer_ai(self, customer_data: Dict[str, Any], 
                                interaction_history: List[Dict] = None) -> Dict[str, Any]:
        """AI analysis of customer for insights, scoring, and recommendations"""
        
        # Prepare context for AI
        context = f"""
        Customer Profile Analysis for Dubai-based Business:
        
        Company: {customer_data.get('company_name', 'N/A')}
        Industry: {customer_data.get('industry', 'Unknown')}
        Size: {customer_data.get('company_size', 'Unknown')}
        Type: {customer_data.get('customer_type', 'prospect')}
        Website: {customer_data.get('website', 'N/A')}
        
        Primary Contact:
        Name: {customer_data.get('primary_contact_name', 'N/A')}
        Email: {customer_data.get('primary_contact_email', 'N/A')}
        
        Recent Interactions: {len(interaction_history) if interaction_history else 0}
        Last Interaction: {interaction_history[0].get('scheduled_at') if interaction_history else 'None'}
        
        Dubai Business Context:
        - Local business culture emphasizes relationships
        - Face-to-face meetings preferred
        - Consider Ramadan and UAE holidays
        - Common languages: English, Arabic
        """
        
        if interaction_history:
            context += f"\n\nRecent Interaction Summary:\n"
            for interaction in interaction_history[:3]:  # Last 3 interactions
                context += f"- {interaction.get('interaction_type')}: {interaction.get('subject')} ({interaction.get('outcome')})\n"
        
        prompt = f"""
        {context}
        
        Provide AI-powered CRM insights:
        
        Analyze and provide insights on:
        1. Lead quality and conversion potential
        2. Recommended next actions
        3. Risk factors (churn, competition)
        4. Opportunities for upselling/cross-selling
        5. Cultural considerations for Dubai/UAE market
        6. Optimal communication approach
        7. Timeline for follow-ups
        
        Respond in JSON format:
        {{
            "lead_score": 75,
            "lead_quality": "high|medium|low",
            "conversion_probability": 0.65,
            "churn_risk": "low|medium|high",
            "lifetime_value_estimate": 15000,
            "key_insights": ["insight 1", "insight 2"],
            "recommended_actions": ["action 1", "action 2"],
            "next_contact_suggestion": "2024-01-15",
            "preferred_communication": "email|phone|meeting",
            "cultural_notes": "Dubai-specific considerations",
            "risk_factors": ["potential risk 1"],
            "opportunities": ["upsell opportunity 1"],
            "urgency_level": "high|medium|low",
            "decision_maker_identified": true,
            "budget_authority": "confirmed|likely|unknown"
        }}
        """
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.openrouter_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://leantime.io",
                    "X-Title": "Leantime CRM"
                }
                
                payload = {
                    "model": "mistralai/mistral-7b-instruct:free",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 1024,
                    "temperature": 0.4
                }
                
                async with session.post(
                    f"{self.openrouter_base}/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        try:
                            if "```json" in content:
                                content = content.split("```json")[1].split("```")[0]
                            elif "```" in content:
                                content = content.split("```")[1].split("```")[0]
                            
                            ai_insights = json.loads(content.strip())
                            return ai_insights
                        except json.JSONDecodeError:
                            return self._calculate_basic_lead_score(customer_data, interaction_history)
                    else:
                        return self._calculate_basic_lead_score(customer_data, interaction_history)
        except Exception as e:
            print(f"AI customer analysis failed: {e}")
            return self._calculate_basic_lead_score(customer_data, interaction_history)
    
    def _calculate_basic_lead_score(self, customer_data: Dict[str, Any], 
                                  interaction_history: List[Dict] = None) -> Dict[str, Any]:
        """Fallback lead scoring using rule-based system"""
        
        score = 0
        
        # Company size scoring
        company_size = customer_data.get('company_size', 'unknown')
        score += self.lead_scoring['company_size'].get(company_size, 0)
        
        # Industry scoring
        industry = customer_data.get('industry', '').lower()
        for ind, points in self.lead_scoring['industry'].items():
            if ind in industry:
                score += points
                break
        
        # Interaction frequency
        if interaction_history:
            recent_interactions = len([i for i in interaction_history 
                                     if i.get('scheduled_at') and 
                                     (datetime.now() - datetime.fromisoformat(str(i['scheduled_at']))).days <= 30])
            if recent_interactions >= 3:
                score += self.lead_scoring['interaction_frequency']['high']
            elif recent_interactions >= 1:
                score += self.lead_scoring['interaction_frequency']['medium']
            else:
                score += self.lead_scoring['interaction_frequency']['low']
        
        # Determine lead quality
        if score >= 70:
            lead_quality = "high"
            conversion_prob = 0.8
        elif score >= 40:
            lead_quality = "medium"
            conversion_prob = 0.5
        else:
            lead_quality = "low"
            conversion_prob = 0.2
        
        return {
            "lead_score": score,
            "lead_quality": lead_quality,
            "conversion_probability": conversion_prob,
            "churn_risk": "low" if score >= 60 else "medium",
            "lifetime_value_estimate": score * 200,  # Simple estimation
            "key_insights": [f"Lead score: {score}/100", f"Quality: {lead_quality}"],
            "recommended_actions": ["Schedule follow-up", "Send proposal"],
            "next_contact_suggestion": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"),
            "preferred_communication": "email",
            "cultural_notes": "Consider UAE business culture",
            "risk_factors": [] if score >= 60 else ["Low engagement"],
            "opportunities": ["Project management solution"],
            "urgency_level": "medium",
            "decision_maker_identified": True,
            "budget_authority": "unknown"
        }
    
    async def create_customer(self, customer_data: Dict[str, Any]) -> str:
        """Create a new customer record"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Generate unique customer ID
            customer_id = f"CUST-{uuid.uuid4().hex[:8].upper()}"
            
            dubai_now = self.get_dubai_time()
            
            # AI analysis for new customer
            ai_insights = await self.analyze_customer_ai(customer_data)
            
            # Insert customer
            query = """
                INSERT INTO mcp_customers (
                    customer_id, company_name, website, industry, company_size,
                    primary_contact_name, primary_contact_email, primary_contact_phone,
                    billing_address, shipping_address, timezone,
                    customer_type, customer_status, lead_source,
                    credit_limit, payment_terms, tax_id,
                    first_contact_date, became_customer_date,
                    ai_health_score, ai_churn_risk, ai_lifetime_value,
                    custom_fields, tags, notes
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s,
                    %s, %s, %s,
                    %s, %s,
                    %s, %s, %s,
                    %s, %s, %s
                )
            """
            
            values = (
                customer_id,
                customer_data['company_name'],
                customer_data.get('website'),
                customer_data.get('industry'),
                customer_data.get('company_size'),
                customer_data['primary_contact_name'],
                customer_data['primary_contact_email'],
                customer_data.get('primary_contact_phone'),
                customer_data.get('billing_address'),
                customer_data.get('shipping_address'),
                customer_data.get('timezone', 'Asia/Dubai'),
                customer_data.get('customer_type', 'prospect'),
                customer_data.get('customer_status', 'active'),
                customer_data.get('lead_source'),
                customer_data.get('credit_limit'),
                customer_data.get('payment_terms', 30),
                customer_data.get('tax_id'),
                customer_data.get('first_contact_date', dubai_now.date()),
                customer_data.get('became_customer_date') if customer_data.get('customer_type') == 'customer' else None,
                ai_insights.get('lead_score', 50) / 100.0,  # Convert to 0-1 scale
                ai_insights.get('churn_risk', 'low'),
                ai_insights.get('lifetime_value_estimate', 0),
                json.dumps(customer_data.get('custom_fields', {})),
                json.dumps(customer_data.get('tags', [])),
                customer_data.get('notes', '')
            )
            
            cursor.execute(query, values)
            conn.commit()
            
            # Add initial contact if provided
            if customer_data.get('additional_contacts'):
                for contact in customer_data['additional_contacts']:
                    await self._add_customer_contact(customer_id, contact)
            
            return customer_id
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to create customer: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def _add_customer_contact(self, customer_id: str, contact_data: Dict[str, Any]):
        """Add additional contact to customer"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Get internal customer ID
            cursor.execute("SELECT id FROM mcp_customers WHERE customer_id = %s", (customer_id,))
            result = cursor.fetchone()
            if not result:
                return
            
            internal_id = result[0]
            
            query = """
                INSERT INTO mcp_customer_contacts (
                    customer_id, name, email, phone, title, department,
                    is_primary, is_billing, is_technical
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                internal_id,
                contact_data['name'],
                contact_data['email'],
                contact_data.get('phone'),
                contact_data.get('title'),
                contact_data.get('department'),
                contact_data.get('is_primary', False),
                contact_data.get('is_billing', False),
                contact_data.get('is_technical', False)
            )
            
            cursor.execute(query, values)
            conn.commit()
            
        except Exception as e:
            print(f"Failed to add customer contact: {e}")
        finally:
            cursor.close()
            conn.close()
    
    async def get_customers(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Get customers with optional filters"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            query = """
                SELECT 
                    c.*,
                    COUNT(DISTINCT cc.id) as contact_count,
                    COUNT(DISTINCT ci.id) as interaction_count,
                    MAX(ci.scheduled_at) as last_interaction_date
                FROM mcp_customers c
                LEFT JOIN mcp_customer_contacts cc ON c.id = cc.customer_id
                LEFT JOIN mcp_customer_interactions ci ON c.id = ci.customer_id
                WHERE 1=1
            """
            
            params = []
            
            if filters:
                if filters.get('customer_type'):
                    query += " AND c.customer_type = %s"
                    params.append(filters['customer_type'])
                
                if filters.get('customer_status'):
                    query += " AND c.customer_status = %s"
                    params.append(filters['customer_status'])
                
                if filters.get('industry'):
                    query += " AND c.industry LIKE %s"
                    params.append(f"%{filters['industry']}%")
                
                if filters.get('company_size'):
                    query += " AND c.company_size = %s"
                    params.append(filters['company_size'])
                
                if filters.get('search'):
                    query += " AND (c.company_name LIKE %s OR c.primary_contact_name LIKE %s OR c.primary_contact_email LIKE %s)"
                    search_term = f"%{filters['search']}%"
                    params.extend([search_term, search_term, search_term])
            
            query += " GROUP BY c.id ORDER BY c.last_interaction_date DESC, c.created_at DESC"
            
            if filters and filters.get('limit'):
                query += f" LIMIT {int(filters['limit'])}"
            
            cursor.execute(query, params)
            customers = cursor.fetchall()
            
            # Process results
            for customer in customers:
                # Convert decimals
                if customer['ai_health_score']:
                    customer['ai_health_score'] = float(customer['ai_health_score'])
                if customer['ai_lifetime_value']:
                    customer['ai_lifetime_value'] = float(customer['ai_lifetime_value'])
                
                # Parse JSON fields
                if customer['custom_fields']:
                    try:
                        customer['custom_fields'] = json.loads(customer['custom_fields'])
                    except:
                        customer['custom_fields'] = {}
                
                if customer['tags']:
                    try:
                        customer['tags'] = json.loads(customer['tags'])
                    except:
                        customer['tags'] = []
            
            return customers
            
        finally:
            cursor.close()
            conn.close()
    
    async def log_interaction(self, interaction_data: Dict[str, Any]) -> str:
        """Log customer interaction"""
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Get customer internal ID
            cursor.execute("SELECT id FROM mcp_customers WHERE customer_id = %s", 
                         (interaction_data['customer_id'],))
            result = cursor.fetchone()
            if not result:
                raise Exception("Customer not found")
            
            customer_internal_id = result[0]
            dubai_now = self.get_dubai_time()
            
            query = """
                INSERT INTO mcp_customer_interactions (
                    customer_id, user_id, interaction_type, subject, description,
                    outcome, scheduled_at, duration_minutes, follow_up_required, follow_up_date
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                customer_internal_id,
                interaction_data['user_id'],
                interaction_data['interaction_type'],
                interaction_data['subject'],
                interaction_data.get('description', ''),
                interaction_data.get('outcome', ''),
                interaction_data.get('scheduled_at', dubai_now),
                interaction_data.get('duration_minutes'),
                interaction_data.get('follow_up_required', False),
                interaction_data.get('follow_up_date')
            )
            
            cursor.execute(query, values)
            interaction_id = cursor.lastrowid
            
            # Update customer's last interaction date
            cursor.execute(
                "UPDATE mcp_customers SET last_interaction_date = %s WHERE customer_id = %s",
                (dubai_now, interaction_data['customer_id'])
            )
            
            conn.commit()
            return str(interaction_id)
            
        except Exception as e:
            conn.rollback()
            raise Exception(f"Failed to log interaction: {str(e)}")
        finally:
            cursor.close()
            conn.close()
    
    async def get_customer_pipeline(self) -> Dict[str, Any]:
        """Get sales pipeline overview"""
        
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Pipeline by customer type
            query = """
                SELECT 
                    customer_type,
                    COUNT(*) as count,
                    AVG(ai_health_score) as avg_health_score,
                    SUM(ai_lifetime_value) as total_value
                FROM mcp_customers
                WHERE customer_status = 'active'
                GROUP BY customer_type
            """
            
            cursor.execute(query)
            pipeline_data = cursor.fetchall()
            
            # Recent activity
            query = """
                SELECT 
                    ci.interaction_type,
                    COUNT(*) as count
                FROM mcp_customer_interactions ci
                WHERE ci.scheduled_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY ci.interaction_type
                ORDER BY count DESC
            """
            
            cursor.execute(query)
            recent_activity = cursor.fetchall()
            
            # Top opportunities
            query = """
                SELECT 
                    c.customer_id,
                    c.company_name,
                    c.ai_health_score,
                    c.ai_lifetime_value,
                    c.last_interaction_date
                FROM mcp_customers c
                WHERE c.customer_type IN ('prospect', 'qualified_lead')
                  AND c.ai_health_score >= 0.7
                ORDER BY c.ai_health_score DESC, c.ai_lifetime_value DESC
                LIMIT 10
            """
            
            cursor.execute(query)
            top_opportunities = cursor.fetchall()
            
            # Process results
            for item in pipeline_data:
                if item['avg_health_score']:
                    item['avg_health_score'] = float(item['avg_health_score'])
                if item['total_value']:
                    item['total_value'] = float(item['total_value'])
            
            for opp in top_opportunities:
                if opp['ai_health_score']:
                    opp['ai_health_score'] = float(opp['ai_health_score'])
                if opp['ai_lifetime_value']:
                    opp['ai_lifetime_value'] = float(opp['ai_lifetime_value'])
            
            return {
                "pipeline_breakdown": {item['customer_type']: item for item in pipeline_data},
                "recent_activity": {item['interaction_type']: item['count'] for item in recent_activity},
                "top_opportunities": top_opportunities,
                "generated_at": self.get_dubai_time().isoformat(),
                "timezone": "Asia/Dubai"
            }
            
        finally:
            cursor.close()
            conn.close()
    
    async def generate_customer_insights(self, customer_id: str) -> Dict[str, Any]:
        """Generate comprehensive AI insights for a specific customer"""
        
        # Get customer data
        customers = await self.get_customers({"search": customer_id})
        if not customers:
            raise Exception("Customer not found")
        
        customer = customers[0]
        
        # Get interaction history
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            query = """
                SELECT * FROM mcp_customer_interactions
                WHERE customer_id = (SELECT id FROM mcp_customers WHERE customer_id = %s)
                ORDER BY scheduled_at DESC
                LIMIT 20
            """
            
            cursor.execute(query, (customer_id,))
            interactions = cursor.fetchall()
            
            # AI analysis
            ai_insights = await self.analyze_customer_ai(customer, interactions)
            
            # Generate recommendations
            recommendations = await self._generate_customer_recommendations(customer, interactions, ai_insights)
            
            return {
                "customer_id": customer_id,
                "customer_name": customer['company_name'],
                "ai_insights": ai_insights,
                "recommendations": recommendations,
                "interaction_summary": {
                    "total_interactions": len(interactions),
                    "last_interaction": interactions[0]['scheduled_at'].isoformat() if interactions else None,
                    "interaction_types": list(set([i['interaction_type'] for i in interactions]))
                },
                "generated_at": self.get_dubai_time().isoformat(),
                "timezone": "Asia/Dubai"
            }
            
        finally:
            cursor.close()
            conn.close()
    
    async def _generate_customer_recommendations(self, customer: Dict, 
                                               interactions: List[Dict], 
                                               ai_insights: Dict) -> List[str]:
        """Generate actionable recommendations for customer management"""
        
        recommendations = []
        
        # Based on lead score
        lead_score = ai_insights.get('lead_score', 50)
        if lead_score >= 80:
            recommendations.append("High-value prospect - schedule proposal meeting within 1 week")
        elif lead_score >= 60:
            recommendations.append("Qualified lead - send case studies and arrange demo")
        else:
            recommendations.append("Nurture lead with valuable content and regular follow-ups")
        
        # Based on interaction frequency
        if not interactions:
            recommendations.append("No recent interactions - reach out immediately")
        elif len(interactions) == 1:
            recommendations.append("Single interaction - schedule follow-up call")
        
        # Based on churn risk
        churn_risk = ai_insights.get('churn_risk', 'low')
        if churn_risk == 'high':
            recommendations.append("HIGH CHURN RISK - immediate attention required")
        elif churn_risk == 'medium':
            recommendations.append("Monitor engagement closely, increase touch points")
        
        # Dubai-specific recommendations
        if customer.get('timezone') == 'Asia/Dubai' or 'dubai' in customer.get('billing_address', '').lower():
            recommendations.append("Consider cultural preferences - relationship-building is key")
            recommendations.append("Schedule face-to-face meeting if possible")
        
        return recommendations