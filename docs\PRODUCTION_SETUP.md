# Leantime Production Deployment Guide for Kanousai

## Email Configuration Options

### Option A: Cloudflare Email Routing (Recommended)
Cloudflare Email Routing allows you to send and receive emails using your domain without managing a mail server.

#### Setup Cloudflare Email Routing:
1. Log into Cloudflare Dashboard
2. Select your domain (kanousai.com)
3. Go to Email > Email Routing
4. Add destination address (where emails will forward to)
5. Create routing <NAME_EMAIL>

#### For Sending Emails via Cloudflare:
**Method 1: Using External SMTP Service (Recommended)**
- Use SendGrid, Mailgun, or Amazon SES
- Configure SMTP credentials in .env

**Method 2: Using Gmail with Custom Domain**
1. Set up Google Workspace or Gmail alias
2. Configure Gmail to <NAME_EMAIL>
3. Generate App Password from Google Account

### Option B: Direct Gmail Configuration
If using Gmail directly:
1. Enable 2-Factor Authentication at https://myaccount.google.com/security
2. Generate App Password at https://myaccount.google.com/apppasswords
3. Use the 16-character password in configuration

## Testing Configuration

### Local Testing (Port 8080)
```bash
# For testing, temporarily modify .env:
LEAN_APP_URL = 'http://localhost:8080'
LEAN_SESSION_SECURE = false

# Start containers
docker compose up -d

# Check logs
docker compose logs -f

# Access at http://localhost:8080
```

## Production Configuration

### Required .env Changes for Production

```env
# Domain Configuration
LEAN_APP_URL = 'https://leantime.kanousai.com'
LEAN_SESSION_SECURE = true

# Email Settings Options

## For SendGrid (Recommended with Cloudflare)
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'smtp.sendgrid.net'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = 'apikey'
LEAN_EMAIL_SMTP_PASSWORD = '[YOUR_SENDGRID_API_KEY]'
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'

## For Mailgun
# LEAN_EMAIL_SMTP_HOSTS = 'smtp.mailgun.org'
# LEAN_EMAIL_SMTP_USERNAME = '<EMAIL>'
# LEAN_EMAIL_SMTP_PASSWORD = '[YOUR_MAILGUN_PASSWORD]'

## For Gmail (if using Google Workspace)
# LEAN_EMAIL_SMTP_HOSTS = 'smtp.gmail.com'
# LEAN_EMAIL_SMTP_USERNAME = '<EMAIL>'
# LEAN_EMAIL_SMTP_PASSWORD = '[YOUR_GMAIL_APP_PASSWORD]'

# Security
LEAN_SESSION_PASSWORD = '[KEEP_GENERATED_SECURE_PASSWORD]'
MYSQL_ROOT_PASSWORD = '[KEEP_GENERATED_SECURE_PASSWORD]'
MYSQL_PASSWORD = '[KEEP_GENERATED_SECURE_PASSWORD]'
LEAN_DB_PASSWORD = '[KEEP_GENERATED_SECURE_PASSWORD]'

# Port (if using reverse proxy)
LEAN_PORT = '8080'  # Internal port, proxy from 443/80
```

## Server Deployment Steps

### 1. Prerequisites on Production Server
- Docker and Docker Compose installed
- SSL certificate configured (Let's Encrypt recommended)
- Nginx or Apache as reverse proxy

### 2. Nginx Reverse Proxy Configuration
Create `/etc/nginx/sites-available/leantime.kanousei.com`:

```nginx
server {
    listen 80;
    server_name leantime.kanousai.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name leantime.kanousai.com;

    ssl_certificate /etc/letsencrypt/live/leantime.kanousai.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/leantime.kanousai.com/privkey.pem;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
    }
}
```

### 3. Deploy to Production Server

```bash
# On production server
git clone https://github.com/Leantime/docker-leantime.git
cd docker-leantime

# Copy your configured .env file
# Make sure to update Gmail app password!

# Start services
docker compose up -d

# Check status
docker compose ps
docker compose logs -f

# Set correct permissions if needed
docker compose exec leantime chown -R www-data:www-data /var/www/html/userfiles
docker compose exec leantime chown -R www-data:www-data /var/www/html/public/userfiles
docker compose exec leantime chown -R www-data:www-data /var/www/html/storage/logs
docker compose exec leantime chown -R www-data:www-data /var/www/html/app/Plugins
```

## Testing Checklist

### Before Production:
- [ ] Test email sending (password reset, notifications)
- [ ] Verify file uploads work
- [ ] Check user registration flow
- [ ] Test project creation
- [ ] Verify timezone settings
- [ ] Check SSL certificate works
- [ ] Test backup and restore procedures

### Security Checklist:
- [ ] Gmail App Password configured (not regular password)
- [ ] Strong database passwords set
- [ ] SSL/HTTPS enabled
- [ ] Firewall configured (only 80/443 open)
- [ ] Regular backups scheduled
- [ ] Monitor disk space for volumes

## Backup Strategy

### Database Backup
```bash
# Backup database
docker compose exec leantime_db mysqldump -u lean -p'[PASSWORD]' leantime > backup_$(date +%Y%m%d).sql

# Restore database
docker compose exec -T leantime_db mysql -u lean -p'[PASSWORD]' leantime < backup.sql
```

### File Backup
```bash
# Backup volumes
docker run --rm -v docker-leantime_userfiles:/data -v $(pwd):/backup alpine tar czf /backup/userfiles_$(date +%Y%m%d).tar.gz -C /data .
docker run --rm -v docker-leantime_public_userfiles:/data -v $(pwd):/backup alpine tar czf /backup/public_userfiles_$(date +%Y%m%d).tar.gz -C /data .
```

## Monitoring

### Health Checks
```bash
# Check container health
docker compose ps

# Check application logs
docker compose logs leantime --tail=50

# Check database logs
docker compose logs leantime_db --tail=50

# Monitor resource usage
docker stats
```

## Troubleshooting

### Email Not Sending

#### For SendGrid:
1. Verify API key is correct and has full send permissions
2. Check sender authentication is configured in SendGrid
3. Ensure <EMAIL> is verified sender
4. Review logs: `docker compose logs leantime | grep -i mail`

#### For Cloudflare Email Routing:
1. Verify email routing rules are active
2. Check DNS records (MX, SPF, DKIM, DMARC)
3. Ensure destination address is configured
4. Check Cloudflare Email dashboard for blocked messages

#### For Gmail:
1. Verify App Password is correct (16 characters, no spaces)
2. Check 2FA is enabled on Gmail account
3. Ensure custom domain is properly configured

### Permission Issues
```bash
docker compose exec leantime chown -R www-data:www-data /var/www/html/userfiles
docker compose exec leantime chmod -R 755 /var/www/html/userfiles
```

### Database Connection Issues
```bash
# Test database connection
docker compose exec leantime_db mysql -u lean -p'[PASSWORD]' -e "SELECT 1"
```

## Support
- Leantime Docs: https://docs.leantime.io
- Cloudflare Email Routing: https://developers.cloudflare.com/email-routing/
- SendGrid Setup: https://docs.sendgrid.com/for-developers/sending-email/quickstart
- Gmail App Passwords: https://support.google.com/accounts/answer/185833