# SendGrid Email Setup for Kanousai.com

## Why SendGrid with Cloudflare?
Cloudflare Email Routing handles **receiving** emails, but for **sending** emails from your application, you need an SMTP service. SendGrid is recommended because:
- Free tier: 100 emails/day
- Excellent deliverability
- Easy API integration
- Works perfectly with Cloudflare domains

## Step-by-Step Setup

### 1. Create SendGrid Account
1. Go to https://signup.sendgrid.com/
2. Sign up for free account
3. Complete email verification

### 2. Sender Authentication
1. In SendGrid Dashboard, go to **Settings > Sender Authentication**
2. Choose **Single Sender Verification** (easier for single domain)
3. Add sender:
   - From Email: `<EMAIL>`
   - From Name: `Kanousai Support`
   - Reply To: `<EMAIL>`
4. Verify the sender email

### 3. Configure Domain Authentication (Optional but Recommended)
1. Go to **Settings > Sender Authentication > Domain Authentication**
2. Add domain: `kanousai.com`
3. SendGrid will provide DNS records
4. Add these records in Cloudflare:
   ```
   Type: CNAME
   Name: em[numbers].kanousai.com
   Content: u[numbers].wl[numbers].sendgrid.net
   
   Type: CNAME
   Name: s1._domainkey
   Content: s1.domainkey.u[numbers].wl[numbers].sendgrid.net
   
   Type: CNAME
   Name: s2._domainkey
   Content: s2.domainkey.u[numbers].wl[numbers].sendgrid.net
   ```
5. Verify in SendGrid after DNS propagation

### 4. Generate API Key
1. Go to **Settings > API Keys**
2. Click **Create API Key**
3. Name: `Leantime SMTP`
4. Permissions: **Full Access** (or Restricted with Mail Send)
5. Copy the API key immediately (shown only once!)

### 5. Update Leantime Configuration

Edit your `.env` file:

```env
# Email Configuration for SendGrid
LEAN_EMAIL_RETURN = '<EMAIL>'
LEAN_EMAIL_USE_SMTP = true
LEAN_EMAIL_SMTP_HOSTS = 'smtp.sendgrid.net'
LEAN_EMAIL_SMTP_AUTH = true
LEAN_EMAIL_SMTP_USERNAME = 'apikey'  # This is literally 'apikey', not your email
LEAN_EMAIL_SMTP_PASSWORD = 'SG.xxxxxxxxxxxxxxxxxxxxx'  # Your SendGrid API key
LEAN_EMAIL_SMTP_SECURE = 'TLS'
LEAN_EMAIL_SMTP_PORT = '587'
```

**Important Notes:**
- Username is always `apikey` (literal string)
- Password is your SendGrid API key
- Port 587 with TLS is recommended

### 6. Alternative Ports (if 587 is blocked)
```env
# Option 2: Port 25 (unencrypted)
LEAN_EMAIL_SMTP_PORT = '25'
LEAN_EMAIL_SMTP_SECURE = ''

# Option 3: Port 465 (SSL)
LEAN_EMAIL_SMTP_PORT = '465'
LEAN_EMAIL_SMTP_SECURE = 'SSL'

# Option 4: Port 2525 (alternative)
LEAN_EMAIL_SMTP_PORT = '2525'
LEAN_EMAIL_SMTP_SECURE = 'TLS'
```

## Cloudflare DNS Configuration

Ensure these DNS records exist in Cloudflare for kanousai.com:

### MX Records (for receiving email)
```
Type: MX
Name: @
Mail server: route1.mx.cloudflare.net
Priority: 1

Type: MX
Name: @
Mail server: route2.mx.cloudflare.net
Priority: 2
```

### SPF Record (authorize SendGrid to send)
```
Type: TXT
Name: @
Content: v=spf1 include:sendgrid.net include:_spf.mx.cloudflare.net ~all
```

### DMARC Record (email authentication)
```
Type: TXT
Name: _dmarc
Content: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>
```

## Testing Email Configuration

### Test in Leantime
1. Start Docker containers:
   ```bash
   docker compose up -d
   ```

2. Access Leantime at http://localhost:8080

3. Test email functionality:
   - Password reset
   - User invitation
   - Notifications

### Test with Docker
```bash
# Check if email settings are loaded
docker compose exec leantime printenv | grep LEAN_EMAIL

# Check logs for email errors
docker compose logs leantime | grep -i mail
docker compose logs leantime | grep -i smtp
```

### Test SendGrid Connection
```bash
# Test SMTP connection from container
docker compose exec leantime sh -c "apt-get update && apt-get install -y telnet && telnet smtp.sendgrid.net 587"
# Type: QUIT to exit
```

## Monitoring & Limits

### SendGrid Free Tier Limits
- 100 emails per day
- Resets daily at midnight UTC

### Monitor Usage
1. SendGrid Dashboard > Activity
2. Check email delivery status
3. Review bounce/spam reports

### Upgrade Options
- Essentials Plan: 50,000 emails/month for $19.95
- Pro Plan: 100,000 emails/month + advanced features

## Troubleshooting

### Common Issues

1. **"Authentication failed"**
   - Verify API key is correct
   - Ensure username is 'apikey' (not your email)
   - Check API key has Mail Send permission

2. **"Connection refused"**
   - Check firewall allows outbound 587
   - Try alternative ports (25, 465, 2525)
   - Verify Docker network settings

3. **"Sender not verified"**
   - Complete sender verification in SendGrid
   - Wait for DNS propagation (up to 48 hours)
   - Check SPF/DKIM records in Cloudflare

4. **Emails going to spam**
   - Complete domain authentication
   - Set up proper SPF, DKIM, DMARC records
   - Avoid spam trigger words in content

### Debug Commands
```bash
# Test from inside container
docker compose exec leantime php -r "
\$transport = (new Swift_SmtpTransport('smtp.sendgrid.net', 587, 'tls'))
  ->setUsername('apikey')
  ->setPassword('YOUR_API_KEY');
\$mailer = new Swift_Mailer(\$transport);
\$message = (new Swift_Message('Test'))
  ->setFrom(['<EMAIL>' => 'Test'])
  ->setTo(['<EMAIL>'])
  ->setBody('Test email');
\$result = \$mailer->send(\$message);
echo 'Sent: ' . \$result . PHP_EOL;
"
```

## Security Best Practices

1. **Never commit API keys** to version control
2. **Use environment variables** for sensitive data
3. **Rotate API keys** regularly
4. **Monitor SendGrid** for unusual activity
5. **Set up alerts** for quota usage
6. **Use IP whitelisting** if available

## Support Resources

- SendGrid Docs: https://docs.sendgrid.com/
- SendGrid Status: https://status.sendgrid.com/
- Cloudflare Email Routing: https://developers.cloudflare.com/email-routing/
- Leantime Email Config: https://docs.leantime.io/administration/email