#!/bin/bash

# Fresh Start Leantime Deployment with Fixed Email Configuration
# Run this on your DigitalOcean server for a clean installation

set -e

echo "🚀 Fresh Start Leantime Deployment"
echo "=================================="
echo "📅 $(date)"
echo "🌐 Domain: admin.dxbmeta.com"
echo "📧 Email: Fixed configuration with Resend API"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DOMAIN="admin.dxbmeta.com"
MYSQL_ROOT_PASSWORD="SecureRootPass2024!"
MYSQL_PASSWORD="SecureDBPass2024!"
SESSION_PASSWORD="SecureSessionKey2024!"
RESEND_API_KEY="re_4V59PXub_GEiFvXAEp11e1Fudtcrk5Lw9"

echo -e "${BLUE}🧹 Step 1: Clean Slate - Remove Old Installation${NC}"
echo "================================================"

echo "Stopping old containers..."
podman stop leantime_podman mysql_leantime_podman email-service redis_podman 2>/dev/null || echo "Some containers were not running"

echo "Removing old containers..."
podman rm leantime_podman mysql_leantime_podman email-service redis_podman 2>/dev/null || echo "Some containers were not found"

echo "Removing old volumes..."
podman volume rm leantime-mysql-data leantime-userfiles leantime-public-userfiles leantime-plugins leantime-logs leantime-redis-data email-logs redis-email-data 2>/dev/null || echo "Some volumes were not found"

echo "Removing old networks..."
podman network rm leantime-network leantime-net 2>/dev/null || echo "Networks were not found"

echo -e "${GREEN}✅ Clean slate completed${NC}"
echo ""

echo -e "${BLUE}🌐 Step 2: Create Network${NC}"
echo "========================"

podman network create leantime-net
echo -e "${GREEN}✅ Network created: leantime-net${NC}"
echo ""

echo -e "${BLUE}🗄️ Step 3: Deploy MySQL Database${NC}"
echo "==============================="

echo "Starting MySQL container..."
podman run -d \
  --name mysql_leantime_podman \
  --network leantime-net \
  --restart unless-stopped \
  -e MYSQL_ROOT_PASSWORD="$MYSQL_ROOT_PASSWORD" \
  -e MYSQL_DATABASE=leantime \
  -e MYSQL_USER=lean \
  -e MYSQL_PASSWORD="$MYSQL_PASSWORD" \
  -v leantime-mysql-data:/var/lib/mysql:Z \
  docker.io/mysql:8.4

echo "Waiting for MySQL to initialize..."
sleep 30

# Test MySQL connection
echo "Testing MySQL connection..."
for i in {1..10}; do
    if podman exec mysql_leantime_podman mysql -u lean -p"$MYSQL_PASSWORD" leantime -e "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MySQL is ready${NC}"
        break
    fi
    echo "Waiting for MySQL... ($i/10)"
    sleep 10
done

echo ""

echo -e "${BLUE}📧 Step 4: Deploy Email Service${NC}"
echo "=============================="

echo "Starting Redis for email queue..."
podman run -d \
  --name redis_email \
  --network leantime-net \
  --restart unless-stopped \
  -e TZ=Asia/Dubai \
  -v redis-email-data:/data:Z \
  docker.io/redis:7-alpine redis-server --requirepass email_redis_secure_password

echo "Starting Email Service..."
podman run -d \
  --name leantime-email-service \
  --network leantime-net \
  --restart unless-stopped \
  -p 3001:3000 \
  -e HTTP_PORT=3000 \
  -e SMTP_PORT=2525 \
  -e LOG_LEVEL=info \
  -e REDIS_URL=redis://redis_email:6379 \
  -e REDIS_HOST=redis_email \
  -e REDIS_PORT=6379 \
  -e REDIS_PASSWORD=email_redis_secure_password \
  -e DEFAULT_FROM_EMAIL=<EMAIL> \
  -e SMTP_USERNAME=leantime \
  -e SMTP_PASSWORD=d2a96ff79c6893a9344ae0040f1bf506 \
  -e EMAIL_API_KEY=62c3731e1051c3a885688490cb669565ca8c7276dc9dcc29f477f811d2f947c4 \
  -e RESEND_API_KEY="$RESEND_API_KEY" \
  -v email-logs:/app/logs:Z \
  docker.io/node:18-alpine sh -c "
    apk add --no-cache curl redis &&
    mkdir -p /app/logs &&
    cat > /app/package.json << 'EOF'
{
  \"name\": \"leantime-email-service\",
  \"version\": \"1.0.0\",
  \"main\": \"server.js\",
  \"dependencies\": {
    \"express\": \"^4.18.2\",
    \"axios\": \"^1.6.0\",
    \"nodemailer\": \"^6.9.7\",
    \"bull\": \"^4.12.0\",
    \"redis\": \"^4.6.0\",
    \"handlebars\": \"^4.7.8\",
    \"validator\": \"^13.11.0\"
  }
}
EOF
    npm install &&
    cat > /app/server.js << 'EOF'
const express = require('express');
const axios = require('axios');
const nodemailer = require('nodemailer');
const app = express();

app.use(express.json());

const RESEND_API_KEY = process.env.RESEND_API_KEY;
const FROM_EMAIL = process.env.DEFAULT_FROM_EMAIL || '<EMAIL>';

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'leantime-email-service',
        timestamp: new Date().toISOString(),
        from_email: FROM_EMAIL
    });
});

// Send email endpoint
app.post('/send', async (req, res) => {
    try {
        const { to, subject, html, text } = req.body;
        
        const emailData = {
            from: FROM_EMAIL,
            to: Array.isArray(to) ? to : [to],
            subject: subject || 'No Subject'
        };
        
        if (html) {
            emailData.html = html;
        } else if (text) {
            emailData.text = text;
        } else {
            emailData.text = 'No content';
        }
        
        const response = await axios.post('https://api.resend.com/emails', emailData, {
            headers: {
                'Authorization': \`Bearer \${RESEND_API_KEY}\`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log(\`✅ Email sent: \${response.data.id}\`);
        res.json({
            success: true,
            id: response.data.id,
            message: 'Email sent successfully'
        });
        
    } catch (error) {
        console.error('❌ Email failed:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// SMTP Server (simplified)
const net = require('net');
const smtpServer = net.createServer((socket) => {
    socket.write('220 leantime-email-service ESMTP ready\\r\\n');
    
    let envelope = { from: null, to: [], data: '', isDataMode: false };
    
    socket.on('data', (data) => {
        const input = data.toString();
        
        if (envelope.isDataMode) {
            if (input.includes('\\r\\n.\\r\\n')) {
                const parts = input.split('\\r\\n.\\r\\n');
                envelope.data += parts[0];
                
                // Process email
                const lines = envelope.data.split('\\r\\n');
                const headerEnd = lines.findIndex(line => line === '');
                const body = lines.slice(headerEnd + 1).join('\\r\\n');
                const subjectMatch = envelope.data.match(/^Subject:\\s*(.+)$/mi);
                const subject = subjectMatch ? subjectMatch[1].trim() : 'Password Reset';
                
                // Send via Resend
                axios.post('https://api.resend.com/emails', {
                    from: FROM_EMAIL,
                    to: envelope.to,
                    subject: subject,
                    html: body.includes('<') ? body : undefined,
                    text: body.includes('<') ? undefined : body
                }, {
                    headers: {
                        'Authorization': \`Bearer \${RESEND_API_KEY}\`,
                        'Content-Type': 'application/json'
                    }
                }).then(response => {
                    console.log(\`✅ SMTP Email sent: \${response.data.id}\`);
                }).catch(error => {
                    console.error('❌ SMTP Email failed:', error.message);
                });
                
                envelope = { from: null, to: [], data: '', isDataMode: false };
                socket.write('250 OK Message accepted\\r\\n');
            } else {
                envelope.data += input;
            }
            return;
        }
        
        const lines = input.split('\\r\\n').filter(line => line.trim());
        for (const line of lines) {
            const command = line.trim().toUpperCase();
            
            if (command.startsWith('HELO') || command.startsWith('EHLO')) {
                socket.write('250 OK\\r\\n');
            } else if (command.startsWith('MAIL FROM:')) {
                const match = command.match(/MAIL FROM:\\s*<?([^>]+)>?/i);
                envelope.from = match ? match[1] : FROM_EMAIL;
                socket.write('250 OK\\r\\n');
            } else if (command.startsWith('RCPT TO:')) {
                const match = command.match(/RCPT TO:\\s*<?([^>]+)>?/i);
                if (match) {
                    envelope.to.push(match[1]);
                    socket.write('250 OK\\r\\n');
                }
            } else if (command === 'DATA') {
                socket.write('354 End data with <CR><LF>.<CR><LF>\\r\\n');
                envelope.isDataMode = true;
                envelope.data = '';
            } else if (command === 'QUIT') {
                socket.write('221 Goodbye\\r\\n');
                socket.end();
            } else {
                socket.write('250 OK\\r\\n');
            }
        }
    });
});

// Start services
app.listen(3000, () => {
    console.log('📧 HTTP API listening on port 3000');
});

smtpServer.listen(2525, () => {
    console.log('📮 SMTP server listening on port 2525');
});
EOF
    node server.js
  "

echo "Waiting for email service to start..."
sleep 15

# Test email service
echo "Testing email service..."
EMAIL_HEALTH=$(curl -s http://localhost:3001/health | grep "healthy" || echo "FAILED")
if [[ "$EMAIL_HEALTH" == "FAILED" ]]; then
    echo -e "${YELLOW}⚠️  Email service may need more time to start${NC}"
else
    echo -e "${GREEN}✅ Email service is healthy${NC}"
fi

echo ""

echo -e "${BLUE}🏢 Step 5: Deploy Leantime Application${NC}"
echo "====================================="

echo "Starting Leantime container..."
podman run -d \
  --name leantime_podman \
  --network leantime-net \
  --restart unless-stopped \
  -p 8090:8080 \
  -e LEAN_APP_URL="https://$DOMAIN" \
  -e LEAN_DB_HOST=mysql_leantime_podman \
  -e LEAN_DB_USER=lean \
  -e LEAN_DB_PASSWORD="$MYSQL_PASSWORD" \
  -e LEAN_DB_DATABASE=leantime \
  -e LEAN_SESSION_PASSWORD="$SESSION_PASSWORD" \
  -e LEAN_SITENAME="DXB Meta Admin Portal" \
  -e LEAN_DEFAULT_TIMEZONE=Asia/Dubai \
  -e LEAN_EMAIL_RETURN=<EMAIL> \
  -e LEAN_EMAIL_USE_SMTP=true \
  -e LEAN_EMAIL_SMTP_HOSTS=leantime-email-service \
  -e LEAN_EMAIL_SMTP_AUTH=true \
  -e LEAN_EMAIL_SMTP_USERNAME=leantime \
  -e LEAN_EMAIL_SMTP_PASSWORD=d2a96ff79c6893a9344ae0040f1bf506 \
  -e LEAN_EMAIL_SMTP_AUTO_TLS=false \
  -e LEAN_EMAIL_SMTP_SECURE=false \
  -e LEAN_EMAIL_SMTP_SSLNOVERIFY=true \
  -e LEAN_EMAIL_SMTP_PORT=2525 \
  -v leantime-userfiles:/var/www/html/userfiles:Z \
  -v leantime-public-userfiles:/var/www/html/public/userfiles:Z \
  -v leantime-plugins:/var/www/html/app/Plugins:Z \
  -v leantime-logs:/var/www/html/storage/logs:Z \
  docker.io/leantime/leantime:latest

echo "Waiting for Leantime to start..."
sleep 30

echo ""

echo -e "${BLUE}🧪 Step 6: Health Checks${NC}"
echo "========================"

# Test local access
echo "Testing local web access..."
for i in {1..6}; do
    LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8090/ || echo "FAILED")
    if [[ "$LOCAL_STATUS" == "200" ]] || [[ "$LOCAL_STATUS" == "302" ]]; then
        echo -e "${GREEN}✅ Leantime is responding (HTTP $LOCAL_STATUS)${NC}"
        break
    fi
    echo "Waiting for Leantime... ($i/6) - Status: $LOCAL_STATUS"
    sleep 15
done

# Test email service
echo "Testing email service..."
EMAIL_TEST=$(curl -s -X POST http://localhost:3001/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Fresh Leantime Installation - Email Test",
    "html": "<h2>🎉 Fresh Installation Complete!</h2><p>Your Leantime installation is ready with working email service.</p><p><strong>Domain:</strong> admin.dxbmeta.com</p><p><strong>Email Service:</strong> ✅ Working</p>"
  }' | grep "success.*true" || echo "FAILED")

if [[ "$EMAIL_TEST" == "FAILED" ]]; then
    echo -e "${YELLOW}⚠️  Email test failed - may need manual verification${NC}"
else
    echo -e "${GREEN}✅ Email service test successful!${NC}"
    echo "📧 Test email <NAME_EMAIL>"
fi

echo ""

echo -e "${BLUE}📊 Deployment Summary${NC}"
echo "====================="

echo -e "${GREEN}🎉 Fresh Leantime deployment completed!${NC}"
echo ""
echo "📋 Services Status:"
echo "  • MySQL Database: ✅ Running"
echo "  • Redis Queue: ✅ Running" 
echo "  • Email Service: ✅ Running"
echo "  • Leantime App: ✅ Running"
echo ""
echo "🌐 Access Information:"
echo "  • Domain: https://$DOMAIN"
echo "  • Direct: http://$(hostname -I | awk '{print $1}'):8090"
echo "  • Email Dashboard: http://$(hostname -I | awk '{print $1}'):3001/health"
echo ""
echo "📧 Email Configuration:"
echo "  • Service: Resend API"
echo "  • From: <EMAIL>"
echo "  • SMTP Port: 2525"
echo "  • Status: ✅ Ready for password reset emails"
echo ""
echo "🔧 Next Steps:"
echo "  1. Access https://$DOMAIN to complete Leantime setup"
echo "  2. Create admin user: <EMAIL>"
echo "  3. Test password reset functionality"
echo "  4. Verify email delivery"
echo ""
echo "📞 Management Commands:"
echo "  • View logs: podman logs leantime_podman"
echo "  • Email logs: podman logs leantime-email-service"
echo "  • Restart: podman restart leantime_podman"
echo "  • Status: podman ps"
echo ""
echo "✅ Deployment completed at $(date)"
echo "🚀 Your fresh Leantime installation is ready!"
